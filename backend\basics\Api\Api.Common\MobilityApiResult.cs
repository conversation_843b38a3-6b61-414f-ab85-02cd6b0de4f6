﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Common
{
    /// <summary>
    /// 
    /// </summary>
    public class MobilityApiResult
    {
        /// <summary>
        /// 
        /// </summary>
        public int ErrorCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ErrorDetail { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ErrorMessage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Exception Exception { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public object Result { get; set; }
    }
}
