﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{E51CDDC2-4293-4083-B453-69C51C4F91B2}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RIB.Visual.Basics.AccountingJournals.ServiceFacade.WebApi</RootNamespace>
    <AssemblyName>RIB.Visual.Basics.AccountingJournals.ServiceFacade.WebApi</AssemblyName>
    <TargetFramework>net8.0</TargetFramework>
    <FileAlignment>512</FileAlignment>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation />
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\RIB.Visual.Platform.ServiceFacade.WebApi.XML</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Platform.AppServer.Web">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.ServiceFacade.WebApi">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Company.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Company.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.AppServer.Runtime">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Company.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Company.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.ServiceFacade.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Mvc.WebApiCompatShim">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Microsoft.AspNetCore.Mvc.WebApiCompatShim.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Formatting">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Net.Http.Formatting.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.CodeDom">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.CodeDom.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Configuration.ConfigurationManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.SqlClient">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Data.SqlClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Common">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Drawing.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{B4F97281-0DBD-4835-9ED8-7DFB966E87FF}" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Controllers\AccountingJournalsController.cs" />
    <Compile Include="Dtos\AccountingJournalsCompleteDto.cs" />
    <Compile Include="Dtos\AccountingJournalsDto.cs" />
    <Compile Include="Dtos\CompanyTransHeaderStatusHistoryDto.cs" />
    <Compile Include="Dtos\CompanyTransHeaderStatusHistoryDto.Generated.cs">
      <DependentUpon>CompanyTransHeaderStatusHistoryDto.cs</DependentUpon>
    </Compile>
    <Compile Include="Dtos\CompanyTransHeaderVDto.cs" />
    <Compile Include="Dtos\CompanyTransHeaderVDto.Generated.cs">
      <DependentUpon>CompanyTransHeaderVDto.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="../../../AssemblyVersion.cs" Link="Properties/AssemblyVersion.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="RIBvisual.snk" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Scripts\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AccountingJournals.BusinessComponents\RIB.Visual.Basics.AccountingJournals.BusinessComponents.csproj">
      <Project>{82E922C9-0BA9-4603-A332-08E73541434B}</Project>
      <Name>RIB.Visual.Basics.AccountingJournals.BusinessComponents</Name>
    </ProjectReference>
    <ProjectReference Include="..\AccountingJournals.Common\RIB.Visual.Basics.AccountingJournals.Common.csproj">
      <Project>{8E7DAD1C-868F-4936-B235-7873223F7FDF}</Project>
      <Name>RIB.Visual.Basics.AccountingJournals.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\AccountingJournals.Core\RIB.Visual.Basics.AccountingJournals.Core.csproj">
      <Project>{BC408BF6-A751-4E17-87F7-706A8A06CF44}</Project>
      <Name>RIB.Visual.Basics.AccountingJournals.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\AccountingJournals.Localization\RIB.Visual.Basics.AccountingJournals.Localization.csproj">
      <Project>{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}</Project>
      <Name>RIB.Visual.Basics.AccountingJournals.Localization</Name>
    </ProjectReference>
  </ItemGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F" />
  </Target>
</Project>