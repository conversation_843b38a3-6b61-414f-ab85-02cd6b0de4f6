
/*
 * $Id$
 * Copyright (c) RIB Software AG
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Web.Http;
using System.Linq;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using RIB.Visual.Basics.AssetMaster.BusinessComponents;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Basics.LookupData.ServiceFacade.WebApi;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.Common.ServiceFacade.WebApi.Final;
using RIB.Visual.Basics.Common.ServiceFacade.WebApi.Converter;

namespace RIB.Visual.Basics.AssetMaster.ServiceFacade.WebApi
{
	/// <summary>
	/// Please add a comment here and change RoutePrefix and Name of controller to your module's and submodule's name
	/// </summary>
	[RoutePrefix("basics/assetmaster")]
	public class BasicsAssetMasterController : EntityUpdateController<AssetMasterLogic,<PERSON><PERSON><PERSON>aster<PERSON>to,AssetMasterEntity,int>
	{
		/// <summary>
		/// 
		/// </summary>
		private const string PermissionDescriptor = "3c17e18947514d48ab6417ed2d991f63";

		/// <summary>
		/// Get asset master Tree according to master data context id
		/// </summary>
		/// <returns></returns>
		[Route("tree")]
		[HttpPost]
		[Permission(Permissions.Read, PermissionDescriptor)]
		public Dictionary<string, object> GetTree(FilterRequest filterRequest)
		{
			var filterResult = new FilterResponse();
			var execInfo = new FilterExecutionInfo(filterRequest, filterResult);
			execInfo.CreateHint("Start fetching of data by:'" + filterRequest.Pattern + "'");
			ConditionDtoToBulkExpressionConverter.EvaluateEnhancedFilter(filterRequest);
			var entities = Logic.GetTreeBySearchFilter(filterRequest, out filterResult);
			execInfo.CreateHint(string.Format("fetched data: '{0}/{1}' records", filterResult.RecordsRetrieved, filterResult.RecordsFound));
			Dictionary<string, object> jsonData = new Dictionary<string, object>();
			if (entities != null)
			{
				jsonData["Main"] = entities.ToDtos(e => new AssetMasterDto(e));
			}
			else
			{
				jsonData["Main"] = null;
			}
			jsonData.Merge("FilterResult", filterResult);
			execInfo.CreateHint(string.Format("completed data fetching ..."));
			return jsonData;
		}

		/// <summary>
		/// the identifier of the costcode new created locations belong to
		/// </summary>
		public class AssetMasterCreationData
		{
			/// <summary>
			/// Get / set the Id of the parent asset master id
			/// </summary>
			public int parentId { get; set; }
		}

		/// <summary>
		/// get new procurement structure 
		/// </summary>
		/// <returns></returns>
		[Route("createdata")]
		[HttpPost]
		[Permission(Permissions.Create, PermissionDescriptor)]
		public AssetMasterDto Create(AssetMasterCreationData data)
		{
			var entity = Logic.CreateEntity(data.parentId);
			var dto = new AssetMasterDto(entity);
			return dto;
		}

		/// <summary>
		/// GET By Id
		/// </summary>
		/// <returns></returns>
		[Route("get")]
		[HttpGet]
		public AssetMasterDto GetItemById(int? id)
		{
			var entity = Logic.GetItemByKey(id);
			return entity == null
				? null
				: new AssetMasterDto(entity);
		}

		/// <summary>
		/// get the json validation schema of AssetMaster entity.
		/// </summary>
		/// <returns>a json validation schema of AssetMaster entity.</returns>
		[Route("schema")]
		[AllowAnonymous]
		public HttpResponseMessage GetSchema()
		{
			return GetDotNetJsonSchema(typeof(AssetMasterDto));
		}

		/// <summary>
		/// 
		/// </summary>
		public class AssetMasterUpdateData
		{
			/// <summary>
			/// Get / set the Id of the parent asset master id
			/// </summary>
			public IEnumerable<AssetMasterDto> AssetMaster { get; set; }

			///// <summary>
			///// 
			///// </summary>
			//public IEnumerable<AssetMasterDto> AssetMasterList { get; set; }

			///// <summary>
			///// for batch disable/enable records
			///// </summary>
			//public IEnumerable<AssetMasterDto> AssetMasters { get; set; }
		}

		/// <summary>
		/// Delete an existing Asset Master
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("deletedata")]
		[Permission(Permissions.Delete, PermissionDescriptor)]
        public void DeleteTree(IEnumerable<AssetMasterDto> dtos)
		{
            Logic.Delete(dtos.ToEntities(e=>e.Copy()));
		}

		/// <summary>
		/// Update costcodes
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("updatedata")]
		//[Permission(Permissions.Read | Permissions.Write | Permissions.Delete, PermissionDescriptor)] // removed the permission control for defect 113290 to allow update for sub containers .
		public AssetMasterUpdateData Update(AssetMasterUpdateData dto)
		{
			AssetMasterUpdateData updateResult = new AssetMasterUpdateData();
			//AssetMasters is for batch disable/enable records
			//if (dto.AssetMasters != null)
			//{
			//	updateResult.AssetMasterList = Logic.SaveEntitiesTransactional(dto.AssetMasters.Select(e => e.Copy()).ToList()).Select(e => new AssetMasterDto(e));
			//}
			//else
			//{
			//	updateResult.AssetMasterList = Logic.SaveEntityTransactional(dto.AssetMaster.Copy()).Select(e => new AssetMasterDto(e));
			//}
			//updateResult.AssetMaster = new AssetMasterDto(Logic.SaveEntityTransactional(dto.AssetMaster.Copy()));
			updateResult.AssetMaster = Logic.SaveEntitiesTransactional(dto.AssetMaster.Select(e => e.Copy()).ToList()).Select(e => new AssetMasterDto(e));

			return updateResult;
		}

		/// <summary>
		/// Check the code is unique or not
		/// </summary>
		/// <param name="id"></param>
		/// <param name="code"></param>
		/// <returns></returns>
		[Route("isunique")]
		[HttpGet]
		public bool IsUnique(int id, string code)
		{
			var mdcContextId = Logic.GetCompanyInfoProvider().GetMasterDataContext();
			return ValidationHelper.IsUnique<AssetMasterEntity>(RIB.Visual.Basics.AssetMaster.BusinessComponents.ModelBuilder.DbModel, e => e.Id != id && e.Code == code && e.MdcContextFk == mdcContextId);
		}


		/// <summary>
		/// Get asset master Tree according to master data context id
		/// </summary>
		/// <returns></returns>
		[Route("lookuptree")]
		[HttpGet]
		public IEnumerable<AssetMasterDto> GetTree()
		{
			try
			{
				return Logic.GetAssetMasterLookupTree().Select(e => new AssetMasterDto(e));
			}
			catch
			{
				throw;
			}
		}
	}
}
