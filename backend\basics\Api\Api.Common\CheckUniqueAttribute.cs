﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Check the collection if unique or not.
	/// </summary>
	[AttributeUsage(AttributeTargets.Property)]
	public class CheckUniqueAttribute : ValidationAttribute
	{
		/// <summary>
		/// Contructor.
		/// </summary>
		public CheckUniqueAttribute()
		{
		}

		/// <summary>
		/// Determines whether a specified object is valid.
		/// </summary>
		/// <param name="value">The object to validate.</param>
		/// <param name="validationContext">The validation context</param>
		/// <returns></returns>
		protected override ValidationResult IsValid(object value, ValidationContext validationContext)
		{
			ValidationResult result = ValidationResult.Success;

			if (validationContext.ObjectInstance != null)
			{
				if (value != null && value is IEnumerable)
				{
					var collection = (value as IEnumerable).AsQueryable();
					var properties = collection.ElementType.GetProperties(System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.SetProperty | System.Reflection.BindingFlags.GetProperty);

					var keyProperties = new List<PropertyInfo>();
					var hintPropertesMap = new Dictionary<PropertyInfo, short>();

					if (properties != null)
					{
						foreach (var property in properties)
						{
							var compositeKey = property.GetCustomAttribute<CompositeUniqueKeyAttribute>();
							if (compositeKey != null)
							{
								keyProperties.Add(property);
							}

							var hint = property.GetCustomAttribute<UniqueHintAttribute>();
							if (hint != null)
							{
								hintPropertesMap.Add(property, hint.Order);
							}
						}
					}

					if (keyProperties.Any())
					{
						HashSet<string> compositeKeys = new HashSet<string>();
						int index = 0;
						List<string> keyLog = new List<string>();
						List<string> hintLog = new List<string>();
						IEnumerable<PropertyInfo> hintProperties = hintPropertesMap.OrderBy(e => e.Value).Select(e => e.Key).ToList();

						foreach (var item in collection)
						{
							string keyValue = null;
							foreach (var key in keyProperties)
							{
								var temp = key.GetValue(item);
								keyValue += "_" + temp;
								keyLog.Add(string.Format("{0}({1})", key.Name, temp)); //property name/value
							}

							foreach (var hint in hintProperties)
							{
								var temp = hint.GetValue(item);
								hintLog.Add(string.Format("{0}({1})", hint.Name, temp)); // property name/value
							}

							if (!compositeKeys.Contains(keyValue))
							{
								compositeKeys.Add(keyValue);
							}
							else
							{
								string errorMsg = string.Format("{0}[{1}].{2}", validationContext.MemberName, index, keyLog[0]);

								for (int i = 1; i < keyLog.Count(); i++)
								{
									errorMsg += string.Format(", {0}", keyLog[i]);
								}

								errorMsg += " must be unique.";

								if (hintLog.Any())
								{
									errorMsg += " Additional information: ";
									var count = 1;
									foreach (var hint in hintLog)
									{
										if (count++ == 1)
										{
											errorMsg += string.Format("{0}", hint);
										}
										else
										{
											errorMsg += string.Format(", {0}", hint);
										}
									}
									errorMsg = errorMsg + ".";
								}

								result = new ValidationResult(errorMsg, new List<string>() { validationContext.MemberName });
								break;
							}
							index++;
							keyLog.Clear();
							hintLog.Clear();
						}
					}
				}
			}

			return result;
		}
	}
}
