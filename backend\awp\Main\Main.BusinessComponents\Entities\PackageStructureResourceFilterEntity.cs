using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Platform.Common;

namespace RIB.Visual.Awp.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class PackageStructureResourceFilterEntity
	{
		/// <summary>
		/// 
		/// </summary>
		public int Id { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? ParentFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string Code { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool Selected {  get; set; }

		/// <summary>
		/// 
		/// </summary>
		public DescriptionTranslateType DescriptionInfo { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public List<int> MdcCostCodeIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public List<int> ProjectCostCodeIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public List<int> MdcMaterialIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public ICollection<PackageStructureResourceFilterEntity> PackageStructureResourceFilter { get; set; }
	}
}
