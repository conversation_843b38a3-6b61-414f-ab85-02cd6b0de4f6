﻿using RIB.Visual.Basics.Core.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Represent a string or translation property which can be truncated if the length of value exceed the max length.
	/// </summary>
	[AttributeUsage(AttributeTargets.Property)]
	public class TruncateAttribute : Attribute
	{
		/// <summary>
		/// Contructor.
		/// </summary>
		/// <param name="maxLength">The allowed max length of string or translation property</param>
		public TruncateAttribute(int maxLength)
		{
			MaxLength = maxLength;
		}

		/// <summary>
		/// Gets or sets MaxLength.
		/// </summary>
		public int MaxLength
		{
			get;
			set;
		}
	}
}
