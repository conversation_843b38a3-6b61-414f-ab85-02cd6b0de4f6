using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.AppServer.Runtime;

namespace RIB.Visual.Awp.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class AwpMainPackageItemAssignmentHelper
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="filterData"></param>
		/// <returns></returns>
		public FilterDataConversionEntity GetFilterLineItemAndResoure(FilterData filterData)
		{
			var filterDataConversionEntity = new FilterDataConversionEntity();
			List<RIB.Visual.Platform.Core.IdentificationData> lineItemIdData = new List<RIB.Visual.Platform.Core.IdentificationData>();

			if (filterData.GroupingStructureNodes != null)
			{
				var lineItemStructureNode = filterData.GroupingStructureNodes.Where(e => e.GroupingType == PackageStructureLineItemGroupingType.LineItemCode);

				if (lineItemStructureNode.Any())
				{
					lineItemIdData.AddRange(lineItemStructureNode.Select(e => new RIB.Visual.Platform.Core.IdentificationData() { Id = e.EntityId, PKey1 = e.EntityHeaderFK }).ToList());
				}

				filterDataConversionEntity.LineItemIdData = lineItemIdData;
			}

			if (filterData.LineItemResourceIds != null && filterData.LineItemResourceIds.Any())
			{
				filterDataConversionEntity.ResourceIdData = filterData.LineItemResourceIds.Select(e => new RIB.Visual.Platform.Core.IdentificationData() { Id = e.Item1, PKey1 = e.Item2, PKey2 = e.Item3 }).ToList();
			}

			return filterDataConversionEntity;
		}
	}
}
