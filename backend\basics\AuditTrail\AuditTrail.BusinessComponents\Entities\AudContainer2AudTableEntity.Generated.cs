//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.AuditTrail.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Basics.AuditTrail.BusinessComponents.AudContainer2AudTableEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("AUD_CONTAINER2AUD_TABLE")]
    public partial class AudContainer2AudTableEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new AudContainer2AudTableEntity object.
        /// </summary>
        public AudContainer2AudTableEntity()
        {
            OnConstruct(); // call partial constructor if present             
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for AudCointainerFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AUD_COINTAINER_FK", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int AudCointainerFk
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for AudTableFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AUD_TABLE_FK", TypeName = "int", Order = 1)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int AudTableFk
        {
            get;
            set;
        }


        #endregion

        #region Navigation Properties
    
        /// <summary>
        /// There are no comments for AudContainerEntity in the schema.
        /// </summary>
        public virtual AudContainerEntity AudContainerEntity
        {
            get;
            set;
        }
    
        /// <summary>
        /// There are no comments for AudTableEntity in the schema.
        /// </summary>
        public virtual AudTableEntity AudTableEntity
        {
            get;
            set;
        }

        #endregion
    
        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            AudContainer2AudTableEntity obj = new AudContainer2AudTableEntity();
            obj.AudCointainerFk = AudCointainerFk;
            obj.AudTableFk = AudTableFk;
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(AudContainer2AudTableEntity clonedEntity);

    }


}
