using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Platform.BusinessComponents;
using NLS = RIB.Visual.Basics.Common.Localization.Properties.Resources;

namespace RIB.Visual.Awp.Main.BusinessComponents
{
	/// <summary>
	/// This class represent a Grouping node, returned via the webapi. 
	/// Is similar to the Datatransferobject (dto)
	/// </summary>
	public partial class PackageStructureLineItemNode: GroupingItemNode
	{
		private IList<PackageStructureLineItemNode> _children;

		/// <summary>
		/// 
		/// </summary>
		/// <param name="node"></param>
		private void AddChild(PackageStructureLineItemNode node)
		{
			_children = _children ?? new List<PackageStructureLineItemNode>();
			_children.Add(node);
		}


		/// <summary>
		/// Ctor
		/// Initialize a new GroupingHierarchyEntity from a grpItem
		/// </summary>
		/// <param name="grpItem"></param>
		/// <param name="myParent"></param>
		/// <param name="sqlbuilder"></param>
		public PackageStructureLineItemNode(GrpItem grpItem, PackageStructureLineItemNode myParent, GroupingSqlBuilder sqlbuilder):
			base(grpItem, myParent, sqlbuilder)
		{
			Code = grpItem.Code;
			Description = FormatEntityIdToDescription(grpItem);
			if (myParent != null)
			{
				ParentId = myParent.Id;
				myParent.AddChild(this);
			}
			EntityId = grpItem.Id as int?;
		}

		private static string FormatEntityIdToDescription(GrpItem grpItem)
		{
			var ret = string.Empty;

			if (grpItem.IdEqualsNull)
			{
				if (grpItem.Level == 1)
				{
					ret = NLS.Grouping_Unassigned;
				}
				else
				{
					ret = NLS.Grouping_ParentAssignment;
				}

				return ret;
			}

			return grpItem.Desc;
		}

		#region Properties

		/// <summary>
		/// 
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
		public int? EntityId { get; set; }

		/// <summary>
		/// Concatenated Description of the Grouping Column, i.e.  concatenated "@code @description"
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"comment")]
		public string Code { get; set; }


		/// <summary>
		/// There are no comments for LogonName in the schema.
		/// </summary>
		public IEnumerable<PackageStructureLineItemNode> Children
		{
			get { return _children; }
			set { _children = value.ToList(); }
		}

		/// <summary>
		/// There are no comments for AqQuantity in the schema.
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"decimal")]
		public decimal? Quantity { get; set; }

		/// <summary>
		/// There are no comments for WqQuantity in the schema.
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"decimal")]
		public decimal? WqQuantity { get; set; }

		/// <summary>
		/// There are no comments for WqQuantity in the schema.
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"decimal")]
		public decimal? QuantityTotal { get; set; }

		/// <summary>
		/// There are no comments for BasUomFk in the schema.
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
		public int? BasUomFk { get; set; }

		/// <summary>
		/// There are no comments for BasUomFk in the schema.
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
		public int? PrcStructureFk { get; set; }

		/// <summary>
		/// There are no comments for UnitRate in the schema.
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"decimal")]
		public decimal? UnitRate { get; set; }

		/// <summary>
		/// There are no comments for FinalPrice in the schema.
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"decimal")]
		public decimal? FinalPrice { get; set; }

		/// <summary>
		/// There are no comments for GrandCostUnitTarget in the schema.
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"decimal")]
		public decimal? GrandCostUnitTarget { get; set; }

		/// <summary>
		/// There are no comments for GrandTotal in the schema.
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"decimal")]
		public decimal? GrandTotal { get; set; }

		#endregion
	}
}
