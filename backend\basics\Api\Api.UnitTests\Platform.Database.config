<?xml version="1.0" encoding="utf-8"?>
<dbsettings>
  <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="dbsettings">
      <xs:complexType>
        <xs:sequence>
          <xs:element ref="dbproviders"/>
        </xs:sequence>
      </xs:complexType>
    </xs:element>
    <xs:element name="dbproviders">
      <xs:complexType>
        <xs:sequence>
          <xs:element ref="dbprovider" maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="default" use="required">
          <xs:simpleType>
            <xs:restriction base="xs:string"/>
          </xs:simpleType>
        </xs:attribute>
      </xs:complexType>
    </xs:element>
    <xs:element name="dbprovider">
      <xs:complexType>
        <xs:attribute name="username">
          <xs:simpleType>
            <xs:restriction base="xs:string"/>
          </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="type" use="required">
          <xs:simpleType>
            <xs:restriction base="xs:string">
              <xs:enumeration value="MSSQL"/>
              <xs:enumeration value="Oracle"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="plainpassword">
          <xs:simpleType>
            <xs:restriction base="xs:string"/>
          </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="name" use="required">
          <xs:simpleType>
            <xs:restriction base="xs:string"/>
          </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="integratedsecurity" type="xs:boolean" use="required"/>
        <xs:attribute name="encryptpassword">
          <xs:simpleType>
            <xs:restriction base="xs:string"/>
          </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="datasource" use="required">
          <xs:simpleType>
            <xs:restriction base="xs:string"/>
          </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="databasename">
          <xs:simpleType>
            <xs:restriction base="xs:string"/>
          </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="serviceid">
          <xs:simpleType>
            <xs:restriction base="xs:string"/>
          </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="servicename">
          <xs:simpleType>
            <xs:restriction base="xs:string"/>
          </xs:simpleType>
        </xs:attribute>
        <xs:attribute name="port">
          <xs:simpleType>
            <xs:restriction base="xs:integer"/>
          </xs:simpleType>
        </xs:attribute>
      </xs:complexType>
    </xs:element>
  </xs:schema>

  <dbproviders default="MSSQL.itwoCloud">
    <dbprovider name="default"
                type="Oracle"
                datasource="RIB-S-ORADEV01"
                databasename=""
                integratedsecurity="false"
                username="iTWO"
                encryptpassword="AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAMRU6YejoB0WC9rbX29zmnAQAAAACAAAAAAADZgAAwAAAABAAAAACO2+p1EpAWuwrGts9jSuxAAAAAASAAACgAAAAEAAAACXB8uV+ZgF8dt7zFLI5M30QAAAA3yfOySyrshLJuC0+zJDxLxQAAACp+8m+HArKrFjgZElY3wbxPpL6cQ=="
                port="1521"
                servicename="RIBITWO"
                serviceid=""/>
		<dbprovider name="MSSQL.itwoCloud(local)"	type="MSSQL" datasource=".\" databasename="itwocloud" integratedsecurity="true"/>
		<dbprovider name="MSSQL.itwoCloud"	type="MSSQL" datasource="sql12-dev\dev" databasename="itwocloud" integratedsecurity="true"/>
    <dbprovider name="Oracle:(local)[iTWO]" type="Oracle" datasource="localhost" databasename="RIBITWO"	username="iTWO" integratedsecurity="false"/>
  </dbproviders>
</dbsettings>