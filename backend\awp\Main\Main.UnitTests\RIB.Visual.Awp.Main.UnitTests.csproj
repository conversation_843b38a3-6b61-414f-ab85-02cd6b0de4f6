﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <TargetFramework>net8.0</TargetFramework>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Platform.AppServer.Runtime">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Runtime.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessComponents">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessComponents.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessEnvironment">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessEnvironment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Core">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.OperationalManagement">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.OperationalManagement.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Server.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Server.Common.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.UnitTests.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.UnitTests.Common.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Localization">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Localization.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.AppServer.Web">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Web.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.ServiceFacade.WebApi.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.BusinessComponents.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.Common.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.Core.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.Localization">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.Localization.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.ServiceDomain">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.ServiceDomain.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.ServiceFacade.WebApi.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="RIB.Visual.Cloud.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.BusinessComponents.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.BusinessComponents.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Common.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Core.dll</HintPath>
      <Private>true</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.ServiceFacade.WebApi.dll</HintPath>
      <Private>true</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <None Include="RIB.Visual.Awp.Main.UnitTests.dll.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="RIBvisual.snk" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Main.BusinessComponents\RIB.Visual.Awp.Main.BusinessComponents.csproj">
      <Project>{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}</Project>
      <Name>RIB.Visual.Awp.Main.BusinessComponents</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\Main.Common\RIB.Visual.Awp.Main.Common.csproj">
      <Project>{7DAC796E-F383-4330-97A9-840454663121}</Project>
      <Name>RIB.Visual.Awp.Main.Common</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\Main.Core\RIB.Visual.Awp.Main.Core.csproj">
      <Project>{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}</Project>
      <Name>RIB.Visual.Awp.Main.Core</Name>
      <Private>False</Private>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="$(RIBvisualBinPool)\xunit.runner.json">
      <Link>xunit.runner.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.Data.SqlClient" />
    <PackageReference Include="EntityFramework" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="DeepEqual" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="XunitXml.TestLogger" />
    <PackageReference Include="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F" />
  </Target>
</Project>