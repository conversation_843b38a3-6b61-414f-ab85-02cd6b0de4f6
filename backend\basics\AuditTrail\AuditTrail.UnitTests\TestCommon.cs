///*
// * $Id: TestCommon.cs 223365 2013-12-13 15:11:03Z ford $
// * Copyright (c) RIB Software AG
// */

//using System;
//using RIB.Visual.Platform.UnitTests.Common;

//namespace RIB.Visual.Basics.AuditTrail.UnitTests
//{
//	/// <summary>
//	/// Test common functionality
//	/// </summary>
//	/// 
//	/// \since 20-01-2010 by rei 
//	internal class TestCommon
//	{
//		/// <summary>
//		/// Initializes application server as self hosted
//		/// </summary>
//		public static void InitializeSelfHosting()
//		{
//			Environment.SetEnvironmentVariable("RIBVISUAL_SERVICESSELFHOSTING", "1");
//		}

//		/// <summary>
//		/// Intialize Server Environment and do Login (integrated Login) into Plattform
//		/// </summary>
//		/// 
//		/// \since 20-01-2010 by rei
//		public static void InitializeForServerConnection()
//		{
//			InitializeSelfHosting();

//			TestEnvironment.InitTestEnvironment();
//		}

//	}
//}