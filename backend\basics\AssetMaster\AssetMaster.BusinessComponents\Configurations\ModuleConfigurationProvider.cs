﻿using System.ComponentModel.Composition;
using Newtonsoft.Json.Linq;

using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Core.BusinessComponents;

namespace RIB.Visual.Basics.AssetMaster.BusinessComponents
{
    /// <summary>
    /// 
    /// </summary>
	[Export("Basics.AssetMaster", typeof(IModuleConfigurationProvider))]
	public class ModuleConfigurationProvider : IModuleConfigurationProvider
	{
		Newtonsoft.Json.Linq.JObject IModuleConfigurationProvider.GetConfiguration()
		{
			dynamic res = new JObject();
			var container = new JArray();
			var schemes = new JArray();
			var collector = new DynamicEntityUsageCollector();
			var usages = collector.ForFilter<IBelongsToAsset>();
			foreach (var usage in usages)
			{
				dynamic dtoSpec = new JObject();
				dtoSpec.typeName = usage.Dto;
				dtoSpec.moduleSubModule = usage.DtoAssembly;
				schemes.Add(dtoSpec);

				if (!string.IsNullOrEmpty(usage.GridLayoutUid))
				{
					dynamic containerGrid = new JObject();
					containerGrid.id = usage.NewGridLayoutUid;
					containerGrid.moduleName = usage.ModuleName;
					containerGrid.usedLayout = usage.GridLayoutUid;
					containerGrid.layout = usage.NewGridLayoutUid;
					containerGrid.dto = usage.Dto;
					containerGrid.assembly = usage.DtoAssembly;
					containerGrid.http = usage.HttpRoute;
					containerGrid.title = usage.GridContainerTitle;
					container.Add(containerGrid);
				}

				if (!string.IsNullOrEmpty(usage.DetailLayoutUid))
				{
					dynamic containerDetail = new JObject();
					containerDetail.id = usage.NewDetailLayoutUid;
					containerDetail.moduleName = usage.ModuleName;
					containerDetail.usedLayout = usage.DetailLayoutUid;
					containerDetail.layout = usage.NewDetailLayoutUid;
					containerDetail.dto = usage.Dto;
					containerDetail.assembly = usage.DtoAssembly;
					containerDetail.http = usage.HttpRoute;
					containerDetail.title = usage.DetailContainerTitle;
					container.Add(containerDetail);
				}
			}
			res.schemes = schemes;

			res.container = container;

			return res;
		}
	}
}
