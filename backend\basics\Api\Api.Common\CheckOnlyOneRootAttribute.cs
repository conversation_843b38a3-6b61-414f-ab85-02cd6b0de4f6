﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Check the collection if there are more than one root or not
	/// </summary>
	[AttributeUsage(AttributeTargets.Property)]
	public class CheckOnlyOneRootAttribute : ValidationAttribute
	{
		/// <summary>
		/// 
		/// </summary>
		public CheckOnlyOneRootAttribute()
		{
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="value"></param>
		/// <param name="validationContext"></param>
		/// <returns></returns>
		protected override ValidationResult IsValid(object value, ValidationContext validationContext)
		{
			ValidationResult result = ValidationResult.Success;

			if (validationContext.ObjectInstance != null)
			{
				if (value != null && value is IEnumerable)
				{
					var collection = (value as IEnumerable).AsQueryable();
					var properties = collection.ElementType.GetProperties(System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.SetProperty | System.Reflection.BindingFlags.GetProperty);

					var keyProperties = new List<PropertyInfo>();

					if (properties != null)
					{
						foreach (var property in properties)
						{
							var compositeKey = property.GetCustomAttribute<CompositeRootKeyAttribute>();
							if (compositeKey != null)
							{
								keyProperties.Add(property);
							}
						}
					}

					if (keyProperties.Any())
					{
						HashSet<string> compositeKeys = new HashSet<string>();
						int index = 0;
						string split = "&_&";
						List<string> keyLog = new List<string>();
						IDictionary<string, string> rootMap = new Dictionary<string, string>();

						foreach (var item in collection)
						{
							string key = null;
							string values = null;
							int isRootPropCount = 0;
							foreach (var keyProp in keyProperties)
							{
								var compositeKey = keyProp.GetCustomAttribute<CompositeRootKeyAttribute>();
								var tempValue = keyProp.GetValue(item);
								string temp = tempValue != null ? tempValue.ToString() : string.Empty;

								if (compositeKey.IsKey)
								{
									key += split + temp;
									isRootPropCount++;
								}
								else if (!string.IsNullOrEmpty(compositeKey.Value))
								{
									if (temp.Equals(compositeKey.Value))
									{
										values += split + temp;
										isRootPropCount++;
									}
								}
								else if (string.IsNullOrEmpty(temp))
								{
									values += split + temp;
									isRootPropCount++;
								}

								keyLog.Add(string.Format("{0}({1})", keyProp.Name, temp));
							}

							if (isRootPropCount == keyProperties.Count)
							{
								if (string.IsNullOrEmpty(key))
								{
									key = split;
								}
								if (!rootMap.ContainsKey(key))
								{
									rootMap.Add(key, values);
								}
								else
								{
									string errorMsg = string.Format("{0}[{1}].{2}", validationContext.MemberName, index, keyLog[0]);

									for (int i = 1; i < keyLog.Count(); ++i)
									{
										errorMsg += string.Format(", {0}", keyLog[i]);
									}

									errorMsg += ", more than one root is not allowed.";

									result = new ValidationResult(errorMsg, new List<string>() { validationContext.MemberName });
									break;
								}
							}
							++index;
							keyLog.Clear();
						}
					}
				}
			}

			return result;
		}
	}
}
