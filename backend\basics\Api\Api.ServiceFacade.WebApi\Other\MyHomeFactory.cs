using IdentityModel.Client;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Platform.Server.Common;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Security;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Xml.Linq;
using Convert = System.Convert;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary/>
	internal class ValidateResult
	{
		/// <summary/>
		public HttpStatusCode StatusCode { get; set; }
		/// <summary/>
		public string LogonName { get; set; }
		/// <summary/>
		public string ErrorCode { get; set; }

	}
	/// <summary>
	/// myHome Factory handles all service and methods used for doing single sign on and 
	/// authenticate a myHome user for itwo4.0
	/// 
	/// Services avialable:
	///   read parameters from web.config file and save it locally into properties
	///   myHome Encryption/Decryption methods
	///	  		used for encrypt/decrypt mobile app passwords
	///   
	///   myHome Ticket validation service 
	///   myHome Logout service 
	///   
	/// rei@2.1.18
	/// </summary>
	internal class MyHomeFactory : TokenFactoryBase
	{
		/// <summary>
		/// parse string with comma separated byte or hex byte (0x23) 
		/// into an array of bytes[]
		/// </summary>
		/// <param name="toparse"></param>
		/// <returns></returns>
		private byte[] ParseStringToByteArray(string toparse)
		{
			var iv = toparse.Split(',');
			var retVal = new List<byte>();
			foreach (var ivItem in iv)
			{
				byte parsed;
				if (ivItem.Contains("x"))
				{
					try
					{
						parsed = Convert.ToByte(ivItem, 16);
						retVal.Add(parsed);
					}
					catch { ;}
				}
				else
				{
					if (byte.TryParse(ivItem, out parsed))
					{
						retVal.Add(parsed);
					}
				}
			}
			return retVal.ToArray();
		}

		private byte[] _providerIv;
		private string _providerKey;

		/// <summary>
		/// reads the configuration parameters from the web.config file of WEB-Server
		/// 
		/// <example>
		/// <code>
		///		<appSettings>
		///			<add key="myhome:sso.providerurl" value="https://rib-w0918.rib-software.com/myhome/ssoserver/sso/"/>
		///			<add key="myhome:provider.iv" value="0x12,0x34,0x56,0x78,0x90,0xab,0xcd,0xef"/>
		///			<add key="myhome:provider.key" value="thekeyvalue here"/>
		///		</appSettings>
		/// </code>
		/// </example>
		/// </summary>
		protected override void ReadConfig()
		{
			_ssoServiceUrl = AppSettingsReader.ReadString("myhome:sso.providerurl");
			_ssoServiceServiceUrl = AppSettingsReader.ReadString("myhome:sso.serviceurl");
			_providerIv = AppSettingsReader.ReadByteArray("myhome:provider.iv");
			_providerKey = AppSettingsReader.ReadString("myhome:provider.key");
			_idpId = AppSettingsReader.ReadInt("idm:identityproviderid");

		}

		/// <summary>
		/// Encrypt a string value with myHome specific key and iv values
		/// </summary>
		/// <param name="input"></param>
		/// <param name="key"></param>
		/// <returns></returns>
		private string Encrypt(string input, string key = null)
		{
			try
			{
				// ReSharper disable once UseObjectOrCollectionInitializer
				var provider =  DES.Create(); // rei@8.6.22 mig4rated to base class like proposed by obsolete message. // new DESCryptoServiceProvider();
				/*************************************************/
				//NOTE: MYHome use the below encrytion/decrytion parameters
				provider.Key = Encoding.UTF8.GetBytes(key ?? _providerKey);
				provider.IV = _providerIv;
				provider.Padding = PaddingMode.PKCS7;
				provider.Mode = CipherMode.CBC;
				/*************************************************/
				var inputByteArray = Encoding.UTF8.GetBytes(input);
				using (var ms = new MemoryStream())
				{
					using (var cs = new CryptoStream(ms, provider.CreateEncryptor(), CryptoStreamMode.Write))
					{
						cs.Write(inputByteArray, 0, inputByteArray.Length);
						cs.FlushFinalBlock();
					}
					return Convert.ToBase64String(ms.ToArray());
				}
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException("Encryption of input string failed", ex);
			}

		}

		/// <summary>
		/// Decrypt a string value with myHome specific key and iv values
		/// </summary>
		/// <param name="input"></param>
		/// <param name="key"></param>
		/// <returns></returns>
		private string Decrypt(string input, string key = null)
		{
			try
			{
				// ReSharper disable once UseObjectOrCollectionInitializer
				var provider = DES.Create(); // rei@8.6.22 mig4rated to base class like proposed by obsolete message. // new DESCryptoServiceProvider();

				/*************************************************/
				//NOTE: MYHome use the below encrytion/decrytion parameters
				provider.Key = Encoding.UTF8.GetBytes(key ?? _providerKey);
				provider.IV = _providerIv;
				provider.Padding = PaddingMode.PKCS7;
				provider.Mode = CipherMode.CBC;
				/*************************************************/
				var inputByteArray = Convert.FromBase64String(input);
				using (var ms = new MemoryStream())
				{
					using (var cs = new CryptoStream(ms, provider.CreateDecryptor(), CryptoStreamMode.Write))
					{
						cs.Write(inputByteArray, 0, inputByteArray.Length);
						cs.FlushFinalBlock();
					}
					return Encoding.UTF8.GetString(ms.ToArray());
				}

			}
			catch (Exception ex)
			{
				throw new BusinessLayerException("Decryption of input string failed", ex);
			}
		}

		protected override ValidateResult ValidateTicket(string ticket)
		{
			ValidateResult result = new ValidateResult() { StatusCode = HttpStatusCode.Unauthorized };

			var escapedServiceUrl = Uri.EscapeDataString(_ssoServiceServiceUrl);

						// build sso servie url
			var validateUrl = string.Format("{0}serviceValidate?ticket={1}&service={2}",
						_ssoServiceUrl, ticket, escapedServiceUrl, escapedServiceUrl);

			ServicePointManager.ServerCertificateValidationCallback = MyRemoteCertificateValidationCallback;

			var response = new HttpClient().GetStringAsync(validateUrl).Result;
			if (!string.IsNullOrWhiteSpace(response))
			{
				var validateResult = ReadLogonNameFromTickedResult(response);
				if (validateResult.StatusCode == HttpStatusCode.OK)
				{
					result.LogonName = validateResult.LogonName;
					result.StatusCode = HttpStatusCode.OK;
				}
				else
				{
					result.ErrorCode = validateResult.ErrorCode;
				}
			}

			return result;
		}

		private static bool MyRemoteCertificateValidationCallback(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
		{
			return true;
		}


		/// <summary>
		/// Parse the user from the validate ticket result service
		/// </summary>
		/// <param name="validateTicketResult"></param>
		/// <returns></returns>
		private ValidateResult ReadLogonNameFromTickedResult(string validateTicketResult)
		{

			var res = new ValidateResult();
			string xmln = "http://www.yale.edu/tp/cas";
			var xml = XDocument.Parse(validateTicketResult);
			var xElement = xml.Element(XName.Get("serviceResponse", xmln));
			if (xElement == null)
			{
				return null;
			}

			var element = xElement.Element(XName.Get("authenticationFailure", xmln));
			if (element != null)
			{
				res.StatusCode = HttpStatusCode.Unauthorized;
				res.ErrorCode = string.Format("{0} {1}", element.Attribute("code"), element.Value);
				return res;
			}

			element = xElement.Element(XName.Get("authenticationSuccess", xmln));
			if (element == null)
			{
				res.StatusCode = HttpStatusCode.Unauthorized;
				res.ErrorCode = "Element authenticationSuccess not found ";
				return res;
			}
			var item = element.Element(XName.Get("user", xmln));
			res.StatusCode = HttpStatusCode.OK;
			res.LogonName = item != null ? item.Value : null;
			return res;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="encryptedUsername"></param>
		public TokenResponse CreateTokenFromEncryptedUser(string encryptedUsername)
		{
			ReadConfig();
			if (!string.IsNullOrWhiteSpace(encryptedUsername))
			{
				var logonName = Decrypt(encryptedUsername);
				return CheckLogonNameandCreateToken(logonName);
			}
			return ResultUnauthorizedLogonname;
		}

		/// <summary>
		/// Test only never public in general !!!!
		/// </summary>
		/// <param name="logonName"></param>
		private string EncryptLogonName(string logonName)
		{
			return string.IsNullOrWhiteSpace(logonName) ? null
											: Encrypt(logonName);
		}
	}
}
