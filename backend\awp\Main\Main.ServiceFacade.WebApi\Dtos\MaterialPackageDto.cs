using System.Collections;
using System.Collections.Generic;
using RIB.Visual.Platform.Common;

namespace RIB.Visual.Awp.Main.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public class MaterialPackageDto
	{
		/// <summary>
		/// 
		/// </summary>
		public int Id { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int MainItemId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string Itemno { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public DescriptionTranslateTypeDto Description { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int TypeFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? ParentFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<MaterialPackageDto> Children { get; set; }

	}
}
