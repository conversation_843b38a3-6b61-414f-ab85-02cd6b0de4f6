﻿using RIB.Visual.Basics.AuditTrail.BusinessComponents;
using RIB.Visual.Basics.AuditTrail.BusinessComponents.Logic;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web.Http;

namespace RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi
{

	/// <summary>
	/// 
	/// </summary>
	[RoutePrefix("basics/audittrail")]
	public class BasicsAuditTrailController : ApiControllerBase<BasicsAuditTrailLogic>
	{
		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		[Route("getinfo")]
		public string GetInfo()
		{
			return ApiControllerBase.GetDefaultInfo();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		[Route("list")]
		[HttpPost]
        public IDictionary<string, object> GetList(AudFilterEntity filter)
		{
			var result = Logic.GetList(filter);
			return result;
        }
               

        // to do : set the container GUID as per the container
        /// <summary>
		/// Get all Containers
		/// </summary>
		/// <returns>all containers</returns>
        [HttpGet]
        [Route("GetAllContainers")]        
        public IEnumerable<AudContainerDto> GetAllContainers()
        {
            var dtos = Logic.GetAllContainers().Select(e => new AudContainerDto(e));
            return dtos;
        }

        // to do : set the container GUID as per the container
        /// <summary>
        /// Get Containers as per description
        /// </summary>
        /// <param name="DescriptionContains">List of all Audit container by description</param>
        /// <returns>all containers as per description</returns>
        [HttpGet]
        [Route("GetAllContainersByContainerDescription")]        
        public IEnumerable<AudContainerDto> GetAllContainersByContainerDescription(string DescriptionContains)
        {
            var dtos = Logic.GetAllContainersByContainerDescription(DescriptionContains).Select(e => new AudContainerDto(e));
            return dtos;
        }

        /// <summary>
        /// Delete an existing container
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("delete")]
        // to do : set the container GUID as per the container
        [Permission(Permissions.Delete, "50593feea9fe4280b36f72e27c8dfda1")]
        public void Delete(AudContainerDto dto)
        {
            Logic.DeleteContainer(dto.Copy());
        }
    }
}
