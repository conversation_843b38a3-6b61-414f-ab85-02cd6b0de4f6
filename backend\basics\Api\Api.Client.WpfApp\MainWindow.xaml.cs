using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows;
using IdentityModel.Client;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace RIB.Visual.Basics.Api.Client.WpfApp
{
	/// <summary>
	/// Interaction logic for MainWindow.xaml
	/// </summary>
	public partial class MainWindow : Window, INotifyPropertyChanged
	{
		private bool _canStartInquiry;
		private bool _canRequestData;

		/// <remarks></remarks>>
		public Model TheModel { get; set; }

		/// <remarks></remarks>>
		private TokenResponse Token { get; set; }

		/// <remarks></remarks>>
		private string AccessToken { get; set; }

		/// <remarks></remarks>>
		private bool DeleteInquiryAfterRead { get; set; }


		/// <remarks></remarks>>
		public bool CanStartInquiry
		{
			get { return _canStartInquiry; }
			set
			{
				_canStartInquiry = value;
				OnPropertyChanged("CanStartInquiry");
			}
		}

		public bool CanRequestData
		{
			get { return _canRequestData; }
			set
			{
				_canRequestData = value;
				OnPropertyChanged("CanRequestData");
			}
		}


		public MainWindow()
		{
			InitializeComponent();
			this.DataContext = this;

			TheModel = new Model();
			DeleteInquiryAfterRead = true;

			InitValues(TheModel);

		}


		private void InitValues(Model theModel)
		{
			CanStartInquiry = false;
			CanRequestData = false;
			theModel.Username = "ribadmin";
			theModel.SetToSecurePassword("ribadmin");

			theModel.SearchPattern = "Felde,";
			theModel.CompanyCode = "901";
			theModel.IdentityServerUrl = Constant.IdentityServerUrl1;
			theModel.Operation = Constant.OperationInquiry;

			theModel.BaseUrl = Constant.BaseUrl1;
			theModel.JsonOutput = "<json output will come here>";
			theModel.IsSelection = true;
			this.Password.Password = theModel.GetUnsecurePassword();

		}


		private void OnLogin(object sender, RoutedEventArgs e)
		{
			TheModel.JsonOutput = "Login via Identity Server running...   Please wait...";

			TheModel.SetToSecurePassword(this.Password.Password);

			BackgroundWorker worker = new BackgroundWorker();
			worker.DoWork += (a, b) =>
								{
									CanStartInquiry = false;
									CanRequestData = false;
									//Token = IdentityServer.Login(TheModel.Username, TheModel.GetUnsecurePassword());
									TokenResponse token;
									var response = IdentityServer.Login(TheModel, out token);

									Token = token;
									if (token == null)
									{ TheModel.JsonOutput = response; }


								};
			worker.RunWorkerCompleted += worker_RunWorkerCompleted;
			worker.RunWorkerAsync();

		}

		private void OnLoginViaAppServer(object sender, RoutedEventArgs e)
		{
			TheModel.JsonOutput = "Login via Application Server running...   Please wait...";

			TheModel.SetToSecurePassword(this.Password.Password);

			BackgroundWorker worker = new BackgroundWorker();
			worker.DoWork += (a, b) =>
			{
				CanStartInquiry = false;
				CanRequestData = false;
				//Token = IdentityServer.Login(TheModel.Username, TheModel.GetUnsecurePassword());
				AccessToken = ServerCalls.DoLogin(TheModel);

				if (AccessToken == null)
				{ TheModel.JsonOutput = AccessToken; }

			};
			worker.RunWorkerCompleted += worker_RunWorkerCompleted;
			worker.RunWorkerAsync();

		}

		void worker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
		{
			TheModel.JsonOutput += "\nLogin finished";
			if (Token != null)
			{
				TheModel.JsonOutput += "\n" + IdentityServer.ShowResponse(Token);
				TheModel.NotifyChanged();
				CanStartInquiry = true;
				CanRequestData = true;
			}
			if (AccessToken != null)
			{
				TheModel.JsonOutput += "\nAccessToken: " + AccessToken;
				TheModel.NotifyChanged();
				CanStartInquiry = true;
				CanRequestData = true;
			}
		}


		private void OnStartInquiry(object sender, RoutedEventArgs e)
		{
			try
			{
				TheModel.ClientUrl = Constant.MakeRequestUrl(TheModel);
				TheModel.NotifyChanged();
				TheModel.JsonOutput = "start Inquiry";
				//Passing Secure string to Initiate New Process, This line below will start a new 
				//Notepad Instance if the User and Password authenticates correctly.

				OpenBrowser(TheModel.ClientUrl);

				if (TheModel.Operation.StartsWith(Constant.OperationInquiry))
				{
					var worker = new BackgroundWorker();
					worker.DoWork += (a, b) =>
					{
						CanStartInquiry = false;
						string requestResult = PollForInquiryReady();

						try
						{
							JObject jObject = JObject.Parse(requestResult);
							TheModel.JsonOutput = JsonConvert.SerializeObject(jObject, Formatting.Indented);
						}
						finally
						{
							;
						}
					};

					worker.RunWorkerCompleted += (a, b) =>
					{
						CanStartInquiry = true;
					};
					worker.RunWorkerAsync();
				}

			}
			catch (Win32Exception ex)
			{
				TheModel.JsonOutput = ex.Message;
				TheModel.NotifyChanged();
			}
		}

		private static void OpenBrowser(string url)
		{
			try
			{
				Process.Start(url);
			}
			catch
			{
				if(RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
				{
					url = url.Replace("&", "^&");
					Process.Start(new ProcessStartInfo("cmd", $"/c start {url}") { CreateNoWindow = true });
				}
				else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
				{
					Process.Start("xdg-open", url);
				}
				else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
				{
					Process.Start("open", url);
				}
				else
				{
					throw;
				}
			}
		}

		/// <summary/>
		/// <param name="result"></param>
		/// <returns></returns>
		private int DoRequest(out string result)
		{
			try
			{
				var accessToken = AccessToken ?? Token.AccessToken;
				var requestOk = ServerCalls.CallService(accessToken, TheModel.ServicesUrl, TheModel.OperationRelUrl, TheModel.CompanyCode, TheModel.RequestIdAsString, DeleteInquiryAfterRead, out result);

				// request was successful and requestID was found, which means Cancel or Save was pressed by User
				if (requestOk)
				{
					return (result == "null") ? 0 : 1;
				}
				return 0;
			}
			catch (Win32Exception ex)
			{
				result = ex.Message;
				return -1;
			}

		}


		private string PollForInquiryReady()
		{
			bool requestCanceledOrSaved = false, cancelled = false;
			string result;
			string busyIndicator = "";
			do
			{
				var retCode = DoRequest(out result);

				requestCanceledOrSaved = (retCode == 1);
				cancelled = (retCode == -1);

				busyIndicator += '>';

				if (!(requestCanceledOrSaved || cancelled))
				{
					TheModel.JsonOutput = busyIndicator;
					Thread.Sleep(1000);
				}
			} while (!(requestCanceledOrSaved || cancelled));

			TheModel.JsonOutput = result;
			return result;
		}


		private void OnRequestData(object sender, RoutedEventArgs e)
		{
			var worker = new BackgroundWorker();
			worker.DoWork += (a, b) =>
			{
				try
				{
					//Passing Secure string to Initiate New Process, This line below will start a new 
					//Notepad Instance if the User and Password authenticates correctly.
					//Process.Start(TheModel.ServicesUrl);
					string result;
					CanRequestData = false;
					TheModel.JsonOutput = "start RequestData ... please Wait ";
					var accessToken = AccessToken ?? Token.AccessToken;

					var requestOk = ServerCalls.CallService(accessToken, TheModel.ServicesUrl, TheModel.OperationRelUrl, TheModel.CompanyCode, TheModel.RequestIdAsString, DeleteInquiryAfterRead, out result);

					if (requestOk) { } else { }
					TheModel.JsonOutput = result;
				}
				catch (Win32Exception ex)
				{
					TheModel.JsonOutput = ex.Message;
				}
			};

			worker.RunWorkerCompleted += (a, b) =>
			{
				CanRequestData = true;
			};
			worker.RunWorkerAsync();


		}


		/// <summary/>
		/// <param name="sender"></param>
		/// <param name="e"></param>
		private void OnReset(object sender, RoutedEventArgs e)
		{
			InitValues(TheModel);
			TheModel.NotifyChanged();
		}

		/// <summary/>
		public event PropertyChangedEventHandler PropertyChanged;
		protected virtual void OnPropertyChanged(string propertyName)
		{
			if (this.PropertyChanged != null)
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
			}
		}

		private void ComboBox_SelectionChanged(System.Object sender, System.Windows.Controls.SelectionChangedEventArgs e)
		{
			TheModel.ClientUrl=Constant.MakeRequestUrl(TheModel);
		}

		private void RadioButton_Checked(System.Object sender, RoutedEventArgs e)
		{
			TheModel.ClientUrl = Constant.MakeRequestUrl(TheModel);
		}
	}
}
