﻿using System;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample;

namespace RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample
{
	/// <summary>
	/// User Dto for communication with the web-service Calla
	/// </summary>
	public class UserDto
	{
		public string Name { get; set; }
		public string LogonName { get; set; }
		public string EMail { get; set; }
		public string Description { get; set; }
		public string Password { get; set; }
	}

}
/// <summary>
/// 
/// </summary>
public static class UserManagement
{
	public static LogonServices LogonServices { get; set; } 

	#region deal with Usermanagement services
	/// <summary>
	/// Create a user via PublicApi asynchronous
	/// </summary>
	/// <returns></returns>
	public static async Task<Response> CreateUserAsynch(UserDto userdto)
	{
		const string name = "CreateUserAsynch";
		Console.WriteLine("{0} - Start", name);
		var responseResult = await Task.Run<Response>(() => CreateUser(userdto));
		Console.WriteLine("{0} - Done", name);
		return responseResult;
	}


	/// <summary>
	/// Create a user via PublicApi
	/// </summary>
	/// <returns></returns>
	private static Response CreateUser(UserDto userdto)
	{
		var fctResponse = new Response() { Result = false };
		using (var client = new HttpClient())
		{
			LogonServices.SetTokenClientContext(client);
			try
			{
				// send Get Call to Backend
				var url = LogonServices.ServicesUrl + "/usermanagement/publicapi/1.0/user/create";
				var payload = JsonConvert.SerializeObject(userdto);
				var myContent = new StringContent(payload, Encoding.UTF8, "application/json");		// defined communication format and add parameter content into it
				var response = client.PostAsync(url, myContent).Result;
				if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.ResetContent)
					return LogonServices.AnalyseHttpStatusCode(response);

				return LogonServices.ReadResponseValue(response, fctResponse);
			}
			catch (Exception ex)
			{
				fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
				return fctResponse;
			}
		}
	}

	/// <summary>
	/// Read User by Logonname via PublicApi  asynch
	/// </summary>
	/// <param name="logonname"></param>
	/// <returns></returns>
	public static async Task<Response> GetUserByLogonNameAsynch(string logonname)
	{
		const string name = "GetUserByLogonNameAsynch";
		Console.WriteLine("{0} - Start", name);
		var responseResult = await Task.Run<Response>(() => GetUserByLogonName(logonname));
		Console.WriteLine("{0} - Done", name);
		return responseResult;
	}


	/// <summary>
	/// Read User by Logonname via PublicApi  
	/// </summary>
	/// <returns></returns>
	private static Response GetUserByLogonName(string logonName)
	{
		var fctResponse = new Response() { Result = false };
		using (var client = new HttpClient())
		{
			LogonServices.SetTokenClientContext(client);
			try
			{
				// send Get Call to Backend
				var url = LogonServices.ServicesUrl + "/usermanagement/publicapi/1.0/user/getbylogonname";
				var urlwithParam = string.Format("{0}?logonName={1}", url, logonName);
				var response = client.PostAsync(urlwithParam, null).Result;
				if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.ResetContent)
					return LogonServices.AnalyseHttpStatusCode(response);

				return LogonServices.ReadResponseValue(response, fctResponse);
			}
			catch (Exception ex)
			{
				fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
				return fctResponse;
			}
		}
	}

	#endregion
	
}