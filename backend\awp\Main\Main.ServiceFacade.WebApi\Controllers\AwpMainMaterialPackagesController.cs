using System.Collections.Generic;
using System.Web.Http;
using RIB.Visual.Platform.ServiceFacade.WebApi;

namespace RIB.Visual.Awp.Main.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	[RoutePrefix("awp/main/materialepackage")]
	public class AwpMainMaterialPackagesController : ApiControllerBase
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		[Route("tree")]
		[HttpPost]
		public IEnumerable<MaterialPackageDto> GetChartConfigs(int projectId)
		{
			var result = new List<MaterialPackageDto>();

			// This is demo, need change to get from api
			result.Add(new MaterialPackageDto
			{
				Id = 1,
				Itemno = "PackageCode",
				Description = new Platform.Common.DescriptionTranslateTypeDto(),
				TypeFk = -1,
				ParentFk = null,
				Children = new List<MaterialPackageDto> {
					new MaterialPackageDto
					{
						Id = 2,
						Itemno = "Sub PackageCode",
						Description = new Platform.Common.DescriptionTranslateTypeDto(),
						TypeFk = -2,
						ParentFk = 1,
						Children = new List<MaterialPackageDto> {
							new MaterialPackageDto
							{
								Id = 3,
								Itemno = "10",
								Description = new Platform.Common.DescriptionTranslateTypeDto(),
								TypeFk = 1,
								ParentFk = 2,
								Children = new List<MaterialPackageDto>()
							}
						}
					}
				}
			});
			return result;
		}
	}
}
