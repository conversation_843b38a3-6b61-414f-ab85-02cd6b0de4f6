﻿/*
 * $Id: ApiDocIdentifyingQueryStringAttribute.cs 575923 2020-02-11 15:29:39Z haagf $
 * Copyright (c) RIB Software SE
 */

using System;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// An attribute that indicates that a web API endpoint method accepts identifying arguments for the entity accessed by means of the web API in its query string.
	/// </summary>
	[AttributeUsage(AttributeTargets.Method)]
	public class ApiDocIdentifyingQueryStringAttribute : ApiDocAttribute
	{
		/// <summary>
		/// Initializes a new instance.
		/// </summary>
		/// <param name="allowId">Indicates that arguments corresponding to the ID fields are accepted.</param>
		/// <param name="allowCode">Indicates that arguments corresponding to the code fields are accepted.</param>
		public ApiDocIdentifyingQueryStringAttribute(Boolean allowId, Boolean allowCode)
		{
			AllowId = allowId;
			AllowCode = allowCode;
		}

		/// <summary>
		/// Indicates that arguments corresponding to the ID fields are accepted.
		/// </summary>
		public Boolean AllowId { get; set; }

		/// <summary>
		/// Indicates that arguments corresponding to the code fields are accepted.
		/// </summary>
		public Boolean AllowCode { get; set; }

		/// <summary>
		/// The name of the generic type parameter whose concrete value is used to determine the ID and/or code fields.
		/// </summary>
		public String TypeParamName { get; set; }
	}
}
