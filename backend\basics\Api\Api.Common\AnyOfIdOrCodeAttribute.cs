﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Represents a composite Id or Code.
	/// </summary>
	[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
	public class AnyOfIdOrCodeAttribute : Attribute
	{
		/// <summary>
		/// Contructor.
		/// </summary>
		public AnyOfIdOrCodeAttribute(string group = "")
		{
			this.Group = group;
		}

		/// <summary>
		/// 
		/// </summary>
		public string Group { get; set; }
	}
}
