﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <TargetFramework>net8.0</TargetFramework>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Platform.AppServer.Runtime">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Runtime.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessComponents">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessComponents.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessEnvironment">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessEnvironment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Core">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.OperationalManagement">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.OperationalManagement.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Localization">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Localization.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.AppServer.Web">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Web.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.ServiceFacade.WebApi.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.BusinessComponents.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.Localization">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.Localization.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.ServiceFacade.WebApi.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Cloud.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.BusinessComponents.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.UnitTests.Common">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.UnitTests.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{B4F97281-0DBD-4835-9ED8-7DFB966E87FF}" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="Configuration\Platform.AppServer.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Configuration\Platform.Database.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="RIB.Visual.Basics.AuditTrail.UnitTests.dll.config" />
    <None Include="RIBvisual.snk" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AuditTrail.BusinessComponents\RIB.Visual.Basics.AuditTrail.BusinessComponents.csproj">
      <Project>{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}</Project>
      <Name>RIB.Visual.Basics.AuditTrail.BusinessComponents</Name>
      <Private>True</Private>
    </ProjectReference>
    <ProjectReference Include="..\AuditTrail.Common\RIB.Visual.Basics.AuditTrail.Common.csproj">
      <Project>{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}</Project>
      <Name>RIB.Visual.Basics.AuditTrail.Common</Name>
      <Private>True</Private>
    </ProjectReference>
    <ProjectReference Include="..\AuditTrail.Core\RIB.Visual.Basics.AuditTrail.Core.csproj">
      <Project>{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}</Project>
      <Name>RIB.Visual.Basics.AuditTrail.Core</Name>
      <Private>True</Private>
    </ProjectReference>
    <ProjectReference Include="..\AuditTrail.ServiceFacade.WebApi\RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi.csproj">
      <Project>{DF399B45-DDD6-412F-83FE-265DB568001F}</Project>
      <Name>RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.Data.SqlClient" />
    <PackageReference Include="EntityFramework" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="DeepEqual" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="XunitXml.TestLogger" />
    <PackageReference Include="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="$(RIBvisualBinPool)\xunit.runner.json">
      <Link>xunit.runner.json</Link>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F" />
  </Target>
</Project>