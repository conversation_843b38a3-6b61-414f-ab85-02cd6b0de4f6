//using Microsoft.VisualStudio.TestTools.UnitTesting;
//using RIB.Visual.Cloud.Common.BusinessComponents;
//using System;

//namespace RIB.Visual.Basics.AccountingJournals.UnitTests
//{
//	/// <summary>
//	///This is a test class for SequenceLogicTest and is intended
//	///to contain all SequenceLogicTest Unit Tests
//	///</summary>
//	[TestClass()]
//	public class SampleTest
//	{

//		private TestContext _testContextInstance;

//		/// <summary>
//		///Gets or sets the test context which provides
//		///information about and functionality for the current test run.
//		///</summary>
//		public TestContext TestContext
//		{
//			get
//			{
//				return _testContextInstance;
//			}
//			set
//			{
//				_testContextInstance = value;
//			}
//		}

//		// 
//		//You can use the following additional attributes as you write your tests:
//		//
//		//Use ClassInitialize to run code before running the first test in the class
//		//[ClassInitialize()]
//		//public static void MyClassInitialize(TestContext testContext)
//		//{
//		//}

//		//
//		//Use TestCleanup to run code after each test has run
//		//[ClassCleanup()]
//		//public static void MyClassCleanup()
//		//{

//		//}

//		[TestMethod]
//		public void Name_Test()
//		{
//			Console.WriteLine("Name_Test started ...");
		
//			TestCommon.InitializeForServerConnection();

//			Console.WriteLine("Logon to Server done...");

//			var logic = new BlobLogic();
//			var result = logic.GetSearchList(e => e.Id < 20);

//			Console.WriteLine("Found {0} blobs", result.Count);

//			Console.WriteLine("Done...");
//		}

//	}
//}
