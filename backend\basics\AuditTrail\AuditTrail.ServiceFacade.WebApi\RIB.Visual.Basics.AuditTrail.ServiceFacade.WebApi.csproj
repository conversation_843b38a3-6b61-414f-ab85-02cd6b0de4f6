﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{DF399B45-DDD6-412F-83FE-265DB568001F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi</RootNamespace>
    <AssemblyName>RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi</AssemblyName>
    <TargetFramework>net8.0</TargetFramework>
    <FileAlignment>512</FileAlignment>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation />
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\RIB.Visual.Platform.ServiceFacade.WebApi.XML</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Platform.AppServer.Web">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.ServiceFacade.WebApi">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Mvc.WebApiCompatShim">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Microsoft.AspNetCore.Mvc.WebApiCompatShim.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Formatting">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Net.Http.Formatting.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework.Functions">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.Functions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.CodeDom">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.CodeDom.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Configuration.ConfigurationManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.SqlClient">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Data.SqlClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFrameworkExtras.EF6">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFrameworkExtras.EF6.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Common">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Drawing.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{B4F97281-0DBD-4835-9ED8-7DFB966E87FF}" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Controllers\BasicsAuditTrailController.cs" />
    <Compile Include="Dtos\AudColumnDto.cs" />
    <Compile Include="Dtos\AudColumnDto.Generated.cs">
      <DependentUpon>AudColumnDto.cs</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Dtos\AudContainer2AudTableDto.cs" />
    <Compile Include="Dtos\AudContainer2AudTableDto.Generated.cs">
      <DependentUpon>AudContainer2AudTableDto.cs</DependentUpon>
    </Compile>
    <Compile Include="Dtos\AudContainerAudTableDto.cs" />
    <Compile Include="Dtos\AudContainerDto.cs" />
    <Compile Include="Dtos\AudContainerDto.Generated.cs">
      <DependentUpon>AudContainerDto.cs</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Dtos\AudTableDto.cs" />
    <Compile Include="Dtos\AudTableDto.Generated.cs">
      <DependentUpon>AudTableDto.cs</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Dtos\LogEntityDto.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="../../../AssemblyVersion.cs" Link="Properties/AssemblyVersion.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="RIBvisual.snk" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Scripts\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AuditTrail.BusinessComponents\RIB.Visual.Basics.AuditTrail.BusinessComponents.csproj">
      <Project>{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}</Project>
      <Name>RIB.Visual.Basics.AuditTrail.BusinessComponents</Name>
    </ProjectReference>
    <ProjectReference Include="..\AuditTrail.Common\RIB.Visual.Basics.AuditTrail.Common.csproj">
      <Project>{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}</Project>
      <Name>RIB.Visual.Basics.AuditTrail.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\AuditTrail.Core\RIB.Visual.Basics.AuditTrail.Core.csproj">
      <Project>{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}</Project>
      <Name>RIB.Visual.Basics.AuditTrail.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\AuditTrail.Localization\RIB.Visual.Basics.AuditTrail.Localization.csproj">
      <Project>{374FBE7A-25B1-42F2-8FFE-C276BF617A17}</Project>
      <Name>RIB.Visual.Basics.AuditTrail.Localization</Name>
    </ProjectReference>
  </ItemGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F" />
  </Target>
</Project>