﻿using RIB.Visual.Basics.Company.BusinessComponents;
using RIB.Visual.Platform.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.AccountingJournals.BusinessComponents
{
    /// <summary>
    /// 
    /// </summary>
    public class AccountingJournalsCompleteEntity : IIdentifyable
    {
        /// <summary>
        /// Standard constructor
        /// </summary>
        public AccountingJournalsCompleteEntity() { }

        /// <summary>
        /// The identifier for an CompanyTransactionHeader
        /// </summary>
        public int MainItemId { get; set; }

        /// <summary>
        ///  The CompanyTransheader to be saved
        /// </summary>
        public CompanyTransheaderEntity CompanyTransheader { get; set; }

        /// <summary>
        /// The CompanyTransaction to be saved
        /// </summary>
        public IEnumerable<CompanyTransactionEntity> TransactionToSave { get; set; }

        /// <summary>
        /// The CompanyTransaction to be deleted
        /// </summary>
        public IEnumerable<CompanyTransactionEntity> TransactionToDelete { get; set; }

        /// <summary>
        /// Id
        /// </summary>
        public int Id
        {
            get
            {
                return MainItemId;
            }
            set
            {
                MainItemId = value;
            }
        }
    }
}
