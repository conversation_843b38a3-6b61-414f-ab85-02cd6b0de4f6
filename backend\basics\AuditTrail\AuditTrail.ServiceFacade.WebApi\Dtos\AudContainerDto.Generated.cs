//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Basics.AuditTrail.BusinessComponents;

namespace RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi
{

    /// <summary>
    /// Represents a Dto class.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("AUD_CONTAINER")]
    public partial class AudContainerDto : RIB.Visual.Platform.Core.ITypedDto<AudContainerEntity>
    {
        #region Constructors
       
        /// <summary>
        /// Initializes an instance of class AudContainerDto.
        /// </summary>
        public AudContainerDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class AudContainerDto.
        /// </summary>
        /// <param name="entity">the instance of class AudContainerEntity</param>
        public AudContainerDto(AudContainerEntity entity)
        {
            Id = entity.Id;
            ContainerUuid = entity.ContainerUuid;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;

            if (entity.DescriptionInfo != null )
            {
                DescriptionInfo = new DescriptionTranslateTypeDto(entity.DescriptionInfo);
            }
            
            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion 

        #region Properties

        /// <summary>
        /// Gets or Sets Id.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }

        /// <summary>
        /// Gets or Sets ContainerUuid.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"guid")]
        [RIB.Visual.Platform.Common.MappedColumn("CONTAINER_UUID", TypeName = "char(32)", Order = 1)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string ContainerUuid { get; set; }

        /// <summary>
        /// Gets or Sets DescriptionInfo.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"translation")]
        public DescriptionTranslateTypeDto DescriptionInfo { get; set; }

        /// <summary>
        /// Gets or Sets InsertedAt.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 4)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public global::System.DateTime InsertedAt { get; set; }

        /// <summary>
        /// Gets or Sets InsertedBy.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 5)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }

        /// <summary>
        /// Gets or Sets UpdatedAt.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 6)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Gets or Sets UpdatedBy.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 7)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// Gets or Sets Version.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 8)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary />
        public List<AudContainer2AudTableDto> AudContainer2AudTableEntities { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(AudContainerEntity); }
        }

        /// <summary>
        /// Copy the current AudContainerDto instance to a new AudContainerEntity instance.
        /// </summary>
        /// <returns>a new instance of class AudContainerEntity</returns>
        public AudContainerEntity Copy() 
        {
          var entity = new AudContainerEntity();

          entity.Id = this.Id;
          entity.ContainerUuid = this.ContainerUuid;
          entity.InsertedAt = this.InsertedAt;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedAt = this.UpdatedAt;
          entity.UpdatedBy = this.UpdatedBy;
          entity.Version = this.Version;

          if (this.DescriptionInfo != null )
          {

               entity.DescriptionInfo = new DescriptionTranslateType(this.DescriptionInfo);
          }

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(AudContainerEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(AudContainerEntity entity);
    }

}
