using RIB.Visual.Basics.AccountingJournals.BusinessComponents;
using RIB.Visual.Basics.Company.BusinessComponents.Logic;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Http;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.LookupData.ServiceFacade.WebApi;
namespace RIB.Visual.Basics.AccountingJournals.ServiceFacade.WebApi
{
    /// <summary>
    /// AccountingJournalsController
    /// </summary>
    [RoutePrefix("basics/accountingJournals")]
    public class AccountingJournalsController : ApiControllerBase<AccountingJournalsLogic>
    {
        /// <summary>
        /// Update a regiontype
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("update")]
        public AccountingJournalsCompleteDto Update(AccountingJournalsCompleteDto dto)
        {
            var enti = dto.Copy();
            Logic.SaveAccountingJournals(enti);
            return new AccountingJournalsCompleteDto(enti);
        }

        /// <summary>
        /// Returns a list of AccountingJournals 
        /// </summary>
        /// <returns></returns>
        [Route("list")]
		[HttpPost]
		public Dictionary<string, object> GetAccountingJournals([FromBody] FilterRequest<Int32> filterRequest)
        {
			var filterResult = new FilterResponse<int>();
			var execInfo = new FilterExecutionInfo<int>(filterRequest, filterResult);
			var orderType = new FilterRequest<int>.OrderType("Id", true);
			FilterRequest<int>.OrderType[] orderBy = new FilterRequest<int>.OrderType[] { orderType };
			filterRequest.OrderBy = orderBy;
			var entities = this.Logic.GetListBySearchFilter(filterRequest, ref filterResult, execInfo);
			var dtos = entities.ToDtos(e => new AccountingJournalsDto(e));
            var jsdata = dtos.CollectLookups<AccountingJournalsDto>(collector => collector
                             .Add("CompanyPeriod", x => x.CompanyPeriodFk));
			jsdata.Add("FilterResult", filterResult);
            return jsdata;
        }

        /// <summary>
        /// Create an empty transheader
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("create")]
        public AccountingJournalsDto Create()
        {
            var CompanyID = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
            var dto = new AccountingJournalsDto();
            dto.Id = Logic.GetNewCompanyTransheaderId();
            dto.CompanyFk = CompanyID;
            dto.TradingPeriodStartDate = "";
            dto.TradingPeriodEndDate = "";
            dto.PostingDate = DateTime.Now;
            var transactionTypeDefaultProvider = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValues<IDefaultEntityProvider>("basics.customize.transactiontype").FirstOrDefault();
            if (transactionTypeDefaultProvider != null)
            {
                var defaultValue = transactionTypeDefaultProvider.GetDefault(null);
                if (defaultValue != null)
                {
                    dto.TransactionTypeFk = defaultValue.Id;
                }
            }
            var defaultStatus = new Company.BusinessComponents.CompanyTransHeaderStatusLogic().GetDefaultStatus();
            if (defaultStatus != null)
            {
                dto.CompanyTransheaderStatusFk = defaultStatus.Id;
            }
            return dto;
        }

        /// <summary>
        /// Delete an existing transheader
        /// </summary>
        /// <returns>The Id of the deleted transheader</returns>
        [HttpPost]
        [Route("delete")]
        public int Delete(AccountingJournalsDto dto)
        {
            if (dto == null) return -1;
            return Logic.Delete(dto.Id);
        }

    }
}
