//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.Api.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Basics.Api.BusinessComponents.DdTempIdsEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("BAS_DDTEMPIDS")]
    public partial class DdTempIdsEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new DdTempIdsEntity object.
        /// </summary>
        public DdTempIdsEntity()
        {
            OnConstruct(); // call partial constructor if present             
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for RequestId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQUESTID", TypeName = "char(32)", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string RequestId
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 1)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id
        {
            get;
            set;
        }


        #endregion
    
        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            DdTempIdsEntity obj = new DdTempIdsEntity();
            obj.RequestId = RequestId;
            obj.Id = Id;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(DdTempIdsEntity clonedEntity);

    }


}
