using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using RIB.Visual.Awp.Main.BusinessComponents;
using RIB.Visual.Platform.ServiceFacade.WebApi;

namespace RIB.Visual.Awp.Main.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	[RoutePrefix("awp/main/packageitemassignment")]
	public class AwpMainPackageItemAssignmentController : ApiControllerBase<AwpMainPackageItemAssignmentLogic>
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="filterData"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("getPrcItemAssignments")]
		public IDictionary<string, object> GetPrcItemAssignments(FilterData filterData)
		{
			return this.Logic.GetPrcItemAssignments(filterData);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("getbyproject")]
		public IEnumerable<object> GetByProject(int projectId)
		{
			return AwpMainPackageItemAssignmentLogic.GetByProjectId(projectId).Select(x => new
			{
				x.Id,
				x.PrcPackageFk,
				x.EstLineItemFk,
				x.EstHeaderFk,
				x.EstResourceFk,
				x.BoqHeaderFk,
				x.BoqItemFk,
				x.PrcItemFk,
				x.PrcItemAssignmentFk,
			});
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="packageId"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("getbypackage")]
		public IEnumerable<object> GetByPackage(int packageId)
		{
			return AwpMainPackageItemAssignmentLogic.GetByPackageIds(new List<int>() { packageId}).Select(x => new
			{
				x.Id,
				x.PrcPackageFk,
				x.EstLineItemFk,
				x.EstHeaderFk,
				x.EstResourceFk,
				x.BoqHeaderFk,
				x.BoqItemFk,
				x.PrcItemFk,
				x.PrcItemAssignmentFk,
			});
		}
	}
}
