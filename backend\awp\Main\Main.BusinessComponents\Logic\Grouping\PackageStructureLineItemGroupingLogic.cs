using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.OperationalManagement;

namespace RIB.Visual.Awp.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class PackageStructureLineItemGroupingLogic : GroupingHierarchyLogic
	{
		private readonly string CodeGroupingName = "Code";

		private int _maxStructureLevel = 1;

		private readonly int UnassignedBoqItemId = -2;

		/// <summary>
		/// This method is used to create a request for the grouping of package structure line items.
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public IEnumerable<GroupingStructureNode> GenerateHierarchyNew(PackageStructureLineItemGroupingRequest request)
		{
			var codeColumn = request.GroupingColumns.FirstOrDefault(e => e.GroupColumnId == CodeGroupingName);

			// group by Project BoQ
			var prjBoqGroupColumn = request.GroupingColumns.FirstOrDefault(e => e.GroupColumnId == GroupingProviderConst.GroupingProviderBoqItem);

			if (prjBoqGroupColumn != null)
			{
				var prjBoqStructureNodes = Execute(request.EstHeaderId, "EST_LI_GROUP_BY_PRJ_BOQ", PackageStructureLineItemGroupingType.ProjectBoQ);

				if(codeColumn != null)
				{
					AttachLineItemNodes(prjBoqStructureNodes, request.EstHeaderId, e => (e.BoqHeaderFk, e.BoqItemFk));
				}

				return prjBoqStructureNodes.Where(e => !e.EntityParentFk.HasValue).ToList();
			}

			// group by WIC BoQ
			var wicBoqGroupColumn = request.GroupingColumns.FirstOrDefault(e => e.GroupColumnId == GroupingProviderConst.GroupingProviderWicBoqItem);

			if (wicBoqGroupColumn != null)
			{
				var wicBoqStructureNodes = Execute(request.EstHeaderId, "EST_LI_GROUP_BY_WIC_BOQ", PackageStructureLineItemGroupingType.WICBoQ);

				if(codeColumn != null)
				{
					AttachLineItemNodes(wicBoqStructureNodes, request.EstHeaderId, e => (e.WicBoqHeaderFk, e.WicBoqItemFk));
				}

				return wicBoqStructureNodes.Where(e => !e.EntityParentFk.HasValue).ToList();
			}

			// group by Code
			var structureLevel = GetStructureLevel();

			var lineItemNodes = Injector.Get<IEstimateMainLineItemLogic>().GetList(request.EstHeaderId, false).Select(i => new GroupingStructureNode()
			{
				Id = i.Id,
				EntityId = i.Id,
				EntityHeaderFK = i.EstHeaderFk,
				BoqHeaderFk = i.BoqHeaderFk,
				BoqItemFk = i.BoqItemFk,
				Code = i.Code,
				Description = i.DescriptionInfo?.Description,
				Quantity = i.Quantity,
				WqQuantity = i.WqQuantityTarget,
				QuantityTotal = i.QuantityTotal,
				BasUomFk = i.BasUomTargetFk,
				PrcStructureFk = i.PrcStructureFk,
				GrandCostUnitTarget = i.GrandCostUnitTarget,
				GrandTotal = i.GrandTotal,
				GroupingType = PackageStructureLineItemGroupingType.LineItemCode,
				StructureLevel = structureLevel,
			}).ToList();

			SetPrcItemAssignmentCount(request.EstHeaderId, lineItemNodes);

			return lineItemNodes;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="estHeaderId"></param>
		/// <param name="spName"></param>
		/// <param name="itemGroupingType"></param>
		/// <returns></returns>
		public IEnumerable<GroupingStructureNode> Execute(int estHeaderId, string spName, PackageStructureLineItemGroupingType itemGroupingType)
		{
			using (var dbcontext = new DbContext(ModelBuilder.DbModel))
			{
				var rootItems = new List<GroupingStructureNode>();

				var entities = dbcontext.ExecuteStoredProcedure<GroupingStructureNode>(spName, estHeaderId).ToList();

				//because the id(-1) is used in client side as the virtual root node, then we need to set the id to -2
				var unassignedBoqItemId = entities.Find(e => e.Id == -1);

				if (unassignedBoqItemId != null)
				{
					unassignedBoqItemId.Id = UnassignedBoqItemId;
					unassignedBoqItemId.EntityId = UnassignedBoqItemId;
					unassignedBoqItemId.BoqHeaderFk = -1;
				}

				var maxStructureLevel = GetStructureLevel();

				//set grouping type
				foreach (var entity in entities)
				{
					entity.GroupingType = itemGroupingType;
					entity.StructureLevel = maxStructureLevel;
					entity.BoqHeaderFk = entity.EntityHeaderFK;
					entity.BoqItemFk = entity.EntityParentFk;
					entity.Reference = entity.Code;
					entity.BoqLineTypeFk = entity.LineTypeFk;
				}

				var id2EntityMap = entities.GroupBy(e => (e.EntityHeaderFK, e.Id )).ToDictionary(bi => bi.Key, e => e.First());

				// Builds the tree
				foreach (var entity in entities)
				{
					if (entity.EntityParentFk == null)
					{
						rootItems.Add(entity);
					}
					else if (id2EntityMap.TryGetValue((entity.EntityHeaderFK, entity.EntityParentFk.Value), out var parentItem))
					{
						if(parentItem.Children == null)
						{
							parentItem.Children = new List<GroupingStructureNode>();
						}

						parentItem.Children.Add(entity);
					}
				}

				// Grand Cost Total Summary
				SummaryGrandCostTotal(rootItems);

				SummaryAssignmentCount(rootItems);

				SetAssignmentLineItemIds(entities);

				return entities;
			}
		}

		private int GetStructureLevel()
		{
			return _maxStructureLevel++;
		}

		private void SummaryGrandCostTotal(IEnumerable<GroupingStructureNode> structureNodes)
		{
			if(structureNodes == null || !structureNodes.Any())
			{
				return;
			}

			foreach (var node in structureNodes)
			{
				if(node.Children != null && node.Children.Any())
				{
					SummaryGrandCostTotal(node.Children);
					if (!RIB.Visual.Boq.Main.BusinessComponents.BoqLineTypeHelper.IsLeaf(node.LineTypeFk))
					{
						node.Quantity = null;
						node.UnitRate = null;
					}
					node.GrandTotal = node.Children.Sum(e => e.GrandTotal ?? 0);
				}
			}
		}

		private void SummaryAssignmentCount(IEnumerable<GroupingStructureNode> structureNodes)
		{
			if (structureNodes == null || !structureNodes.Any())
			{
				return;
			}

			foreach (var node in structureNodes)
			{
				if (node.Children != null && node.Children.Any())
				{
					SummaryAssignmentCount(node.Children);
					node.AssignmentCount = node.Children.Sum(e => e.AssignmentCount ?? 0);
					node.UnAssignmentCount = node.Children.Sum(e => e.UnAssignmentCount ?? 0);
				}
			}
		}

		private static void SetAssignmentLineItemIds(IEnumerable<GroupingStructureNode> structureNodes)
		{
			if (structureNodes == null || !structureNodes.Any())
			{
				return;
			}

			foreach (var node in structureNodes.Where(e => !string.IsNullOrWhiteSpace(e.AssignmentLineItemIds)).ToList())
			{
				node.AssignmentLineItemIdsList = node.AssignmentLineItemIds.Split(',').Select(int.Parse);

				node.AssignmentLineItemIds = string.Empty;
			}

			foreach (var node in structureNodes.Where(e => !string.IsNullOrWhiteSpace(e.UnAssignmentLineItemIds)).ToList())
			{
				node.UnAssignmentLineItemIdsList = node.UnAssignmentLineItemIds.Split(',').Select(int.Parse);

				node.UnAssignmentLineItemIds = string.Empty;
			}
		}

		private void AttachLineItemNodes(IEnumerable<GroupingStructureNode> boqNodes, int estHeaderId, Func<IEstLineItemEntity, (int?, int?)> groupbyFunc)
		{
			if (boqNodes == null || !boqNodes.Any())
			{
				return;
			}

			var maxId = boqNodes.Max(e => e.Id) + 10000;

			var structureLevel = GetStructureLevel();

			var boqId2LineItemsMap = Injector.Get<IEstimateMainLineItemLogic>().GetList(estHeaderId, false).GroupBy(groupbyFunc).ToDictionary(e => e.Key, e => e.Select(i => new GroupingStructureNode()
			{
				Id = i.Id,
				EntityId = i.Id,
				EntityHeaderFK = i.EstHeaderFk,
				BoqHeaderFk = i.BoqHeaderFk,
				BoqItemFk = i.BoqItemFk,
				Code = i.Code,
				Description = i.DescriptionInfo?.Description,
				Quantity = i.Quantity,
				WqQuantity = i.WqQuantityTarget,
				QuantityTotal = i.QuantityTotal,
				BasUomFk = i.BasUomTargetFk,
				PrcStructureFk = i.PrcStructureFk,
				GrandCostUnitTarget = i.GrandCostUnitTarget,
				GrandTotal = i.GrandTotal,
				GroupingType = PackageStructureLineItemGroupingType.LineItemCode,
				StructureLevel = structureLevel,
			}).ToList());

			var lineItemNodes = boqId2LineItemsMap.Values.SelectMany(e => e.ToList()).ToList();

			// set PrcItem assignment count
			SetPrcItemAssignmentCount(estHeaderId, lineItemNodes);

			// rebuild lineItemNode Id
			foreach ( var lineItemNode in lineItemNodes)
			{
				lineItemNode.Id = ++maxId;
			}

			// rebuild lineItemNode Parent Id
			foreach ( var boqNode in boqNodes)
			{
				if(boqId2LineItemsMap.ContainsKey((boqNode.EntityHeaderFK, boqNode.EntityId)))
				{
					boqId2LineItemsMap.TryGetValue((boqNode.EntityHeaderFK, boqNode.EntityId), out var lineItemNodesList);

					if (lineItemNodesList != null && lineItemNodesList.Any())
					{
						foreach (var lineItemNode in lineItemNodesList)
						{
							lineItemNode.EntityParentFk = boqNode.EntityId;
						}

						if (boqNode.Children == null)
						{
							boqNode.Children = new List<GroupingStructureNode>();
						}

						boqNode.Children.AddRange(lineItemNodesList);
					}
				}

				// unassign boqNode
				if (boqNode.EntityId == UnassignedBoqItemId)
				{
					boqId2LineItemsMap.TryGetValue((null, null), out var lineItemNodesList);

					if (lineItemNodesList != null && lineItemNodesList.Any())
					{
						foreach (var lineItemNode in lineItemNodesList)
						{
							lineItemNode.EntityParentFk = boqNode.EntityId;
						}

						boqNode.Children.AddRange(lineItemNodesList);
					}
				}
			}
		}

		private static void SetPrcItemAssignmentCount(int estHeaderId, IEnumerable<GroupingStructureNode> lineItemNodes)
		{
			if(lineItemNodes == null || !lineItemNodes.Any())
			{
				return;
			}

			var lineItem2AssignmentCountMap = Injector.Get<IPrcItemAssignmentLogic>().GetEntitiesByEstHeaderId(estHeaderId).GroupBy(e => e.EstLineItemFk).ToDictionary(e => e.Key, e => e.Count());

			foreach (var lineItem in lineItemNodes)
			{
				if (lineItem2AssignmentCountMap.ContainsKey(lineItem.EntityId))
				{
					lineItem.AssignmentCount = lineItem2AssignmentCountMap[lineItem.EntityId];
				}
				else
				{
					lineItem.AssignmentCount = 0;
				}
			}
		}

		/// <summary>
		/// This method takes the request from the call, 
		/// analyses the grouping and output columns and creates an dynamic sql statement  out of it.
		/// It returnes the grouped result with the calculated output column according the defined group columns.
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public IEnumerable<PackageStructureLineItemNode> GenerateHierarchy(PackageStructureLineItemGroupingRequest request)
		{
			var result = DoGroup(request);

			var rootGrpHierarchyItemList = ConvertGrpItemsToNode(result.GrpItems, result.AggregateColumns, result.GroupingSqlBuilder);

			var itemList = rootGrpHierarchyItemList.Flatten(e => e.Children).ToList();

			// get lineitem information from the output columns
			foreach (var item in itemList)
			{
				ProcessOutputColumns(item);
			}

			if (request.GroupingColumns != null && request.GroupingColumns.Any())
			{
				// add the grouping columns to the result
				foreach (var groupingColumn in request.GroupingColumns.Where(e => e.GroupColumnId == GroupingProviderConst.GroupingProviderBoqItem || e.GroupColumnId == GroupingProviderConst.GroupingProviderWicBoqItem).ToList())
				{
					var itemsWithBoqFk = itemList.Where(e => (e.GroupColumnId == GroupingProviderConst.GroupingProviderBoqItem || e.GroupColumnId == GroupingProviderConst.GroupingProviderWicBoqItem) && e.EntityId.HasValue).ToList();

					var boqItemIds = itemsWithBoqFk.Select(e => e.EntityId.Value).Distinct().ToList();

					if (boqItemIds.Any())
					{
						var boqItemId2Entity = Injector.Get<IBoqItemLogic>().GetBoqItemsByBoqItemIds(boqItemIds).GroupBy(e => e.Id).ToDictionary(e => e.Key, e => e.First());

						foreach (var itemWithBoqFk in itemsWithBoqFk.Where(e => e.EntityId.HasValue && boqItemId2Entity.ContainsKey(e.EntityId.Value)).ToList())
						{
							var currentBoqItem = boqItemId2Entity[itemWithBoqFk.EntityId.Value];

							itemWithBoqFk.Quantity = currentBoqItem.Quantity;

							itemWithBoqFk.BasUomFk = currentBoqItem.BasUomFk;

							itemWithBoqFk.UnitRate = currentBoqItem.Price;

							itemWithBoqFk.FinalPrice = currentBoqItem.Finalprice;

							itemWithBoqFk.WqQuantity = null;

							itemWithBoqFk.QuantityTotal = null;

							itemWithBoqFk.PrcStructureFk = null;
						}
					}
				}
			}

			return rootGrpHierarchyItemList;
		}

		private static void ProcessOutputColumns(PackageStructureLineItemNode node)
		{
			if (node.OutputColumns != null && node.OutputColumns.Any() && node.OutputColumns.Length >= 7)
			{
				node.Quantity = node.OutputColumns[0] as decimal?;
				node.WqQuantity = node.OutputColumns[1] as decimal?;
				node.QuantityTotal = node.OutputColumns[2] as decimal?;
				node.BasUomFk = node.OutputColumns[3] as int?;
				node.GrandCostUnitTarget = node.OutputColumns[4] as decimal?;
				node.GrandTotal = node.OutputColumns[5] as decimal?;
				node.PrcStructureFk = node.OutputColumns[6] as int?;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="rootItemList"></param>
		/// <param name="aggregateColumns"></param>
		/// <param name="sqlbuilder"></param>
		/// <returns></returns>
		public static IEnumerable<PackageStructureLineItemNode> ConvertGrpItemsToNode(IList<GrpItem> rootItemList, IList<AggregateSqlColumnInfo> aggregateColumns, GroupingSqlBuilder sqlbuilder)
		{
			IList<PackageStructureLineItemNode> result = new List<PackageStructureLineItemNode>();

			foreach (var i in rootItemList)
			{
				var grpHierNode = new PackageStructureLineItemNode(i, null, sqlbuilder);

				AddChildrenRecursive(i, grpHierNode, aggregateColumns, sqlbuilder);

				result.Add(grpHierNode);
			}

			return result;
		}

		/// <summary>
		/// This method converts an internal GroupItem (GrpItem) to an hierarchical Groupitem recursive.
		/// It iterate thru the tree from the start node grpItem
		/// 
		/// </summary>
		/// <param name="grpItem">Startnode for recursion</param>
		/// <param name="grpNode"></param>
		/// <param name="aggregateColumns"></param>
		/// <param name="sqlbuilder"></param>
		private static void AddChildrenRecursive(GrpItem grpItem, PackageStructureLineItemNode grpNode, IList<AggregateSqlColumnInfo> aggregateColumns, GroupingSqlBuilder sqlbuilder)
		{
			if (grpItem.Children == null || !grpItem.Children.Any())
			{
				return;
			}

			foreach (var child in grpItem.Children)
			{
				var grpHierNode = new PackageStructureLineItemNode(child, grpNode, sqlbuilder);

				if (child.Children == null || !child.Children.Any()) // no further recursion
				{
					continue;
				}

				AddChildrenRecursive(child, grpHierNode, aggregateColumns, sqlbuilder);
			}
		}

		/// <summary>
		/// Handles the standard group for the PackageStructureLineItem.
		/// </summary>
		/// <param name="grpSqlCol"></param>
		/// <param name="groupCol"></param>
		/// <param name="modelColInfo"></param>
		protected override GroupingSqlColumnInfo HandleStandardGroup(GroupingSqlColumnInfo grpSqlCol, GroupColumn groupCol, ModelMappingInfo modelColInfo)
		{
			grpSqlCol.SqlAlias = GroupingSqlBuilder.RootTableAlias;
			grpSqlCol.GroupByColumnList = new List<GroupingColumnDsc>()
			{
				new GroupingColumnDsc("Id"),
				new GroupingColumnDsc(grpSqlCol.EntityColumnName, grpSqlCol.EntityColumnName)
			};
			grpSqlCol.OrderByColumnList = new List<GroupingColumnDsc>
			{
				new GroupingColumnDsc(grpSqlCol.EntityColumnName)
			};
			// handle DateOptions, only valid for a DateTime Property
			if (modelColInfo.IsDateTime)
			{
				grpSqlCol.DateOption = string.IsNullOrWhiteSpace(groupCol.DateOption)
					? GroupingConst.DateOptionDate
					: groupCol.DateOption;
			}

			return grpSqlCol;
		}

		/// <summary>
		/// The method take the dynamic row from the datareader, loops over all returned fields (dynamic values) and assigns the value
		/// a new GrpItem, The type of GrpItem is determined by the sqlGroupingColumns and aggregateColumns.
		/// First all sqlGroupingColumns will be processed and then the aggregateColumns
		/// 
		/// </summary>
		/// <param name="lastRootItem"></param>
		/// <param name="rootGrpItems"></param>
		/// <param name="row"></param>
		/// <param name="sqlGroupingColumns"></param>
		/// <param name="aggregateColumns"></param>
		protected override void ReadRowIntoGroupColumns(
			ref GrpItem lastRootItem,
			IList<GrpItem> rootGrpItems,
			DataRecordDynamicWrapper row,
			IList<GroupingSqlColumnInfo> sqlGroupingColumns,
			IList<AggregateSqlColumnInfo> aggregateColumns)
		{

			var dataRecord = row.DataRecord;
			GrpItem item = null;
			GrpItem itemParent = null;

			var rowIdx = 0;
			var level = 1;

			foreach (var groupCol in sqlGroupingColumns)
			{
				switch (groupCol.GroupType)
				{
					case GroupingConst.GroupColTypeStandard:
						item = new GrpItem(dataRecord[rowIdx++], dataRecord[rowIdx++].ToString(), null, null, level, groupCol.GroupColumnId, groupCol.GroupType, groupCol.DateOption);
						ProcessItem(item, ref lastRootItem, ref itemParent, level, rootGrpItems);

						break;
					case GroupingConst.GroupColTypeReference:
						if (groupCol.DateOption != null)
						{
							item = new GrpItem(dataRecord[rowIdx++], null, null, null, level, groupCol.GroupColumnId, groupCol.GroupType, groupCol.DateOption);
							if (groupCol.HasSortColumn) { rowIdx++; } // skip sorting syntheticolumn
							ProcessItem(item, ref lastRootItem, ref itemParent, level, rootGrpItems);
						}
						else
						{
							item = new GrpItem(dataRecord[rowIdx++], dataRecord[rowIdx++].ToString(), dataRecord[rowIdx++].ToString(), MakeIntNullable(dataRecord[rowIdx++]), level, groupCol.GroupColumnId, groupCol.GroupType);
							if (groupCol.HasSortColumn) { rowIdx++; } // skip sorting syntheticolumn
							ProcessItem(item, ref lastRootItem, ref itemParent, level, rootGrpItems);
						}
						break;
					case GroupingConst.GroupColTypeHierarchy:
						for (var hLevel = 1; hLevel <= groupCol.Depth; hLevel++)
						{
							item = new GrpItem(dataRecord[rowIdx++], dataRecord[rowIdx++].ToString(), dataRecord[rowIdx++].ToString(), MakeIntNullable(dataRecord[rowIdx++]), level, groupCol.GroupColumnId, groupCol.GroupType);

							ProcessItem(item, ref lastRootItem, ref itemParent, level, rootGrpItems);

							if (hLevel < groupCol.Depth) { level++; }  //  increment level only if not last
						}
						break;
					default:
						throw new BusinessLayerException("ReadRowIntoGroupColumns(): Wrong type of GroupType found. " + groupCol.GroupType.ToString());
				}

				level++;
			}

			// last item of all group item eats aggregate output columns
			var outColumnItem = item;  // this item eats the aggregate output columns, it the one on the lowest level

			if (outColumnItem == null)
			{
				return;
			}

			outColumnItem.OutputColumns = new dynamic[aggregateColumns.Count];
			var outColsIdx = 0;

			outColumnItem.Count = dataRecord[rowIdx++] as dynamic;  // eat first outcolumn into count

			foreach (var groupCol in aggregateColumns)
			{
				switch (groupCol.AggregateFunction)
				{
					case GroupingConst.AggregateFctMax:
					case GroupingConst.AggregateFctMin:
					case GroupingConst.AggregateFctSum:
						outColumnItem.OutputColumns[outColsIdx++] = dataRecord[rowIdx++];
						break;
					case GroupingConst.AggregateFctAvg:
						// cater aggregate Sum into a AverageItem.
						outColumnItem.OutputColumns[outColsIdx++] = new AverageItem(outColumnItem, dataRecord[rowIdx++]);
						break;
				}
			}
		}

	}
}
