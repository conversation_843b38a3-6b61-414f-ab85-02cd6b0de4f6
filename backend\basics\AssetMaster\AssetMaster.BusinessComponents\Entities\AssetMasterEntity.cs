using RIB.Visual.Platform.Core;
using RIB.Visual.Basics.Core.Core;
using System.ComponentModel.DataAnnotations.Schema;

namespace RIB.Visual.Basics.AssetMaster.BusinessComponents
{

	/// <summary/>
	public partial class AssetMasterEntity : IIdentifyable, IAssetMasterEntity
	{
		/// <summary>
		/// MatrialCount
		/// </summary>
		[NotMapped]
		public int AssetMasterCount { get; set; }

		/// <summary>
		/// AddressEntity
		/// </summary>
		[NotMapped]
		public RIB.Visual.Basics.Common.BusinessComponents.AddressEntity AddressEntity { get; set; }
	}
}
