﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="RIB.Visual.Platform.AppServer.Console.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <appSettings>
    <clear/>
  </appSettings>
  <connectionStrings>
    <add connectionString="Server=ribsqlprdagw002.database.windows.net;Initial Catalog=itwocloud;Persist Security Info=False;User ID=itwo40developer;Password=****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;" name="Default" />
    <add connectionString="Server=.\;Database=iTWOCloud;Integrated Security=true" name="Default.local" />
  </connectionStrings>
</configuration>