﻿<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="BusinessComponents.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2008" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:devart="http://devart.com/schemas/edml/StorageSchemaExtensions/1.0" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityContainer Name="DbContextStoreContainer">
          <EntitySet Name="BAS_DDTEMPIDS" EntityType="BusinessComponents.Store.BAS_DDTEMPIDS" store:Type="Tables" Table="BAS_DDTEMPIDS" />
          <EntitySet Name="BAS_APIREQUESTITEMs" EntityType="BusinessComponents.Store.BAS_APIREQUESTITEM" store:Type="Tables" Table="BAS_APIREQUESTITEM" />
        </EntityContainer>
        <EntityType Name="BAS_DDTEMPIDS">
          <Key>
            <PropertyRef Name="REQUESTID" />
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="REQUESTID" Type="char" Nullable="false" MaxLength="32" />
          <Property Name="ID" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="BAS_APIREQUESTITEM">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="REQUESTID" Type="varchar" Nullable="false" MaxLength="32" />
          <Property Name="MODULENAME" Type="varchar" Nullable="false" MaxLength="252" />
          <Property Name="ITEMDATA" Type="text" />
          <Property Name="STATUS" Type="int" Nullable="false" DefaultValue="0" />
          <Property Name="VALIDUNTIL" Type="datetime" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="REQUESTCONTEXT" Type="text" />
        </EntityType>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="RIB.Visual.Basics.Api.BusinessComponents" Alias="Self" d4p1:ViewGeneration="true" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:devart="http://devart.com/schemas/edml/ConceptualSchemaExtensions/1.0" xmlns:ed="http://devart.com/schemas/EntityDeveloper/1.0" annotation:UseStrongSpatialTypes="false" xmlns:d4p1="http://devart.com/schemas/edml/ConceptualSchemaExtensions/1.0" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="ModelBuilder" ed:Namespace="RIB.Visual.Basics.Api.BusinessComponents" annotation:LazyLoadingEnabled="false" ed:Guid="2c8b7c01-1421-4d2d-b86b-3d5c5b084643">
          <EntitySet Name="DdTempIdsEntities" EntityType="RIB.Visual.Basics.Api.BusinessComponents.DdTempIdsEntity" />
          <EntitySet Name="ApiRequestItemEntities" EntityType="RIB.Visual.Basics.Api.BusinessComponents.ApiRequestItemEntity" />
        </EntityContainer>
        <EntityType Name="DdTempIdsEntity" ed:Guid="6902c783-fe1a-4395-9200-d7ba04c3ee9a" ed:GenerateEntityCode="True" ed:GenerateDTO="False">
          <Key>
            <PropertyRef Name="RequestId" />
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="RequestId" Type="String" Nullable="false" MaxLength="32" FixedLength="true" ed:ValidateMaxLength="32" ed:ValidateRequired="True" ed:Guid="fadd9d49-dad6-4f9d-aaea-b56f8bdf6269" />
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="06c83e50-aa8d-4e66-b740-2bac6cd8ce19" />
        </EntityType>
        <EntityType Name="ApiRequestItemEntity" ed:Guid="8c85498e-a48a-40be-8202-498a5a863c1c">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="655f7228-2e3d-4bd7-9f99-a2f9f5b9da66" />
          <Property Name="RequestId" Type="String" Nullable="false" MaxLength="32" ed:ValidateMaxLength="32" ed:ValidateRequired="True" ed:Guid="92bb87a8-72d6-46c5-9c9f-79bbceef3617" />
          <Property Name="ModuleName" Type="String" Nullable="false" MaxLength="252" ed:ValidateMaxLength="252" ed:ValidateRequired="True" ed:Guid="a70c7795-2c4f-4a92-948e-5942fdbf488d" />
          <Property Name="RequestContext" Type="String" ed:ValidateRequired="False" ed:Guid="a6c84a9d-4bf2-4316-bedb-6c25647d6f1a" />
          <Property Name="ItemData" Type="String" ed:ValidateRequired="False" ed:Guid="2cb55f44-ea8d-4c7b-b746-ddd072052d1a" />
          <Property Name="Status" Type="Int32" Nullable="false" DefaultValue="0" ed:ValidateRequired="True" ed:Guid="5b4d7e0f-b127-4496-8de0-55bf1cd7f369" />
          <Property Name="ValidUntil" Type="DateTime" ed:ValidateRequired="False" ed:Guid="8317306f-604c-4db4-acc7-c85e552aa3b6" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="1d5c6eb6-9b35-4444-8886-ac1bebf7d5cd" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="False" ed:Guid="fa1aa935-90a2-4518-b01c-4d70b0ebfd24" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="True" ed:Guid="d6510131-7a85-4e6f-9f07-c054e1ae9893" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="False" ed:Guid="d9e77720-5767-464e-9a4a-d228e1b1c713" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="409d475a-a0f3-478b-ba41-eeab5bf8d7fd" />
        </EntityType>
        <ComplexType Name="DescriptionTranslateType" ed:Guid="d503cfc6-f9ef-411c-af64-0e66c76468fc" ed:GenerateDTO="True" ed:GenerateOnlyMapping="True">
          <Property Name="Description" Type="String" ed:ValidateRequired="False" ed:Guid="69283371-dcfd-487a-8900-4a9e28ba8105" />
          <Property Name="DescriptionTr" Type="Int32" ed:ValidateRequired="False" ed:Guid="7019d3f9-9cd2-4cd7-bd4a-5e52a2d17a1c" />
        </ComplexType>
      </Schema>
    </edmx:ConceptualModels>
    <!-- MSL content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="DbContextStoreContainer" CdmEntityContainer="ModelBuilder">
          <EntitySetMapping Name="DdTempIdsEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Basics.Api.BusinessComponents.DdTempIdsEntity">
              <MappingFragment StoreEntitySet="BAS_DDTEMPIDS">
                <ScalarProperty Name="RequestId" ColumnName="REQUESTID" />
                <ScalarProperty Name="Id" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ApiRequestItemEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Basics.Api.BusinessComponents.ApiRequestItemEntity">
              <MappingFragment StoreEntitySet="BAS_APIREQUESTITEMs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="RequestId" ColumnName="REQUESTID" />
                <ScalarProperty Name="Status" ColumnName="STATUS" />
                <ScalarProperty Name="ValidUntil" ColumnName="VALIDUNTIL" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="ItemData" ColumnName="ITEMDATA" />
                <ScalarProperty Name="ModuleName" ColumnName="MODULENAME" />
                <ScalarProperty Name="RequestContext" ColumnName="REQUESTCONTEXT" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <edmx:Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </edmx:Connection>
    <edmx:Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="false" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
      </DesignerInfoPropertySet>
    </edmx:Options>
    <edmx:Diagrams>
      <Diagram Name="Main" />
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>