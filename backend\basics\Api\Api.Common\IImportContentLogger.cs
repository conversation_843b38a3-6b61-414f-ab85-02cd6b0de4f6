using RIB.Visual.Basics.Core.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.Threading;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// 
	/// </summary>
	public interface IImportContentLogger : IPublicApiExecutionLogger
	{

		/// <summary>
		/// 
		/// </summary>
		void StartWrite(ImportMasterDataContext context);


		/// <summary>
		/// 
		/// </summary>
		void FinishAndWaitCompleted();

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		bool HasError();

		#region Properties

		/// <summary>
		/// The validation result.
		/// </summary>
		IEnumerable<PublicApiValidationResult> ValidationResults { get; set; }

		#endregion


	}
}
