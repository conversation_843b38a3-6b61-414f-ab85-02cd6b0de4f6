using System;
using System.IO;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using IdentityModel.Client;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.Common;
using RIB.Visual.Services.Platform.BusinessComponents;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RIB.Visual.Platform.Server.Common;
using System.Net.Http;
using System.Net;
using System.Web;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public class CatlProfile
	{
		/// <summary>
		/// 
		/// </summary>
		public AttributeProfile attributes { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string id { get; set; }
	}

	/// <summary>
	/// 
	/// </summary>
	public class AttributeProfile
	{
		/// <summary>
		/// 
		/// </summary>
		public string account_no { get; set; }
	}


	/// <summary>
	/// </summary>
	internal class CatlFactory : TokenFactoryBase
	{
		private string _clientId = String.Empty;
		private string _clientSecret = String.Empty;
		private string _tokenEndPoint = String.Empty;
		private string _userinfoEndPoint = string.Empty;
		private string _redirectURI = string.Empty;

		/// <summary>
		/// reads the configuration parameters from the web.config file of WEB-Server
		/// 
		/// <example>
		/// <code>
		///		<appSettings>
		///			<add key="catl:sso.clientId" value=""/>
		///			<add key="catl:sso.clientSecret" value=""/>
		///			<add key="catl:sso.tokenEndPoint" value=""/>
		///			<add key="catl:sso.userinfoEndPoint" value=""/>
		///			<add key="catl:sso.redirectURI" value=""/>
		///		</appSettings>
		/// </code>
		/// </example>
		/// </summary>
		protected override void ReadConfig()
		{
			_clientId = AppSettingsReader.ReadString("catl:sso.clientId");
			_clientSecret = AppSettingsReader.ReadString("catl:sso.clientSecret");
			_tokenEndPoint = AppSettingsReader.ReadString("catl:sso.tokenEndPoint");
			_userinfoEndPoint = AppSettingsReader.ReadString("catl:sso.userinfoEndPoint");
			_redirectURI = AppSettingsReader.ReadString("catl:sso.redirectURI");

			_idpId = AppSettingsReader.ReadInt("idm:identityproviderid");
		}

		/// <summary>
		/// </summary>
		/// <param name="ticket"></param>
		/// <returns></returns>
		protected override ValidateResult ValidateTicket(string ticket)
		{
			ValidateResult validateResult = new ValidateResult() { StatusCode = HttpStatusCode.Unauthorized };
			string redirectUrl = HttpUtility.UrlEncode(_redirectURI);

			var tokenUrl = $"{_tokenEndPoint}?grant_type=authorization_code&client_id={_clientId}" +
				$"&client_secret={_clientSecret}&code={ticket}&redirect_uri={redirectUrl}";

			using (HttpClient client = new HttpClient())
			{
				var response = client.PostAsync(tokenUrl,null).Result;
				validateResult.StatusCode = response.StatusCode;
				if (response.StatusCode == HttpStatusCode.OK)
				{
					var result = response.Content.ReadAsStringAsync().Result;
					JObject jObject = JObject.Parse(result);
					string accessToken = jObject["access_token"].ToString();

					var userProfileUrl = $"{_userinfoEndPoint}?access_token={accessToken}";				
					response = client.GetAsync(userProfileUrl).Result;
					validateResult.StatusCode = response.StatusCode;
					result = response.Content.ReadAsStringAsync().Result;
					if (response.StatusCode == HttpStatusCode.OK)
					{
						var profile = JsonConvert.DeserializeObject<CatlProfile>(result);
						if (null != profile && null != profile.attributes)
						{
							validateResult.LogonName = profile.attributes.account_no;
						}
					}
					else
					{
						jObject = JObject.Parse(result);
						validateResult.ErrorCode = jObject["error"].ToString();
					}
				}			
			}
			
			return validateResult;
		}
	}
}
