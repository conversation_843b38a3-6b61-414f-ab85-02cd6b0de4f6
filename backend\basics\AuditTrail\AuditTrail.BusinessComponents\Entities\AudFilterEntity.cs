﻿using RIB.Visual.Platform.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;

namespace RIB.Visual.Basics.AuditTrail.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public partial class AudFilterEntity
	{

        /// <summary>
        /// 
        /// </summary>
        [DomainName(Name = "integer")]
        public int? ObjectFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[DomainName(Name = "integer")]
		public int? RecordFk { get; set; }

		/// <summary>
		/// mandatory
		/// </summary>
		[DomainName(Name = "datetime")]
		public DateTime DateFrom { get; set; }

		/// <summary>
		/// mandatory
		/// </summary>
		[DomainName(Name = "datetime")]
		public DateTime DateTo { get; set; }

		/// <summary>
		/// mandatory
		/// </summary>
		public string[] ContainerList { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string[] ColumnList { get; set; }

		/// <summary>
		/// A/U/D >> All/Modified/Deleted 
		/// mandatory
		/// </summary>
		public string Action { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string LogOnNameContains { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [DomainName(Name = "integer")]
        public int PageNumber { get; set; }

	}
}
