﻿using System.Collections.Generic;
using System.ComponentModel.Composition;
using Newtonsoft.Json.Linq;

using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Core.BusinessComponents;

namespace RIB.Visual.Basics.AssetMaster.BusinessComponents
{
    /// <summary>
    /// 
    /// </summary>
	[Export("basics.assetmaster", typeof(ILayoutContainerDefinition))]
	public class ContainerDefinitionExporter : ILayoutContainerDefinition
	{
		System.Collections.Generic.IEnumerable<JObject> ILayoutContainerDefinition.ContainerDefinition
		{
			get
			{
				var containers = new List<JObject>();
				var collector = new DynamicEntityUsageCollector();
				var usages = collector.ForFilter<IBelongsToAsset>();
				foreach (var usage in usages)
				{
					if (!string.IsNullOrEmpty(usage.GridLayoutUid))
					{
						dynamic containerGrid = new JObject();
						containerGrid.id = usage.NewGridLayoutUid;
						containerGrid.template = "app/components/base/grid-partial.html";
						containerGrid.title = usage.GridContainerTitle;
						containerGrid.controller = "basicsAssetMasterBelongsToListController";
						containerGrid.layout = usage.NewGridLayoutUid;
						containerGrid.uuid = usage.NewGridLayoutUid;
						containerGrid.permission = usage.PermissionUid;
						containers.Add(containerGrid);
					}

					if (!string.IsNullOrEmpty(usage.DetailLayoutUid))
					{
						dynamic containerDetail = new JObject();
						containerDetail.id = usage.NewDetailLayoutUid;
						containerDetail.template = "app/components/base/form-detail-partial.html";
						containerDetail.title = usage.DetailContainerTitle;
						containerDetail.controller = "basicsAssetMasterBelongsToDetailController";
						containerDetail.layout = usage.NewDetailLayoutUid;
						containerDetail.uuid = usage.NewDetailLayoutUid;
						containerDetail.permission = usage.PermissionUid;
						containers.Add(containerDetail);
					}
				}

				return containers;
			}
		}
	}
}
