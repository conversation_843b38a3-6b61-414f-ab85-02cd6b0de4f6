using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Platform.Core;

namespace RIB.Visual.Awp.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class AwpMainPackageItemAssignmentLogic : LogicBase
	{
		/// <summary>
		/// get prcItem assignments
		/// </summary>
		/// <param name="filterData "></param>
		/// <returns></returns>
		public IDictionary<string, object> GetPrcItemAssignments(FilterData filterData)
		{
			var entities = GetPrcItemAssignmentsByFilter(filterData);

			var boqItems = FillWithExtendInfo(entities);

			return new Dictionary<string, object>()
				{
					{ "dtos", entities },
					{ "packageboqItems", boqItems }
				};
		}

		/// <summary>
		/// get prcItem assignments by filter
		/// </summary>
		/// <param name="filterData"></param>
		/// <returns></returns>
		private static IEnumerable<IPrcItemAssignmentEntity> GetPrcItemAssignmentsByFilter(FilterData filterData)
		{
			var defaultVal = new List<IPrcItemAssignmentEntity>();

			if(filterData == null)
			{
				return defaultVal;
			}

			var prcItemAssignmentLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IPrcItemAssignmentLogic>();

			// get prcItem assignment by position boq
			if (filterData.BoqHeaderId.HasValue && filterData.BoqItemId.HasValue && filterData.PrcPackageFk.HasValue)
			{
				return prcItemAssignmentLogic.GetPrcItemAssignmentsByBoqIds(filterData.EstHeaderFk, filterData.PrcPackageFk.Value, new List<RIB.Visual.Platform.Core.IdentificationData>()
				{
					new Platform.Core.IdentificationData()
					{
						Id = filterData.BoqItemId.Value,
						PKey1 = filterData.BoqHeaderId.Value,
					}
				});
			}

			// get prcItem assignment by prcItem
			if (filterData.PrcItemFk.HasValue && filterData.PrcPackageFk.HasValue)
			{
				return prcItemAssignmentLogic.GetPrcItemAssignmentsByPrcItemIds(filterData.EstHeaderFk, filterData.PrcPackageFk.Value, new List<long>()
				{
					filterData.PrcItemFk.Value
				});
			}

			// get prcItem assignment by lineitem or resources
			var filterDataConversionEntity = new AwpMainPackageItemAssignmentHelper().GetFilterLineItemAndResoure(filterData);

			if (filterDataConversionEntity.LineItemIdData != null)
			{
				return prcItemAssignmentLogic.GetItemAssignmentsByLineItemIdData(filterDataConversionEntity.LineItemIdData, true).ToList();
			}

			if (filterDataConversionEntity.ResourceIdData != null)
			{
				return prcItemAssignmentLogic.GetItemAssignmentsByResourceIdData(filterDataConversionEntity.ResourceIdData, true).ToList();
			}

			return defaultVal;
		}

		private static IEnumerable<IBoqItemEntity> FillWithExtendInfo(IEnumerable<IPrcItemAssignmentEntity> entities)
		{
			if(entities == null || !entities.Any())
			{
				return Enumerable.Empty<IBoqItemEntity>();
			}

			var boqHeaderIds = entities.CollectIds(e => e.BoqHeaderFk);
			var pacakgeIds = entities.Select(e => e.PrcPackageFk);
			var boqItemIds = entities.CollectIds(e => e.BoqItemFk);

			var context = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;

			IEnumerable<IBoqItemEntity> boqItems = null;
			IEnumerable<IBoqItemEntity> boqRootItems = null; // use root item id as boq header fk
			IEnumerable<IPrcPackageEntity> packages = null;

			var parallelOptions = new ParallelOptions { MaxDegreeOfParallelism = Math.Max(1, Environment.ProcessorCount / 2) };
			Parallel.Invoke(parallelOptions,
				() =>
				{
					Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext = context;
					boqItems = Injector.Get<IBoqItemLogic>().GetBoqItemsByBoqItemIds(boqItemIds);
				},
				() =>
				{
					Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext = context;
					boqRootItems = Injector.Get<IBoqItemLogic>().GetBoqRootItemsAsInterface(boqHeaderIds);
				},
				() =>
				{
					Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext = context;
					packages = Injector.Get<IPrcPackageLogic>().GetPackagesByIds(pacakgeIds);
				});

			var packageStatuss = new BasicsCustomizePackageStatusLogic().GetByIds(packages.Select(e => e.PackageStatusFk).ToList()).Select(e => (BasicsCustomizePackageStatusEntity)e);

			foreach (var item in entities)
			{
				if (item.BoqHeaderFk.HasValue)
				{
					var rootItem = boqRootItems.FirstOrDefault(e => e.BoqHeaderFk == item.BoqHeaderFk);

					if (rootItem != null)
					{
						item.BoqHeaderReference = rootItem.Reference;
					}
				}

				var package = packages.FirstOrDefault(e => item.PrcPackageFk == e.Id);

				if (package != null)
				{
					item.PackageCode = package.Code;
					item.PackageStatusFk = package.PackageStatusFk;
				}

				var packageStatus = packageStatuss.FirstOrDefault(e => e.Id == package.PackageStatusFk);

				if (packageStatus != null && packageStatus.IsContracted)
				{
					item.IsPackageStatusContracted = packageStatus.IsContracted;
				}
			}

			return boqItems ?? new List<IBoqItemEntity>();
		}

		/// <summary>
		/// get prcItem assignment by project id
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public static IEnumerable<IPrcItemAssignmentEntity> GetByProjectId(int projectId)
		{
			var packages = BusinessApplication.BusinessEnvironment.GetExportedValue<IPrcPackageLogic>().GetPrcPackageByProjectId(projectId);

			var packageIds = packages.Select(e => e.Id).ToList();

			return BusinessApplication.BusinessEnvironment.GetExportedValue<IPrcItemAssignmentLogic>().GetEntitiesByPackageIds(packageIds);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="packageIds"></param>
		/// <returns></returns>
		public static IEnumerable<IPrcItemAssignmentEntity> GetByPackageIds(IEnumerable<int> packageIds)
		{
			return BusinessApplication.BusinessEnvironment.GetExportedValue<IPrcItemAssignmentLogic>().GetEntitiesByPackageIds(packageIds);
		}
	}
}
