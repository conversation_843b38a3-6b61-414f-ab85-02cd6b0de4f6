using System.Net;
using Newtonsoft.Json.Linq;

namespace RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample
{
	/// <summary>
	/// 
	/// </summary>
	public class Response
	{
		/// <summary>
		/// 
		/// </summary>
		// ReSharper disable once UnusedAutoPropertyAccessor.Global
		public bool Result { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string ResponseValue { get; set; }
		
		// ReSharper disable once UnusedAutoPropertyAccessor.Global
		/// <summary>
		/// 
		/// </summary>
		public JObject ObjValue { get; set; }

		public JArray ArrValue { get; set; }

		// Status Code
		public HttpStatusCode StatusCode { get; set; }
	}
}