﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace RIB.Visual.Basics.Api.Common
{
		/// <summary>
	/// Single Sign On Data Transfer Object Class
	/// used to supply required info for establishing the signon 
	/// via multiple options
	/// 
	/// </summary>
	public class SsoInfo
	{
		/// <summary/>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public string Username { get; set; }
		/// <summary/>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public string Password { get; set; }
		/// <summary/>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public string ShortTermToken { get; set; }
		/// <summary/>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public string Ticket { get; set; }
		/// <summary/>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public string CompanyCode { get; set; }
		/// <summary>
		/// myHome 
		/// </summary>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public string EncryptedUsername { get; set; }
	}

}
