﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Represents a composite unique key, which could have one or more in a object.
	/// </summary>
	[AttributeUsage(AttributeTargets.Property)]
	public class CompositeUniqueKeyAttribute : Attribute
	{
		/// <summary>
		/// Contructor.
		/// </summary>
		public CompositeUniqueKeyAttribute()
		{
		}
	}
}
