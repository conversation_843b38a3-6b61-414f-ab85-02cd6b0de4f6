﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.BillingSchema.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Basics.BillingSchema.BusinessComponents.RubricCategoryEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("BAS_RUBRIC_CATEGORY")]
    public partial class RubricCategoryEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new RubricCategoryEntity object.
        /// </summary>
        public RubricCategoryEntity()
        {
          this.DescriptionInfo = new DescriptionTranslateType();
          this.IsLive = true;
            OnConstruct(); // call partial constructor if present
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for RubricFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_RUBRIC_FK", TypeName = "int", Order = 1)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int RubricFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DescriptionInfo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 2, TranslationColumnName = "DESCRIPTION_TR")]
        public virtual DescriptionTranslateType DescriptionInfo {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Sorting in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SORTING", TypeName = "int", Order = 4)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Sorting {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsLive in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISLIVE", TypeName = "bit", Order = 8)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsLive {
            get; set;
        }


        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            RubricCategoryEntity obj = new RubricCategoryEntity();
            obj.Id = Id;
            obj.RubricFk = RubricFk;
            obj.DescriptionInfo = (DescriptionTranslateType)DescriptionInfo.Clone();
            obj.Sorting = Sorting;
            obj.IsLive = IsLive;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(RubricCategoryEntity clonedEntity);

    }


}
