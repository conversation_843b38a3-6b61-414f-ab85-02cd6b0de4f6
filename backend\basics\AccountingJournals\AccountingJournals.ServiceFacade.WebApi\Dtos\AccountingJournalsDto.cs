﻿using RIB.Visual.Basics.Company.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.AppServer.Runtime;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.AccountingJournals.ServiceFacade.WebApi
{
    /// <summary>
    /// 
    /// </summary>
    public class AccountingJournalsDto
    {
        #region Constructors
       
        /// <summary>
        /// Initializes an instance of class CompanyTransheaderDto.
        /// </summary>
        public AccountingJournalsDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class CompanyTransheaderDto.
        /// </summary>
        /// <param name="entity">the instance of class CompanyTransheaderEntity</param>
        public AccountingJournalsDto(CompanyTransheaderEntity entity)
        {          
            CompanyFk = entity.CompanyFk;
            CompanyPeriodFk = entity.CompanyPeriodFk;
            TransactionTypeFk = entity.TransactionTypeFk;
            Id = entity.Id;
            Description = entity.Description;
            PostingDate = entity.PostingDate;
            CommentText = entity.CommentText;
            ReturnValue = entity.ReturnValue;
            IsSuccess = entity.IsSuccess;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;
			BasCompanyTransheader = entity.BasCompanyTransheader;
			BasCompanyTransheaderFk = entity.CompanyTransheaderFk;
            CompanyTransheaderStatusFk = entity.CompanyTransheaderStatusFk;

            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion 

        #region Properties

        /// <summary>
        /// Gets or Sets CompanyThransheaderId.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }

        /// <summary>
        /// Gets or Sets CompanyFk.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyFk { get; set; }

        /// <summary>
        /// Gets or Sets CompanyYearFk.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyYearFk { get; set; }

        /// <summary>
        /// Gets or Sets CompanyPeriodFk.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyPeriodFk { get; set; }

        /// <summary>
        /// Gets or Sets TradingYear.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int TradingYear { get; set; }

        /// <summary>
        /// Gets or Sets TradingPeriod.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int TradingPeriod { get; set; }

        /// <summary>
        /// Gets or Sets TradingPeriodStartDate.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"code")]
        public string TradingPeriodStartDate { get; set; }

        /// <summary>
        /// Gets or Sets TradingPeriodEndDate.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"code")]
        public string TradingPeriodEndDate { get; set; }

        /// <summary>
        /// Gets or Sets TransactionTypeFk.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int TransactionTypeFk { get; set; }

        /// <summary>
        /// Gets or Sets TransactionTypeDescription.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        [System.ComponentModel.DataAnnotations.StringLength(42)]
        public string TransactionTypeDescription { get; set; }

        /// <summary>
        /// Gets or Sets TransactionTypeAbbreviation.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"code")]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string TransactionTypeAbbreviation { get; set; }

        /// <summary>
        /// Gets or Sets CompanyTransStatusFk.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyTransheaderStatusFk
        {
            get;
            set;
        }

        /// <summary>
        /// Gets or Sets Description.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        [System.ComponentModel.DataAnnotations.StringLength(42)]
        public string Description { get; set; }

        /// <summary>
        /// Gets or Sets PostingDate.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"dateutc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public DateTime PostingDate { get; set; }

        /// <summary>
        /// Gets or Sets CommentText.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"comment")]
        [System.ComponentModel.DataAnnotations.StringLength(255)]
        public string CommentText { get; set; }

        /// <summary>
        /// Gets or Sets ReturnValue.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"remark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ReturnValue { get; set; }

        /// <summary>
        /// Gets or Sets IsSuccess.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"boolean")]
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Gets or Sets InsertedAt.
        /// </summary>
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public global::System.DateTime InsertedAt { get; set; }

        /// <summary>
        /// Gets or Sets InsertedBy.
        /// </summary>
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }

        /// <summary>
        /// Gets or Sets UpdatedAt.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Gets or Sets UpdatedBy.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// Gets or Sets Version.
        /// </summary>
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string BasCompanyTransheader { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? BasCompanyTransheaderFk { get; set; }

        #endregion

		/// <param name="entity"></param>
        private void OnConstruct(CompanyTransheaderEntity entity)
        {
            if (entity.CompanyPeriodFk >= 0)
            {
                var companyPeriodLogic = new BasicsCompanyPeriodLogic();
                var companyPeriodEntity = companyPeriodLogic.GetPeriodById(entity.CompanyPeriodFk);
                TradingPeriod = companyPeriodEntity.TradingPeriod;
                TradingPeriodStartDate = companyPeriodEntity.StartDate.ToShortDateString();
                TradingPeriodEndDate = companyPeriodEntity.EndDate.ToShortDateString();
                if (companyPeriodEntity.CompanyYearFk >= 0)
                {
                    var companyYearLogic = new BasicsCompanyYearLogic();
                    var companyYearEntity = companyYearLogic.GetItemByKey(companyPeriodEntity.CompanyYearFk);
                    CompanyYearFk = companyYearEntity.Id;
                    TradingYear = companyYearEntity.TradingYear;
                }
            }
            if (entity.TransactionTypeFk >= 0)
            {
                var description = this.GetFieldById(entity.TransactionTypeFk, "DESCRIPTION");
                var avvreviation = this.GetFieldById(entity.TransactionTypeFk, "ABBREVIATION");
                TransactionTypeDescription = description;
                TransactionTypeAbbreviation = avvreviation;
            }
        }

        private string GetFieldById(int id, string searchField)
        {
            var lookupQualifier = "basics.customize.transactiontype";
            var simpleLookupDescriptorFactory = BusinessApplication.BusinessEnvironment.GetExportedValue<ISimpleLookupDescriptorFactory>();
            var simpleLookupDescriptor = simpleLookupDescriptorFactory.CreateCustom(lookupQualifier, searchField);
            var simpleLookupValueProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<ISimpleLookupValueProvider>();
            var list = simpleLookupValueProvider.GetList(simpleLookupDescriptor);
            var item = list.Where(e => e.Id == id).FirstOrDefault();
            return item == null ? "" : item.DisplayMember;
        }
    }
}
