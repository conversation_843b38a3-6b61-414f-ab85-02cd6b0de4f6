﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RIB.Visual.Basics.Api.ServiceFacade.WebApi</RootNamespace>
    <AssemblyName>RIB.Visual.Basics.Api.ServiceFacade.WebApi</AssemblyName>
    <TargetFramework>net8.0</TargetFramework>
    <FileAlignment>512</FileAlignment>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation />
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\RIB.Visual.Basics.Api.ServiceFacade.WebApi.XML</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <DocumentationFile>bin\Release\RIB.Visual.Basics.Api.ServiceFacade.WebApi.XML</DocumentationFile>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <!--
    <Reference Include="Microsoft.IdentityModel.Tokens">
      <HintPath>$(RIBvisualBinPool)\Microsoft.IdentityModel.Tokens.dll</HintPath>
      <Private>False</Private>
    </Reference>
	-->
    <Reference Include="RIB.Visual.Platform.AppServer.Web">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Server.Common">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Server.Common.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Platform.ServiceFacade.WebApi">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.OperationalManagement">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.OperationalManagement.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.Common.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.ServiceFacade.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Platform.AppServer.Runtime">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Mvc.WebApiCompatShim">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Microsoft.AspNetCore.Mvc.WebApiCompatShim.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.SqlServer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Formatting">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Net.Http.Formatting.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="IdentityModel">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\IdentityModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IdentityModel.Tokens.Jwt">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.IdentityModel.Tokens.Jwt.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Microsoft.IdentityModel.Tokens.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Microsoft.IdentityModel.Logging.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Caching">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Runtime.Caching.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Configuration.ConfigurationManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.CodeDom">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.CodeDom.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.SqlClient">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Data.SqlClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Common">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Drawing.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{B4F97281-0DBD-4835-9ED8-7DFB966E87FF}" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Controllers\BasicsApiControllerInquiry.cs" />
    <Compile Include="Controllers\BasicsApiSchemaController.cs" />
    <Compile Include="Controllers\BasicsSsoController.cs" />
    <Compile Include="Controllers\ImportMasterDataContentBase.cs" />
    <Compile Include="Controllers\InquiryRequest.cs" />
    <Compile Include="Controllers\PublicApiModelValidator.cs" />
    <Compile Include="Controllers\PublicApiControllerBase.cs" />
    <Compile Include="Controllers\SsoTokenResponse.cs" />
    <Compile Include="Dtos\ApiRequestItemDto.cs" />
    <Compile Include="Dtos\ApiRequestItemDto.Generated.cs">
      <DependentUpon>ApiRequestItemDto.cs</DependentUpon>
    </Compile>
    <Compile Include="Dtos\PublicApiDefaultRequest.cs" />
    <Compile Include="Dtos\PublicApiFilterRequest.cs" />
    <Compile Include="Dtos\PublicApiGetDataByIdRequest.cs" />
    <Compile Include="Dtos\PublicApiGetDataRequest.cs" />
    <Compile Include="Dtos\PublicApiRequestBase.cs" />
    <Compile Include="Controllers\PublicApiRequestHandler.cs" />
    <Compile Include="Dtos\PublicApiSearchRequest.cs" />
    <Compile Include="Dtos\TranslateText.cs" />
    <Compile Include="Dtos\TranslateTextDescriptorApiDto.cs" />
    <Compile Include="Other\ExecuteRequestContext.cs" />
    <Compile Include="Other\IdentityServerLogon.cs" />
    <Compile Include="Other\NsccFactory.cs" />
    <Compile Include="Other\CatlFactory.cs" />
    <Compile Include="Other\SimpleFairFactory.cs" />
    <Compile Include="Other\MyHomeFactory.cs" />
    <Compile Include="Other\SsoFactory.cs" />
    <Compile Include="Other\TokenFactoryBase.cs" />
    <Compile Include="Other\TokenResponseStaticMethods.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="../../../AssemblyVersion.cs" Link="Properties/AssemblyVersion.cs" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Controllers\BasicsApiController.cs" />
    <None Include="RIBvisual.snk" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Scripts\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Api.BusinessComponents\RIB.Visual.Basics.Api.BusinessComponents.csproj">
      <Project>{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}</Project>
      <Name>RIB.Visual.Basics.Api.BusinessComponents</Name>
    </ProjectReference>
    <ProjectReference Include="..\Api.Common\RIB.Visual.Basics.Api.Common.csproj">
      <Project>{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}</Project>
      <Name>RIB.Visual.Basics.Api.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Api.Core\RIB.Visual.Basics.Api.Core.csproj">
      <Project>{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}</Project>
      <Name>RIB.Visual.Basics.Api.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\Api.Localization\RIB.Visual.Basics.Api.Localization.csproj">
      <Project>{74E96892-0A0F-4141-A99A-B13E6F2AB479}</Project>
      <Name>RIB.Visual.Basics.Api.Localization</Name>
    </ProjectReference>
  </ItemGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F" />
  </Target>
</Project>