//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Basics.AuditTrail.BusinessComponents;

namespace RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi
{

    /// <summary>
    /// Represents a Dto class.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("AUD_COLUMN")]
    public partial class AudColumnDto : RIB.Visual.Platform.Core.ITypedDto<AudColumnEntity>
    {
        #region Constructors
       
        /// <summary>
        /// Initializes an instance of class AudColumnDto.
        /// </summary>
        public AudColumnDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class AudColumnDto.
        /// </summary>
        /// <param name="entity">the instance of class AudColumnEntity</param>
        public AudColumnDto(AudColumnEntity entity)
        {
            Id = entity.Id;
            AudTableFk = entity.AudTableFk;
            Columnname = entity.Columnname;
            Isenabletracking = entity.Isenabletracking;
            Isdeleted = entity.Isdeleted;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;
            
            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion 

        #region Properties

        /// <summary>
        /// Gets or Sets Id.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }

        /// <summary>
        /// Gets or Sets AudTableFk.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AUD_TABLE_FK", TypeName = "int", Order = 1)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int AudTableFk { get; set; }

        /// <summary>
        /// Gets or Sets Columnname.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("COLUMNNAME", TypeName = "varchar(32)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string Columnname { get; set; }

        /// <summary>
        /// Gets or Sets Isenabletracking.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISENABLETRACKING", TypeName = "bit", Order = 3)]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool Isenabletracking { get; set; }

        /// <summary>
        /// Gets or Sets Isdeleted.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISDELETED", TypeName = "bit", Order = 4)]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool Isdeleted { get; set; }

        /// <summary>
        /// Gets or Sets InsertedAt.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 5)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public global::System.DateTime InsertedAt { get; set; }

        /// <summary>
        /// Gets or Sets InsertedBy.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 6)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }

        /// <summary>
        /// Gets or Sets UpdatedAt.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 7)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Gets or Sets UpdatedBy.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// Gets or Sets Version.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 9)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary />
        public AudTableDto AudTableEntity { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(AudColumnEntity); }
        }

        /// <summary>
        /// Copy the current AudColumnDto instance to a new AudColumnEntity instance.
        /// </summary>
        /// <returns>a new instance of class AudColumnEntity</returns>
        public AudColumnEntity Copy() 
        {
          var entity = new AudColumnEntity();

          entity.Id = this.Id;
          entity.AudTableFk = this.AudTableFk;
          entity.Columnname = this.Columnname;
          entity.Isenabletracking = this.Isenabletracking;
          entity.Isdeleted = this.Isdeleted;
          entity.InsertedAt = this.InsertedAt;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedAt = this.UpdatedAt;
          entity.UpdatedBy = this.UpdatedBy;
          entity.Version = this.Version;

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(AudColumnEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(AudColumnEntity entity);
    }

}
