﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{C73FC722-EB0C-4A2B-8A18-58477ED9F080}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RIB.Visual.Basics.AssetMaster.ServiceFacade.WebApi</RootNamespace>
    <AssemblyName>RIB.Visual.Basics.AssetMaster.ServiceFacade.WebApi</AssemblyName>
    <TargetFramework>net8.0</TargetFramework>
    <FileAlignment>512</FileAlignment>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation />
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\RIB.Visual.Basics.AssetMaster.ServiceFacade.WebApi.XML</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Cloud.Common.BusinessComponents">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.AppServer.Web">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.ServiceFacade.WebApi">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.ServiceFacade.WebApi.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Mvc.WebApiCompatShim">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Microsoft.AspNetCore.Mvc.WebApiCompatShim.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.SqlServer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Formatting">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Net.Http.Formatting.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.CodeDom">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.CodeDom.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Configuration.ConfigurationManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.SqlClient">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Data.SqlClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Common">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Drawing.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{B4F97281-0DBD-4835-9ED8-7DFB966E87FF}" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Dtos\AssetMasterDto.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Dtos</DependentUpon>
    </Compile>
    <Compile Include="Dtos\AssetMasterDto.Generated.cs">
      <DependentUpon>AssetMasterDto.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="LookupServices\AssetMasterLookupService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="../../../AssemblyVersion.cs" Link="Properties/AssemblyVersion.cs" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Controllers\BasicsAssetMasterController.cs" />
    <None Include="RIBvisual.snk" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Scripts\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AssetMaster.BusinessComponents\RIB.Visual.Basics.AssetMaster.BusinessComponents.csproj">
      <Project>{BA7BE816-268A-40BB-88F9-06F6B1810778}</Project>
      <Name>RIB.Visual.Basics.AssetMaster.BusinessComponents</Name>
    </ProjectReference>
    <ProjectReference Include="..\AssetMaster.Common\RIB.Visual.Basics.AssetMaster.Common.csproj">
      <Project>{14D6ED46-6F19-4D07-98FA-5C85AD59BCE7}</Project>
      <Name>RIB.Visual.Basics.AssetMaster.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\AssetMaster.Core\RIB.Visual.Basics.AssetMaster.Core.csproj">
      <Project>{0535C6D7-5444-46C0-AD01-44666669338C}</Project>
      <Name>RIB.Visual.Basics.AssetMaster.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\AssetMaster.Localization\RIB.Visual.Basics.AssetMaster.Localization.csproj">
      <Project>{7F41F49B-780C-48CC-B35C-6B4C4D99CD79}</Project>
      <Name>RIB.Visual.Basics.AssetMaster.Localization</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F" />
  </Target>
</Project>