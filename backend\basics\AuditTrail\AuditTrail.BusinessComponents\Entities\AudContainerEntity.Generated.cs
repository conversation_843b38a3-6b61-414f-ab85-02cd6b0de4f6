//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.AuditTrail.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Basics.AuditTrail.BusinessComponents.AudContainerEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("AUD_CONTAINER")]
    public partial class AudContainerEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new AudContainerEntity object.
        /// </summary>
        public AudContainerEntity()
        {
          this.DescriptionInfo = new DescriptionTranslateType();
            this.AudContainer2AudTableEntities = new HashSet<AudContainer2AudTableEntity>();
            OnConstruct(); // call partial constructor if present             
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ContainerUuid in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"guid")]
        [RIB.Visual.Platform.Common.MappedColumn("CONTAINER_UUID", TypeName = "char(32)", Order = 1)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string ContainerUuid
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for DescriptionInfo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"translation")]
        public virtual DescriptionTranslateType DescriptionInfo
        {
            get;
            set;
        }


        #endregion

        #region Navigation Properties
    
        /// <summary>
        /// There are no comments for AudContainer2AudTableEntities in the schema.
        /// </summary>
        public virtual ICollection<AudContainer2AudTableEntity> AudContainer2AudTableEntities
        {
            get;
            set;
        }

        #endregion
    
        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            AudContainerEntity obj = new AudContainerEntity();
            obj.Id = Id;
            obj.ContainerUuid = ContainerUuid;
            obj.DescriptionInfo = (DescriptionTranslateType)DescriptionInfo.Clone();
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(AudContainerEntity clonedEntity);

    }


}
