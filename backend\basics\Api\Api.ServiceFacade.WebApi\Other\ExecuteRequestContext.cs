﻿using RIB.Visual.Platform.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	/// <typeparam name="TRequest"></typeparam>
	public class ExecuteRequestContext<TRequest>
	where TRequest : PublicApiRequestBase
	{
		/// <summary>
		/// Constructor
		/// </summary>
		public ExecuteRequestContext()
		{

		}

		/// <summary>
		/// 
		/// </summary>
		public TRequest Request
		{
			get;
			set;
		}

		/// <summary>
		/// 
		/// </summary>
		public Permissions? TargetPermission
		{
			get;
			set;
		}

		/// <summary>
		/// 
		/// </summary>
		public string ErrorMessage
		{
			get;
			set;
		}
	}
}
