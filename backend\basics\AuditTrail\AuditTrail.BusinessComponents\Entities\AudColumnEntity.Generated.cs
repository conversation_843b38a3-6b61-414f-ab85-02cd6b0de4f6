//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.AuditTrail.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Basics.AuditTrail.BusinessComponents.AudColumnEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("AUD_COLUMN")]
    public partial class AudColumnEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new AudColumnEntity object.
        /// </summary>
        public AudColumnEntity()
        {
          this.Isenabletracking = false;
          this.Isdeleted = false;
            OnConstruct(); // call partial constructor if present             
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for AudTableFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AUD_TABLE_FK", TypeName = "int", Order = 1)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int AudTableFk
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Columnname in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("COLUMNNAME", TypeName = "varchar(32)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string Columnname
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Isenabletracking in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISENABLETRACKING", TypeName = "bit", Order = 3)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool Isenabletracking
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Isdeleted in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISDELETED", TypeName = "bit", Order = 4)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool Isdeleted
        {
            get;
            set;
        }


        #endregion

        #region Navigation Properties
    
        /// <summary>
        /// There are no comments for AudTableEntity in the schema.
        /// </summary>
        public virtual AudTableEntity AudTableEntity
        {
            get;
            set;
        }

        #endregion
    
        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            AudColumnEntity obj = new AudColumnEntity();
            obj.Id = Id;
            obj.AudTableFk = AudTableFk;
            obj.Columnname = Columnname;
            obj.Isenabletracking = Isenabletracking;
            obj.Isdeleted = Isdeleted;
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(AudColumnEntity clonedEntity);

    }


}
