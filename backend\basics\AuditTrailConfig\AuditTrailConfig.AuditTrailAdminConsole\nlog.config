<?xml version="1.0" encoding="utf-8"?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" autoReload="true" throwExceptions="true" internalLogLevel="Off">
	<!-- optional, add some variables https://github.com/nlog/NLog/wiki/Configuration-file#variables -->
	<!--<variable name="logbasedir" value="App_Data/logs" />-->
	<!-- See https://github.com/nlog/nlog/wiki/Configuration-file for information on customizing logging rules and outputs.-->
	<!-- add your targets here. 
	See https://github.com/nlog/NLog/wiki/Targets for possible targets. 
	See https://github.com/nlog/NLog/wiki/Layout-Renderers for the possible layout renderers.-->
	<targets async="false">
		<target name="consoleTarget" xsi:type="Console" layout="${message}" />
	</targets>
	<time xsi:type="FastLocal" />
	<!--https://makolyte.com/nlog-log-to-console/-->
	<rules>
		<logger name="*" enabled="false" minlevel="Info" writeTo="consoleTarget" />
	</rules>
</nlog>