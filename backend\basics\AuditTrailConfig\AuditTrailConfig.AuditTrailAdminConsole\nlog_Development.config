<?xml version="1.0" encoding="utf-8"?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" autoReload="true" throwExceptions="true" internalLogLevel="Off">
	<!-- enable asp.net core layout renderers -->
	<extensions>
		<add assembly="NLog.Web.AspNetCore" />
		<add assembly="NLog.WindowsEventLog" />
	</extensions>
	<!-- optional, add some variables https://github.com/nlog/NLog/wiki/Configuration-file#variables -->
	<!--<variable name="logbasedir" value="App_Data/logs" />-->
	<!-- See https://github.com/nlog/nlog/wiki/Configuration-file for information on customizing logging rules and outputs.-->
	<!-- add your targets here. 
	See https://github.com/nlog/NLog/wiki/Targets for possible targets. 
	See https://github.com/nlog/NLog/wiki/Layout-Renderers for the possible layout renderers.-->
	<targets async="true">
		<target name="consoleTarget" xsi:type="Console" layout="${longdate} level=${level} message=${message}" />
	</targets>
	<time xsi:type="FastLocal" />
	<!--<time xsi:type="FastUTC" />-->
	<rules>
		<logger name="*" enabled="true" minlevel="Trace" writeTo="consoleTarget" />
	</rules>
</nlog>