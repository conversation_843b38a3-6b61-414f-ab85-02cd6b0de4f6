﻿/*
 * $Id: ApiDocIdRouteAttribute.cs 575790 2020-02-10 13:45:30Z haagf $
 * Copyright (c) RIB Software SE
 */

using System;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Indicates that a part of the endpoint URL route is formed by the IDs of the entity.
	/// </summary>
	[AttributeUsage(AttributeTargets.Method)]
	public class ApiDocIdRouteAttribute : ApiDocAttribute
	{
		/// <summary>
		/// Initializes a new instance.
		/// </summary>
		/// <param name="paramName">The name of the parameter that captures the route information.</param>
		/// <exception cref="ArgumentNullException"><paramref name="paramName"/> is <see langword="null"/>.</exception>
		public ApiDocIdRouteAttribute(String paramName = "id")
		{
			if (paramName == null)
			{
				throw new ArgumentNullException("paramName");
			}

			_paramName = paramName;
		}

		private readonly String _paramName;

		/// <summary>
		/// Indicates the name of the parameter that captures the route information.
		/// </summary>
		public String ParamName
		{
			get { return _paramName; }
		}

		/// <summary>
		/// The name of the generic type parameter whose concrete value is used to determine the ID fields.
		/// </summary>
		public String TypeParamName { get; set; }
	}
}
