/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable, inject } from '@angular/core';
import { BaseValidationService, ValidationInfo, ValidationResult, IValidationFunctions, IEntityRuntimeDataRegistry } from '@libs/platform/data-access';
import { PlatformTranslateService, ServiceLocator } from '@libs/platform/common';
import { IPrcInventoryHeaderEntity } from '../../model/entities/prc-inventory-header-entity.interface';
import { ProcurementInventoryHeaderDataService } from '../procurement-inventory-header-data.service';

/**
 * Validation service for procurement inventory header entities
 */
@Injectable({
	providedIn: 'root'
})
export class ProcurementInventoryHeaderValidationService extends BaseValidationService<IPrcInventoryHeaderEntity> {
	private readonly translationService = inject(PlatformTranslateService);
	private dataService?: ProcurementInventoryHeaderDataService;

	protected generateValidationFunctions(): IValidationFunctions<IPrcInventoryHeaderEntity> {
		return {
			PrjProjectFk: this.validatePrjProjectFk,
			InventoryDate: this.validateInventoryDate,
			TransactionDate: this.validateTransactionDate,
			PrcStockTransactionTypeFk: this.validatePrcStockTransactionTypeFk,
			PrjStockFk: this.validatePrjStockFk,
		};
	}

	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IPrcInventoryHeaderEntity> {
		// Use ServiceLocator to avoid circular dependency
		if (!this.dataService) {
			this.dataService = ServiceLocator.injector.get(ProcurementInventoryHeaderDataService);
		}
		return this.dataService;
	}

	/**
	 * Validates the project foreign key
	 */
	private validatePrjProjectFk = (info: ValidationInfo<IPrcInventoryHeaderEntity>): ValidationResult => {
		const entity = info.entity;
		const value = info.value as number;

		const fieldName = this.translationService.instant('procurement.inventory.header.entityProjectNo').toString();

		// Check if mandatory
		if (!value || value === 0) {
			return new ValidationResult(
				this.translationService.instant('cloud.common.emptyOrNullValueErrorMessage', { fieldName }).toString()
			);
		}

		// Reset stock if project changed
		if (value !== 0 && entity.PrjProjectFk !== value) {
			entity.PrjStockFk = null as any; // Use null to avoid constraint violations
			// Validate the reset stock field
			this.validatePrjStockFk({ entity, value: null, field: 'PrjStockFk' });
		}

		return new ValidationResult();
	};

	/**
	 * Validates the inventory date
	 */
	private validateInventoryDate = (info: ValidationInfo<IPrcInventoryHeaderEntity>): ValidationResult => {
		const entity = info.entity;
		const value = info.value as string;
		const fieldName = this.translationService.instant('procurement.inventory.header.inventoryDate').toString();

		// Check if mandatory
		if (!value) {
			return new ValidationResult(
				this.translationService.instant('cloud.common.emptyOrNullValueErrorMessage', { fieldName }).toString()
			);
		}

		// Set transaction date if not set
		if (value && !entity.TransactionDate) {
			entity.TransactionDate = value;
		}

		return new ValidationResult();
	};

	/**
	 * Validates the transaction date
	 */
	private validateTransactionDate = (info: ValidationInfo<IPrcInventoryHeaderEntity>): ValidationResult => {
		const value = info.value as string;
		const fieldName = this.translationService.instant('procurement.inventory.header.transactionDate').toString();

		// Check if mandatory
		if (!value) {
			return new ValidationResult(
				this.translationService.instant('cloud.common.emptyOrNullValueErrorMessage', { fieldName }).toString()
			);
		}

		return new ValidationResult();
	};

	/**
	 * Validates the stock transaction type foreign key
	 */
	private validatePrcStockTransactionTypeFk = (info: ValidationInfo<IPrcInventoryHeaderEntity>): ValidationResult => {
		const value = info.value as number;
		const fieldName = this.translationService.instant('procurement.inventory.header.prcStockTransactionTypeFk').toString();

		// Check if mandatory
		if (!value || value === 0 || value === null || value === undefined) {
			return new ValidationResult(
				this.translationService.instant('cloud.common.emptyOrNullValueErrorMessage', { fieldName }).toString()
			);
		}

		return new ValidationResult();
	};

	/**
	 * Validates the project stock foreign key
	 */
	private validatePrjStockFk = (info: ValidationInfo<IPrcInventoryHeaderEntity>): ValidationResult => {
		const value = info.value as number;
		const fieldName = this.translationService.instant('procurement.inventory.header.prjStockFk').toString();

		// Check if mandatory
		if (!value || value === 0) {
			return new ValidationResult(
				this.translationService.instant('cloud.common.emptyOrNullValueErrorMessage', { fieldName }).toString()
			);
		}

		return new ValidationResult();
	};
}
