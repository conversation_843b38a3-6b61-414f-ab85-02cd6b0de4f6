<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="BusinessComponents.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2008" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:devart="http://devart.com/schemas/edml/StorageSchemaExtensions/1.0" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityContainer Name="DbContextStoreContainer">
          <EntitySet Name="BAS_COMPANY_TRANSHEADER_Vs" EntityType="BusinessComponents.Store.BAS_COMPANY_TRANSHEADER_V" store:Type="Views" Table="BAS_COMPANY_TRANSHEADER_V" />
          <EntitySet Name="BAS_COMPANYTRNHDSTATHSTIES" EntityType="BusinessComponents.Store.BAS_COMPANYTRNHDSTATHSTY" store:Type="Tables" Table="BAS_COMPANYTRNHDSTATHSTY" />
        </EntityContainer>
        <EntityType Name="BAS_COMPANY_TRANSHEADER_V">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="BAS_COMPANY_FK" />
            <PropertyRef Name="BAS_COMPANY_PERIOD_FK" />
            <PropertyRef Name="POSTING_DATE" />
            <PropertyRef Name="BAS_TRANSACTIONTYPE_FK" />
            <PropertyRef Name="ISSUCCESS" />
            <PropertyRef Name="START_DATE" />
            <PropertyRef Name="END_DATE" />
            <PropertyRef Name="TRADING_PERIOD" />
            <PropertyRef Name="TRADING_YEAR" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="BAS_COMPANY_FK" Type="int" Nullable="false" />
          <Property Name="BAS_COMPANY_PERIOD_FK" Type="int" Nullable="false" />
          <Property Name="POSTING_DATE" Type="date" Nullable="false" />
          <Property Name="BAS_TRANSACTIONTYPE_FK" Type="int" Nullable="false" />
          <Property Name="ISSUCCESS" Type="bit" Nullable="false" />
          <Property Name="RETURN_VALUE" Type="nvarchar" MaxLength="2000" />
          <Property Name="BAS_COMPANY_TRANSHEADER_FK" Type="int" />
          <Property Name="COMMENT_TEXT" Type="nvarchar" MaxLength="255" />
          <Property Name="COMPANYTRANSHEADERSTATUSFK" Type="int" Nullable="false" />
          <Property Name="START_DATE" Type="date" Nullable="false" />
          <Property Name="END_DATE" Type="date" Nullable="false" />
          <Property Name="TRADING_PERIOD" Type="int" Nullable="false" />
          <Property Name="TRADING_YEAR" Type="int" Nullable="false" />
          <Property Name="TYPE_DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="TYPE_DESCRIPTION_TR" Type="int" />
          <Property Name="TYPE_ABBREVIATION" Type="nvarchar" MaxLength="16" />
        </EntityType>
        <EntityType Name="BAS_COMPANYTRNHDSTATHSTY">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="BAS_COMPANY_TRANSHEADER_FK" Type="int" Nullable="false" />
          <Property Name="BAS_COMPANYTRANSHDRSTAT_OLD_FK" Type="int" Nullable="false" />
          <Property Name="BAS_COMPANYTRANSHDRSTAT_NEW_FK" Type="int" Nullable="false" />
          <Property Name="REMARK" Type="nvarchar" MaxLength="2000" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="RIB.Visual.Basics.AccountingJournals.BusinessComponents" Alias="Self" d4p1:ViewGeneration="true" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:devart="http://devart.com/schemas/edml/ConceptualSchemaExtensions/1.0" xmlns:ed="http://devart.com/schemas/EntityDeveloper/1.0" annotation:UseStrongSpatialTypes="false" xmlns:d4p1="http://devart.com/schemas/edml/ConceptualSchemaExtensions/1.0" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="ModelBuilder" ed:Namespace="RIB.Visual.Basics.AccountingJournals.BusinessComponents" annotation:LazyLoadingEnabled="false" ed:Guid="2c8b7c01-1421-4d2d-b86b-3d5c5b084643">
          <EntitySet Name="CompanyTransHeaderVEntities" EntityType="RIB.Visual.Basics.AccountingJournals.BusinessComponents.CompanyTransHeaderVEntity" />
          <EntitySet Name="CompanyTransHeaderStatusHistoryEntities" EntityType="RIB.Visual.Basics.AccountingJournals.BusinessComponents.CompanyTransHeaderStatusHistoryEntity" />
        </EntityContainer>
        <EntityType Name="CompanyTransHeaderVEntity" ed:Guid="a24472b0-928b-427e-8b01-cfc9e178f71d">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="CompanyFk" />
            <PropertyRef Name="CompanyPeriodFk" />
            <PropertyRef Name="PostingDate" />
            <PropertyRef Name="TransactiontypeFk" />
            <PropertyRef Name="Issuccess" />
            <PropertyRef Name="StartDate" />
            <PropertyRef Name="EndDate" />
            <PropertyRef Name="TradingPeriod" />
            <PropertyRef Name="TradingYear" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="860ddb3f-9c03-4a67-ab1a-ab68df8042f8" />
          <Property Name="Description" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="False" ed:Guid="af8102f1-a4d5-47dc-8f99-193270b798eb">
          </Property>
          <Property Name="CompanyFk" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="ec9cf0d9-a0b9-48aa-b64d-6c8bc90c88f6" />
          <Property Name="CompanyPeriodFk" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="5e8e1f6f-36eb-4b8a-9ff8-67f7e3a724e2" />
          <Property Name="PostingDate" Type="DateTime" Nullable="false" ed:ValidateRequired="True" ed:Guid="19ea53e4-0b36-4cb9-ac48-e541f2d37049" />
          <Property Name="TransactiontypeFk" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="cbdf7b1a-3640-4bc3-98d2-311a49eacba3" />
          <Property Name="Issuccess" Type="Boolean" Nullable="false" ed:ValidateRequired="True" ed:Guid="ba1b5ab0-1344-4e77-ad25-0d2d15270173" />
          <Property Name="ReturnValue" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="False" ed:Guid="f9003224-424f-4dc1-b869-04de97b91a6c">
          </Property>
          <Property Name="CompanyTransheaderFk" Type="Int32" ed:ValidateRequired="False" ed:Guid="d6b0d51e-da7b-4904-8699-35887d5981ec" />
          <Property Name="CommentText" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="False" ed:Guid="5fb53135-e021-4ec1-9791-add8b180bbf5">
          </Property>
          <Property Name="CompanyTransHeaderStatusFk" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="269f865a-262d-44b4-aee3-4d02a9f3e1df" />
          <Property Name="StartDate" Type="DateTime" Nullable="false" ed:ValidateRequired="True" ed:Guid="273caf76-7db2-469b-8fdb-5277f4d52cda" />
          <Property Name="EndDate" Type="DateTime" Nullable="false" ed:ValidateRequired="True" ed:Guid="f251d3ac-9c31-4517-91a4-4e63f6eb53c1" />
          <Property Name="TradingPeriod" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="82c478f3-9635-49af-8199-38b269ddb8b9" />
          <Property Name="TradingYear" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="3c3f6af9-f780-41bb-84a5-cd8332c599ff" />
          <Property Name="TypeDescriptionInfo" Type="RIB.Visual.Basics.AccountingJournals.BusinessComponents.DescriptionTranslateType" Nullable="false" ed:ValidateRequired="False" ed:Guid="fc862a9c-21db-4c47-9585-662ee27fb91c" />
          <Property Name="TypeAbbreviation" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="False" ed:Guid="0c752999-6030-4faa-b3c5-277a59a5f308">
          </Property>
        </EntityType>
        <EntityType Name="CompanyTransHeaderStatusHistoryEntity" ed:Guid="b5e89205-eb2c-4592-a6a9-cc012b3973c9">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="c34d7ea4-2af6-4634-b763-a75f1d561e85" />
          <Property Name="CompanyTransheaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="afc03993-26e1-458c-9d3c-311efec613f1" />
          <Property Name="BasCompanytranshdrstatOldFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="b2c6704b-3768-46a5-b02c-014fd1fb3549" />
          <Property Name="BasCompanytranshdrstatNewFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="6b4e0119-9f29-4f02-a37c-4391321ce795" />
          <Property Name="Remark" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="dbe8924b-a13b-486a-b69d-fe5e3cd99353">
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="3cd62bb1-9a93-45a4-9ffb-6f0403335ebe" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="dde04ad3-60b2-4c7c-a891-a7b4b51d7188" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="c857f1d3-a4d4-4bd6-baff-8d09338d996c" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="b4a497b6-869e-466a-bac2-bfdc81591dac" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="0f0aed88-e1be-417d-9a84-dc5e6447a9b2" />
        </EntityType>
        <ComplexType Name="DescriptionTranslateType" ed:Guid="d503cfc6-f9ef-411c-af64-0e66c76468fc" ed:GenerateOnlyMapping="True">
          <Property Name="Description" Type="String" ed:ValidateRequired="false" ed:Guid="69283371-dcfd-487a-8900-4a9e28ba8105" />
          <Property Name="DescriptionTr" Type="Int32" ed:ValidateRequired="false" ed:Guid="7019d3f9-9cd2-4cd7-bd4a-5e52a2d17a1c" />
        </ComplexType>
      </Schema>
    </edmx:ConceptualModels>
    <!-- MSL content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="DbContextStoreContainer" CdmEntityContainer="ModelBuilder">
          <EntitySetMapping Name="CompanyTransHeaderVEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Basics.AccountingJournals.BusinessComponents.CompanyTransHeaderVEntity">
              <MappingFragment StoreEntitySet="BAS_COMPANY_TRANSHEADER_Vs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                <ScalarProperty Name="CompanyFk" ColumnName="BAS_COMPANY_FK" />
                <ScalarProperty Name="CompanyPeriodFk" ColumnName="BAS_COMPANY_PERIOD_FK" />
                <ScalarProperty Name="PostingDate" ColumnName="POSTING_DATE" />
                <ScalarProperty Name="TransactiontypeFk" ColumnName="BAS_TRANSACTIONTYPE_FK" />
                <ScalarProperty Name="Issuccess" ColumnName="ISSUCCESS" />
                <ScalarProperty Name="ReturnValue" ColumnName="RETURN_VALUE" />
                <ScalarProperty Name="CompanyTransheaderFk" ColumnName="BAS_COMPANY_TRANSHEADER_FK" />
                <ScalarProperty Name="CommentText" ColumnName="COMMENT_TEXT" />
                <ScalarProperty Name="StartDate" ColumnName="START_DATE" />
                <ScalarProperty Name="EndDate" ColumnName="END_DATE" />
                <ScalarProperty Name="TradingPeriod" ColumnName="TRADING_PERIOD" />
                <ScalarProperty Name="TradingYear" ColumnName="TRADING_YEAR" />
                <ScalarProperty Name="TypeAbbreviation" ColumnName="TYPE_ABBREVIATION" />
                <ScalarProperty Name="CompanyTransHeaderStatusFk" ColumnName="COMPANYTRANSHEADERSTATUSFK" />
                <ComplexProperty Name="TypeDescriptionInfo" TypeName="RIB.Visual.Basics.AccountingJournals.BusinessComponents.DescriptionTranslateType">
                  <ScalarProperty Name="Description" ColumnName="TYPE_DESCRIPTION" />
                  <ScalarProperty Name="DescriptionTr" ColumnName="TYPE_DESCRIPTION_TR" />
                </ComplexProperty>
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="CompanyTransHeaderStatusHistoryEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Basics.AccountingJournals.BusinessComponents.CompanyTransHeaderStatusHistoryEntity">
              <MappingFragment StoreEntitySet="BAS_COMPANYTRNHDSTATHSTIES">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="CompanyTransheaderFk" ColumnName="BAS_COMPANY_TRANSHEADER_FK" />
                <ScalarProperty Name="BasCompanytranshdrstatOldFk" ColumnName="BAS_COMPANYTRANSHDRSTAT_OLD_FK" />
                <ScalarProperty Name="BasCompanytranshdrstatNewFk" ColumnName="BAS_COMPANYTRANSHDRSTAT_NEW_FK" />
                <ScalarProperty Name="Remark" ColumnName="REMARK" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <edmx:Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </edmx:Connection>
    <edmx:Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="false" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
      </DesignerInfoPropertySet>
    </edmx:Options>
    <edmx:Diagrams>
      <Diagram Name="Main" />
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>