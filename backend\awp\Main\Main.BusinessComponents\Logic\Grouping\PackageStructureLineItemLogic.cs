using System;
using System.Collections.Generic;
using System.Globalization;
using RIB.Visual.Basics.Core.Core.Basics;
using RIB.Visual.Platform.BusinessComponents;

namespace RIB.Visual.Awp.Main.BusinessComponents
{
	/// <summary>
	///
	/// </summary>
	public class PackageStructureLineItemLogic: LogicBase
	{
		#region Collaboration

		private static readonly LazyExportedValue<IActiveCollaboratorsManager> ActiveCollaboratorsMgr = new();

		private IEnumerable<(string Area, string Context)> GetCollaborationContexts(int projectId)
		{
			return new List<(String Area, String Context)>() { new("awp.main", projectId.ToString(CultureInfo.InvariantCulture)) };
		}

		/// <summary>
		/// Activate Collaborators context
		///
		/// </summary>
		/// <returns></returns>
		public void ActivateCollaborators(int projectId)
		{
			ActiveCollaboratorsMgr.Value.LogActivity(Context.UserId, () => GetCollaborationContexts(projectId));
		}

		#endregion

		/// <summary>
		///
		/// </summary>
		/// <param name="request"></param>
		public static IEnumerable<GroupingStructureNode> Hierarchy(PackageStructureLineItemGroupingRequest request)
		{
			return new PackageStructureLineItemGroupingLogic().GenerateHierarchyNew(request);
		}
	}
}
