using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample
{
	/// <summary>
	/// This Class can be used for handling itwo 4.0 context 
	/// 
	/// </summary>
	public class ServiceClientContext : JObject
	{
		/// <summary>
		/// This constructor initializes the context in old format
		/// without secureClientRole 
		/// 
		/// </summary>
		/// <param name="signedInClientId"></param>
		/// <param name="clientId"></param>
		/// <param name="permissionClient"></param>
		/// <param name="permissionRoleId"></param>
		/// <param name="dataLanguageId"></param>
		/// <param name="language"></param>
		/// <param name="culture"></param>
		public ServiceClientContext(int signedInClientId = 0, int clientId = 0, int permissionClient = 0, int permissionRoleId = 0,
			int dataLanguageId = 0, string language = "en", string culture = "en-gb")
		{
			Init(signedInClientId, clientId, permissionClient, permissionRoleId, dataLanguageId, language, culture);
		}

		/// <summary>
		/// the initializer
		/// </summary>
		/// <param name="signedInClientId"></param>
		/// <param name="clientId"></param>
		/// <param name="permissionClient"></param>
		/// <param name="permissionRoleId"></param>
		/// <param name="dataLanguageId"></param>
		/// <param name="language"></param>
		/// <param name="culture"></param>
		private void Init(int signedInClientId, int clientId, int permissionClient, int permissionRoleId,
			int dataLanguageId, string language, string culture)
		{
			Add(new JProperty("culture", culture));
			Add(new JProperty("signedInClientId", signedInClientId));
			Add(new JProperty("clientId", clientId));
			Add(new JProperty("permissionClientId", permissionClient));
			Add(new JProperty("permissionRoleId", permissionRoleId));
			Add(new JProperty("dataLanguageId", dataLanguageId));
			Add(new JProperty("language", language));
		}

		/// <summary>
		/// This constructor initializes the context in new format with secureClientRole 
		/// 
		/// </summary>
		/// <param name="secureClientRole"></param>
		/// <param name="dataLanguageId"></param>
		/// <param name="language"></param>
		/// <param name="culture"></param>
		public ServiceClientContext(string secureClientRole,
			int dataLanguageId = 0, string language = "en", string culture = "en-gb")
		{
			Init(secureClientRole, dataLanguageId, language, culture);
		}


		/// <summary>
		/// the initializer
		/// 
		/// </summary>
		/// <param name="secureClientRole"></param>
		/// <param name="dataLanguageId"></param>
		/// <param name="language"></param>
		/// <param name="culture"></param>
		private void Init(string secureClientRole, int dataLanguageId, string language, string culture)
		{
			Add(new JProperty("culture", culture));
			Add(new JProperty("dataLanguageId", dataLanguageId));
			Add(new JProperty("language", language));
			Add(new JProperty("secureClientRole", secureClientRole));
		}


		/// <summary>
		/// Return this JObject as unformatted string
		/// 
		/// </summary>
		/// <returns></returns>
		public string AsString()
		{
			return this.ToString(Formatting.None);
		}

	}
}