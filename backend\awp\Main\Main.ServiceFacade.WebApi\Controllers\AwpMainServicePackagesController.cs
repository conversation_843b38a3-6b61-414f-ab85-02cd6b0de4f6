using System.Collections.Generic;
using System.Web.Http;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Platform.ServiceFacade.WebApi;

namespace RIB.Visual.Awp.Main.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	[RoutePrefix("awp/main/servicepackage")]
	public class AwpMainServicePackagesController : ApiControllerBase
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		[Route("tree")]
		[HttpPost]
		public IEnumerable<ServicePackageDto> GetChartConfigs(int projectId)
		{
			var result = new List<ServicePackageDto>();

			// This is demo, need change to get from api
			result.Add(new ServicePackageDto
			{
				Id = 1,
				Reference = "PackageCode",
				BriefInfo = new Platform.Common.DescriptionTranslateTypeDto(),
				TypeFk = -1,
				ParentFk = null,
				Children = new List<ServicePackageDto> {
					new ServicePackageDto
					{
						Id = 2,
						Reference = "Sub PackageCode",
						BriefInfo = new Platform.Common.DescriptionTranslateTypeDto(),
						TypeFk = -2,
						ParentFk = 1,
						Children = new List<ServicePackageDto> {
							new ServicePackageDto
							{
								Id = 3,
								Reference = "1",
								BriefInfo = new Platform.Common.DescriptionTranslateTypeDto(),
								TypeFk = 103,
								ParentFk = 2,
								Children = new List<ServicePackageDto> {
									new ServicePackageDto
									{
										Id = 7,
										Reference = "10.",
										BriefInfo = new Platform.Common.DescriptionTranslateTypeDto(),
										TypeFk = 1,
										ParentFk = 3,
										Children = new List<ServicePackageDto> {

										}
									}
								}
							}
						}
					}
				}
			});

			result.Add(new ServicePackageDto
			{
				Id = 4,
				Reference = "PackageCode",
				BriefInfo = new Platform.Common.DescriptionTranslateTypeDto(),
				TypeFk = -1,
				ParentFk = null,
				Children = new List<ServicePackageDto> {
					new ServicePackageDto
					{
						Id = 5,
						Reference = "Sub PackageCode",
						BriefInfo = new Platform.Common.DescriptionTranslateTypeDto(),
						TypeFk = -2,
						ParentFk = 4,
						Children = new List<ServicePackageDto> {
							new ServicePackageDto
							{
								Id = 6,
								Reference = "1",
								BriefInfo = new Platform.Common.DescriptionTranslateTypeDto(),
								TypeFk = 103,
								ParentFk = 5,
								Children = new List<ServicePackageDto> {
									new ServicePackageDto
									{
										Id = 8,
										Reference = "10.",
										BriefInfo = new Platform.Common.DescriptionTranslateTypeDto(),
										TypeFk = 1,
										ParentFk = 6,
										Children = new List<ServicePackageDto> {

										}
									}
								}
							}
						}
					}
				}
			});

			return result;
		}
	}
}
