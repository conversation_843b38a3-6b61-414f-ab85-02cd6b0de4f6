//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON>art Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Basics.AuditTrail.BusinessComponents;

namespace RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi
{

    /// <summary>
    /// Represents a Dto class.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("AUD_CONTAINER2AUD_TABLE")]
    public partial class AudContainer2AudTableDto : RIB.Visual.Platform.Core.ITypedDto<AudContainer2AudTableEntity>
    {
        #region Constructors
       
        /// <summary>
        /// Initializes an instance of class AudContainer2AudTableDto.
        /// </summary>
        public AudContainer2AudTableDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class AudContainer2AudTableDto.
        /// </summary>
        /// <param name="entity">the instance of class AudContainer2AudTableEntity</param>
        public AudContainer2AudTableDto(AudContainer2AudTableEntity entity)
        {
            AudCointainerFk = entity.AudCointainerFk;
            AudTableFk = entity.AudTableFk;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;
            
            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion 

        #region Properties

        /// <summary>
        /// Gets or Sets AudCointainerFk.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AUD_COINTAINER_FK", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int AudCointainerFk { get; set; }

        /// <summary>
        /// Gets or Sets AudTableFk.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AUD_TABLE_FK", TypeName = "int", Order = 1)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int AudTableFk { get; set; }

        /// <summary>
        /// Gets or Sets InsertedAt.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 2)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public global::System.DateTime InsertedAt { get; set; }

        /// <summary>
        /// Gets or Sets InsertedBy.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 3)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }

        /// <summary>
        /// Gets or Sets UpdatedAt.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 4)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Gets or Sets UpdatedBy.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 5)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// Gets or Sets Version.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 6)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary />
        public AudContainerDto AudContainerEntity { get; set; }

        /// <summary />
        public AudTableDto AudTableEntity { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(AudContainer2AudTableEntity); }
        }

        /// <summary>
        /// Copy the current AudContainer2AudTableDto instance to a new AudContainer2AudTableEntity instance.
        /// </summary>
        /// <returns>a new instance of class AudContainer2AudTableEntity</returns>
        public AudContainer2AudTableEntity Copy() 
        {
          var entity = new AudContainer2AudTableEntity();

          entity.AudCointainerFk = this.AudCointainerFk;
          entity.AudTableFk = this.AudTableFk;
          entity.InsertedAt = this.InsertedAt;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedAt = this.UpdatedAt;
          entity.UpdatedBy = this.UpdatedBy;
          entity.Version = this.Version;

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(AudContainer2AudTableEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(AudContainer2AudTableEntity entity);
    }

}
