/*
 * Copyright(c) RIB Software GmbH
 */

import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { getCustomDialogDataToken } from '@libs/ui/common';
import { TranslatePipe } from '@libs/platform/common';
import { IValidationError, IValidationWizardConfig } from '../../services/procurement-inventory-validation-wizard.service';

/**
 * Interface for grouped validation errors
 */
interface IGroupedValidationError {
	entityType: 'header' | 'inventory';
	entityId: number;
	entityName: string;
	errors: IValidationError[];
}

/**
 * Procurement Inventory Validation Wizard Component
 * 
 * This component displays validation errors in a structured dialog format,
 * similar to the AngularJS validation wizard but adapted for Angular.
 */
@Component({
	selector: 'procurement-inventory-validation-wizard',
	standalone: true,
	imports: [CommonModule, TranslatePipe],
	template: `
		<div class="validation-wizard-container">
			<!-- Header Message -->
			<div class="validation-message" *ngIf="config.message">
				<div class="alert alert-danger">
					<div class="tlb-icons ico-error">
						{{ config.message }}
					</div>
				</div>
			</div>

			<!-- Validation Errors by Entity -->
			<div class="validation-errors">
				<div *ngFor="let group of groupedErrors; trackBy: trackByEntityId" class="entity-group">
					<h4 class="entity-header">
						<span class="tlb-icons" [ngClass]="getEntityIcon(group.entityType)"></span>
						{{ group.entityName }}
					</h4>
					
					<div class="error-list">
						<div *ngFor="let error of group.errors; trackBy: trackByErrorField" class="error-item">
							<div class="error-field">
								<strong>{{ error.fieldName }}:</strong>
							</div>
							<div class="error-message">
								<span class="tlb-icons ico-error-small"></span>
								<span *ngIf="error.translationKey; else plainMessage">
									{{ error.translationKey | translate: error.translationParams }}
								</span>
								<ng-template #plainMessage>
									{{ error.errorMessage }}
								</ng-template>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Summary -->
			<div class="validation-summary" *ngIf="totalErrorCount > 0">
				<div class="alert alert-warning">
					<strong>{{ 'procurement.inventory.validation.summary' | translate }}</strong>
					{{ 'procurement.inventory.validation.totalErrors' | translate: {count: totalErrorCount} }}
				</div>
			</div>

			<!-- Instructions -->
			<div class="validation-instructions">
				<p>{{ 'procurement.inventory.validation.instructions' | translate }}</p>
			</div>
		</div>
	`,
	styles: [`
		.validation-wizard-container {
			padding: 20px;
			max-height: 500px;
			overflow-y: auto;
		}

		.validation-message {
			margin-bottom: 20px;
		}

		.validation-message .alert {
			padding: 10px 15px;
			border-radius: 4px;
			border: 1px solid #d32f2f;
			background-color: #ffebee;
			color: #d32f2f;
		}

		.validation-message .tlb-icons.ico-error {
			padding-left: 20px;
			background-position: 0 center;
			background-size: 16px;
		}

		.validation-errors {
			margin-bottom: 20px;
		}

		.entity-group {
			margin-bottom: 25px;
			border: 1px solid #e0e0e0;
			border-radius: 4px;
			overflow: hidden;
		}

		.entity-header {
			background-color: #f5f5f5;
			padding: 10px 15px;
			margin: 0;
			border-bottom: 1px solid #e0e0e0;
			font-size: 14px;
			font-weight: 600;
		}

		.entity-header .tlb-icons {
			margin-right: 8px;
			padding-left: 20px;
			background-position: 0 center;
			background-size: 16px;
		}

		.entity-header .tlb-icons.ico-header {
			background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTkgM0g1Yy0xLjEgMC0yIC45LTIgMnYxNGMwIDEuMS45IDIgMiAyaDE0YzEuMSAwIDItLjkgMi0yVjVjMC0xLjEtLjktMi0yLTJ6bS01IDEySDZ2LTJoOHYyem0zLTRINnYtMmgxMXYyem0wLTRINlY1aDExdjJ6Ii8+PC9zdmc+');
		}

		.entity-header .tlb-icons.ico-inventory {
			background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTkgN2gtM1Y2YTQgNCAwIDAgMC04IDB2MUg1YTEgMSAwIDAgMC0xIDF2MTFhMyAzIDAgMCAwIDMgM2gxMGEzIDMgMCAwIDAgMy0zVjhhMSAxIDAgMCAwLTEtMXpNMTAgNmEyIDIgMCAwIDEgNCAwdjFoLTRWNnptOCAxM2ExIDEgMCAwIDEtMSAxSDdhMSAxIDAgMCAxLTEtMVY5aDJ2MWExIDEgMCAwIDAgMiAwVjloNHYxYTEgMSAwIDAgMCAyIDBWOWgydjEweiIvPjwvc3ZnPg==');
		}

		.error-list {
			padding: 15px;
		}

		.error-item {
			margin-bottom: 15px;
			padding: 10px;
			background-color: #fff5f5;
			border-left: 4px solid #f44336;
			border-radius: 0 4px 4px 0;
		}

		.error-item:last-child {
			margin-bottom: 0;
		}

		.error-field {
			font-weight: 600;
			color: #333;
			margin-bottom: 5px;
		}

		.error-message {
			color: #d32f2f;
			display: flex;
			align-items: center;
		}

		.error-message .tlb-icons.ico-error-small {
			width: 14px;
			height: 14px;
			margin-right: 6px;
			background-size: 14px;
			background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNCIgaGVpZ2h0PSIxNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSIjZDMyZjJmIj48cGF0aCBkPSJNMTIgMkM2LjQ4IDIgMiA2LjQ4IDIgMTJzNC40OCAxMCAxMCAxMCAxMC00LjQ4IDEwLTEwUzE3LjUyIDIgMTIgMnptMSAxNWgtMnYtMmgydjJ6bTAtNGgtMlY3aDJ2NnoiLz48L3N2Zz4=');
		}

		.validation-summary {
			margin-bottom: 20px;
		}

		.validation-summary .alert {
			padding: 10px 15px;
			border-radius: 4px;
			border: 1px solid #ff9800;
			background-color: #fff3e0;
			color: #e65100;
		}

		.validation-instructions {
			padding: 15px;
			background-color: #f9f9f9;
			border-radius: 4px;
			border: 1px solid #e0e0e0;
		}

		.validation-instructions p {
			margin: 0;
			color: #666;
			font-size: 14px;
		}

		/* Scrollbar styling */
		.validation-wizard-container::-webkit-scrollbar {
			width: 8px;
		}

		.validation-wizard-container::-webkit-scrollbar-track {
			background: #f1f1f1;
			border-radius: 4px;
		}

		.validation-wizard-container::-webkit-scrollbar-thumb {
			background: #c1c1c1;
			border-radius: 4px;
		}

		.validation-wizard-container::-webkit-scrollbar-thumb:hover {
			background: #a8a8a8;
		}
	`]
})
export class ProcurementInventoryValidationWizardComponent {
	private readonly dlgWrapper = inject(getCustomDialogDataToken<IValidationWizardConfig, ProcurementInventoryValidationWizardComponent>());

	public get config(): IValidationWizardConfig {
		return this.dlgWrapper.value || {
			title: 'Validation Errors',
			message: 'Please correct the following errors:',
			errors: []
		};
	}

	public get groupedErrors(): IGroupedValidationError[] {
		const grouped = new Map<string, IGroupedValidationError>();

		this.config.errors.forEach(error => {
			const key = `${error.entityType}_${error.entityId}`;
			
			if (!grouped.has(key)) {
				grouped.set(key, {
					entityType: error.entityType,
					entityId: error.entityId,
					entityName: this.getEntityDisplayName(error.entityType, error.entityId),
					errors: []
				});
			}
			
			grouped.get(key)!.errors.push(error);
		});

		return Array.from(grouped.values());
	}

	public get totalErrorCount(): number {
		return this.config.errors.length;
	}

	/**
	 * Gets the display name for an entity
	 */
	public getEntityDisplayName(entityType: 'header' | 'inventory', entityId: number): string {
		if (entityType === 'header') {
			return `Inventory Header (ID: ${entityId})`;
		} else {
			return `Inventory Item (ID: ${entityId})`;
		}
	}

	/**
	 * Gets the icon class for an entity type
	 */
	public getEntityIcon(entityType: 'header' | 'inventory'): string {
		return entityType === 'header' ? 'ico-header' : 'ico-inventory';
	}

	/**
	 * TrackBy function for entity groups
	 */
	public trackByEntityId(index: number, item: IGroupedValidationError): string {
		return `${item.entityType}_${item.entityId}`;
	}

	/**
	 * TrackBy function for error items
	 */
	public trackByErrorField(index: number, item: IValidationError): string {
		return `${item.entityId}_${item.fieldName}`;
	}
}
