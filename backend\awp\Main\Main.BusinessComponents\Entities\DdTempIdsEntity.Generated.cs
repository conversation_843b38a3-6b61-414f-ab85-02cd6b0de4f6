﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Awp.Main.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Awp.Main.BusinessComponents.DdTempIdsEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("BAS_DDTEMPIDS")]
    public partial class DdTempIdsEntity : ICloneable
    {
        /// <summary>
        /// Initialize a new DdTempIdsEntity object.
        /// </summary>
        public DdTempIdsEntity()
        {
            OnConstruct(); // call partial constructor if present             
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for RequestId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQUESTID", TypeName = "char(32)", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string RequestId
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 1)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Key1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("KEY1", TypeName = "int", Order = 2)]
        public virtual int? Key1
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Key2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("KEY2", TypeName = "int", Order = 3)]
        public virtual int? Key2
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Key3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("KEY3", TypeName = "int", Order = 4)]
        public virtual int? Key3
        {
            get;
            set;
        }


        #endregion
    
        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            DdTempIdsEntity obj = new DdTempIdsEntity();
            obj.RequestId = RequestId;
            obj.Id = Id;
            obj.Key1 = Key1;
            obj.Key2 = Key2;
            obj.Key3 = Key3;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(DdTempIdsEntity clonedEntity);

    }


}
