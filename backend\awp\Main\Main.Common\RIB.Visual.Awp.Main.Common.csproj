﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{7DAC796E-F383-4330-97A9-************}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RIB.Visual.Awp.Main.Common</RootNamespace>
    <AssemblyName>RIB.Visual.Awp.Main.Common</AssemblyName>
    <TargetFramework>net8.0</TargetFramework>
    <FileAlignment>512</FileAlignment>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\RIB.Visual.Awp.Main.Common.XML</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Platform.OperationalManagement">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.OperationalManagement.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common, Version=1.0.0.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Common, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Core, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Common, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Newtonsoft.Json.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <None Include="RIBvisual.snk" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Main.Core\RIB.Visual.Awp.Main.Core.csproj">
      <Project>{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}</Project>
      <Name>RIB.Visual.Awp.Main.Core</Name>
      <Private>False</Private>
    </ProjectReference>
  </ItemGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F" />
  </Target>
</Project>