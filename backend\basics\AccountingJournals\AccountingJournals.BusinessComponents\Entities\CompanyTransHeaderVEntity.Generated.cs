﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.AccountingJournals.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Basics.AccountingJournals.BusinessComponents.CompanyTransHeaderVEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("BAS_COMPANY_TRANSHEADER_V")]
    public partial class CompanyTransHeaderVEntity : ICloneable
    {
        /// <summary>
        /// Initialize a new CompanyTransHeaderVEntity object.
        /// </summary>
        public CompanyTransHeaderVEntity()
        {
          this.TypeDescriptionInfo = new DescriptionTranslateType();
            OnConstruct(); // call partial constructor if present
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 1)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Description {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_FK", TypeName = "int", Order = 2)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CompanyFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyPeriodFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_PERIOD_FK", TypeName = "int", Order = 3)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CompanyPeriodFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PostingDate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("POSTING_DATE", TypeName = "date", Order = 4)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual System.DateTime PostingDate {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TransactiontypeFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_TRANSACTIONTYPE_FK", TypeName = "int", Order = 5)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int TransactiontypeFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Issuccess in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISSUCCESS", TypeName = "bit", Order = 6)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool Issuccess {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ReturnValue in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RETURN_VALUE", TypeName = "nvarchar(2000)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ReturnValue {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyTransheaderFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_TRANSHEADER_FK", TypeName = "int", Order = 8)]
        public virtual int? CompanyTransheaderFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CommentText in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("COMMENT_TEXT", TypeName = "nvarchar(255)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(255)]
        public virtual string CommentText {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyTransHeaderStatusFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("COMPANYTRANSHEADERSTATUSFK", TypeName = "int", Order = 10)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CompanyTransHeaderStatusFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StartDate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("START_DATE", TypeName = "date", Order = 11)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual System.DateTime StartDate {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for EndDate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("END_DATE", TypeName = "date", Order = 12)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual System.DateTime EndDate {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TradingPeriod in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TRADING_PERIOD", TypeName = "int", Order = 13)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int TradingPeriod {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TradingYear in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TRADING_YEAR", TypeName = "int", Order = 14)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int TradingYear {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TypeDescriptionInfo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TYPE_DESCRIPTION", TypeName = "nvarchar(252)", Order = 15, TranslationColumnName = "TYPE_DESCRIPTION_TR")]
        public virtual DescriptionTranslateType TypeDescriptionInfo {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TypeAbbreviation in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TYPE_ABBREVIATION", TypeName = "nvarchar(16)", Order = 17)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string TypeAbbreviation {
            get; set;
        }


        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            CompanyTransHeaderVEntity obj = new CompanyTransHeaderVEntity();
            obj.Id = Id;
            obj.Description = Description;
            obj.CompanyFk = CompanyFk;
            obj.CompanyPeriodFk = CompanyPeriodFk;
            obj.PostingDate = PostingDate;
            obj.TransactiontypeFk = TransactiontypeFk;
            obj.Issuccess = Issuccess;
            obj.ReturnValue = ReturnValue;
            obj.CompanyTransheaderFk = CompanyTransheaderFk;
            obj.CommentText = CommentText;
            obj.CompanyTransHeaderStatusFk = CompanyTransHeaderStatusFk;
            obj.StartDate = StartDate;
            obj.EndDate = EndDate;
            obj.TradingPeriod = TradingPeriod;
            obj.TradingYear = TradingYear;
            obj.TypeDescriptionInfo = (DescriptionTranslateType)TypeDescriptionInfo.Clone();
            obj.TypeAbbreviation = TypeAbbreviation;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(CompanyTransHeaderVEntity clonedEntity);

    }


}
