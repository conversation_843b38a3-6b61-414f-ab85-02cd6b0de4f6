﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;


namespace RIB.Visual.Basics.Api.Client.SyncDataApp
{
	public static class LoginService
	{
		/// <summary>
		/// This method call the iTWOCloud WEB-Api. 
		/// 
		/// Header will be enhanced by token and context
		/// 
		/// </summary>
		public static bool DoLogin(IdentityModel model, out string result)
		{
			result = null;

			using (var client = new HttpClient())
			{
				var logonUri = string.Format("{0}{1}?username={2}&password={3}", model.ApiBaseUrl, "/basics/api/apilogon", model.UserName, model.Password);
				if (!model.CheckCertificate)
				{
					ServicePointManager.ServerCertificateValidationCallback += (sender, cert, chain, sslPolicyErrors) => true;
				}

				try
				{
					//client.DefaultRequestHeaders.
					var response = client.GetAsync(logonUri).Result;
					if (response.StatusCode == HttpStatusCode.OK)
					{
						result = JsonConvert.DeserializeObject<string>(response.Content.ReadAsStringAsync().Result);
						return true;
					}
					else
					{
						return false;
					}

				}
				catch (AccessViolationException ex)
				{
					result = ex.Message + "\r\nLogin failed wrong username/password! Error: " + ex.StackTrace;
					return false;
				}
				catch (Exception ex)
				{
					result = ex.Message + "\r\nLogin failed! other failure, Error: " + ex.StackTrace;
					return false;
				}
			}

		}
	}
}
