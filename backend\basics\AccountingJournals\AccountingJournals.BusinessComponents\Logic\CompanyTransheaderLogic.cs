﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.LookupData.BusinessComponents;
using RIB.Visual.Platform.Core;
using System.Linq.Expressions;
using RIB.Visual.Platform.Common;
using RIB.Visual.Cloud.Common.BusinessComponents;
// ReSharper disable PossibleMultipleEnumeration
using System.Linq.Dynamic;
using RIB.Visual.Basics.AccountingJournals.BusinessComponents;

namespace RIB.Visual.Basics.AccountingJournals.BusinessComponents
{
	/// <summary>
    /// CompanyTransheaderLogic
	/// </summary>
    public class CompanyTransheaderLogic : RVPBizComp.LogicBase
	{
		/// <summary>
		/// Provides access to the database model.
		/// </summary>
		/// <returns></returns>
		public override System.Data.Entity.Infrastructure.DbCompiledModel GetDbModel() => ModelBuilder.DbModel;

		/// <summary>
		/// GetCompanyTransactionListByDt
		/// </summary>
		/// <param name="tradingYearFrom"></param>
		/// <param name="tradingYearTo"></param>
		/// <param name="postingDateFrom"></param>
		/// <param name="postingDateTo"></param>
		/// <param name="tradingPeriodStartDateFrom"></param>
		/// <param name="tradingPeriodStartDateTo"></param>
		/// <param name="tradingPeriodEndDateFrom"></param>
		/// <param name="tradingPeriodEndDateTo"></param>
		/// <param name="companyId"></param>
		/// <param name="isSuccess"></param>
		/// <param name="transactionTypeId"></param>
		/// <returns></returns>
		public IEnumerable<CompanyTransHeaderVEntity> GetCompanyTransactionListByDt(int? tradingYearFrom, int? tradingYearTo, DateTime? postingDateFrom, DateTime? postingDateTo, DateTime? tradingPeriodStartDateFrom, DateTime? tradingPeriodStartDateTo, DateTime? tradingPeriodEndDateFrom, DateTime? tradingPeriodEndDateTo, int companyId, bool? isSuccess, int? transactionTypeId)
		{
            Expression<Func<CompanyTransHeaderVEntity,bool>> exp = e => true;
            if (tradingYearFrom != null)
            {
                exp = exp.And(e => e.TradingYear >= tradingYearFrom.Value);
            }

            if (tradingYearTo != null)
            {
                exp = exp.And(e => e.TradingYear <= tradingYearTo.Value);
            }

            if (postingDateFrom != null)
            {
                exp = exp.And(e => e.PostingDate >= postingDateFrom.Value);
            }

            if (postingDateTo != null)
            {
                exp = exp.And(e => e.PostingDate <= postingDateTo.Value);
            }

            if (tradingPeriodStartDateFrom != null)
            {
                exp = exp.And(e => e.StartDate >= tradingPeriodStartDateFrom.Value);
            }

            if (tradingPeriodStartDateTo != null)
            {
                exp = exp.And(e => e.StartDate <= tradingPeriodStartDateTo.Value);
            }

            if (tradingPeriodEndDateFrom != null)
            {
                exp = exp.And(e => e.EndDate >= tradingPeriodEndDateFrom.Value);
            }

            if (tradingPeriodEndDateTo != null)
            {
                exp = exp.And(e => e.EndDate <= tradingPeriodEndDateTo.Value);
            }

            if (isSuccess != null)
            {
                exp = exp.And(e => e.Issuccess == isSuccess);
            }

            if (transactionTypeId != null)
            {
                exp = exp.And(e => e.TransactiontypeFk == transactionTypeId.Value);
            }

            exp = exp.And(e => e.CompanyFk == companyId);

            using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
            {
                var list = dbContext.Entities<CompanyTransHeaderVEntity>().Where(exp).OrderBy(e => e.PostingDate).ToList(); ;

                Translate(list);
                return list;
            }
		}

        private IEnumerable<CompanyTransHeaderVEntity> Translate(IEnumerable<CompanyTransHeaderVEntity> source)
        {
            return source.Translate(this.UserLanguageId, new Func<CompanyTransHeaderVEntity, DescriptionTranslateType>[] { e => e.TypeDescriptionInfo });
        }
    }
}
