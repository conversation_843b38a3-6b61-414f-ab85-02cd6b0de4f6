﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.BillingSchema.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Basics.BillingSchema.BusinessComponents.RubricEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("BAS_RUBRIC")]
    public partial class RubricEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new RubricEntity object.
        /// </summary>
        public RubricEntity()
        {
          this.DescriptionInfo = new DescriptionTranslateType();
            OnConstruct(); // call partial constructor if present
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DescriptionInfo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 1, TranslationColumnName = "DESCRIPTION_TR")]
        public virtual DescriptionTranslateType DescriptionInfo {
            get; set;
        }


        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            RubricEntity obj = new RubricEntity();
            obj.Id = Id;
            obj.DescriptionInfo = (DescriptionTranslateType)DescriptionInfo.Clone();
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(RubricEntity clonedEntity);

    }


}
