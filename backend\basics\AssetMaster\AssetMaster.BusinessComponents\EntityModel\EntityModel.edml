<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="BusinessComponents.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2008" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:devart="http://devart.com/schemas/edml/StorageSchemaExtensions/1.0" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityContainer Name="DbContextStoreContainer">
          <EntitySet Name="MDC_ASSET_MASTERs" EntityType="BusinessComponents.Store.MDC_ASSET_MASTER" store:Type="Tables" Table="MDC_ASSET_MASTER" />
          <EntitySet Name="BAS_DDTEMPIDS" EntityType="BusinessComponents.Store.BAS_DDTEMPIDS" store:Type="Tables" Table="BAS_DDTEMPIDS" />
          <EntitySet Name="BAS_TRANSLATIONs" EntityType="BusinessComponents.Store.BAS_TRANSLATION" store:Type="Tables" Table="BAS_TRANSLATION" />
          <AssociationSet Name="MDC_ASSET_MASTER_FK2" Association="BusinessComponents.Store.MDC_ASSET_MASTER_FK2">
            <End Role="MDC_ASSET_MASTER" EntitySet="MDC_ASSET_MASTERs" />
            <End Role="MDC_ASSET_MASTER1" EntitySet="MDC_ASSET_MASTERs" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="MDC_ASSET_MASTER">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="MDC_ASSET_MASTER_PARENT_FK" Type="int" />
          <Property Name="MDC_CONTEXT_FK" Type="int" Nullable="false" />
          <Property Name="CODE" Type="nvarchar" Nullable="false" MaxLength="16" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="DESCRIPTION_TR" Type="int" />
          <Property Name="BAS_ADDRESS_FK" Type="int" />
          <Property Name="ISLIVE" Type="bit" Nullable="false" />
          <Property Name="REMARK" Type="nvarchar(max)" />
          <Property Name="USERDEFINED1" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED2" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED3" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED4" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED5" Type="nvarchar" MaxLength="252" />
          <Property Name="MDC_ASSET_MASTER_LEVEL1_FK" Type="int" />
          <Property Name="MDC_ASSET_MASTER_LEVEL2_FK" Type="int" />
          <Property Name="MDC_ASSET_MASTER_LEVEL3_FK" Type="int" />
          <Property Name="MDC_ASSET_MASTER_LEVEL4_FK" Type="int" />
          <Property Name="MDC_ASSET_MASTER_LEVEL5_FK" Type="int" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="ALLOW_ASSIGNMENT" Type="bit" Nullable="false" devart:DefaultValue="1" />
        </EntityType>
        <EntityType Name="BAS_DDTEMPIDS">
          <Key>
            <PropertyRef Name="REQUESTID" />
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="REQUESTID" Type="char" Nullable="false" MaxLength="32" />
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="KEY1" Type="int" />
          <Property Name="KEY2" Type="int" />
          <Property Name="KEY3" Type="int" />
        </EntityType>
        <EntityType Name="BAS_TRANSLATION">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="BAS_LANGUAGE_FK" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="BAS_LANGUAGE_FK" Type="int" Nullable="false" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="2000" />
          <Property Name="SOURCEINFO" Type="nvarchar" MaxLength="255" />
          <Property Name="MAXLENGTH" Type="int" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <Association Name="MDC_ASSET_MASTER_FK2">
          <End Role="MDC_ASSET_MASTER" Type="BusinessComponents.Store.MDC_ASSET_MASTER" Multiplicity="0..1" />
          <End Role="MDC_ASSET_MASTER1" Type="BusinessComponents.Store.MDC_ASSET_MASTER" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="MDC_ASSET_MASTER">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="MDC_ASSET_MASTER1">
              <PropertyRef Name="MDC_ASSET_MASTER_PARENT_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="RIB.Visual.Basics.AssetMaster.BusinessComponents" Alias="Self" d4p1:ViewGeneration="true" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:devart="http://devart.com/schemas/edml/ConceptualSchemaExtensions/1.0" xmlns:ed="http://devart.com/schemas/EntityDeveloper/1.0" annotation:UseStrongSpatialTypes="false" xmlns:d4p1="http://devart.com/schemas/edml/ConceptualSchemaExtensions/1.0" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="ModelBuilder" ed:Namespace="RIB.Visual.Basics.AssetMaster.BusinessComponents" annotation:LazyLoadingEnabled="false" ed:Guid="2c8b7c01-1421-4d2d-b86b-3d5c5b084643">
          <EntitySet Name="AssetMasterEntities" EntityType="RIB.Visual.Basics.AssetMaster.BusinessComponents.AssetMasterEntity" />
          <EntitySet Name="DdTempIdsEntities" EntityType="RIB.Visual.Basics.AssetMaster.BusinessComponents.DdTempIdsEntity" />
          <EntitySet Name="TranslationEntities" EntityType="RIB.Visual.Basics.AssetMaster.BusinessComponents.TranslationEntity" />
          <AssociationSet Name="MDC_ASSET_MASTER_FK2Set" Association="RIB.Visual.Basics.AssetMaster.BusinessComponents.MDC_ASSET_MASTER_FK2">
            <End Role="AssetMasterParent" EntitySet="AssetMasterEntities" />
            <End Role="AssetMasterChildren" EntitySet="AssetMasterEntities" />
          </AssociationSet>
          <AssociationSet Name="AssetMasterEntity_DdTempIdsEntitySet" Association="RIB.Visual.Basics.AssetMaster.BusinessComponents.AssetMasterEntity_DdTempIdsEntity">
            <End Role="AssetMasterEntity" EntitySet="AssetMasterEntities" />
            <End Role="DdTempIdsEntities" EntitySet="DdTempIdsEntities" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="AssetMasterEntity" ed:Guid="f969533f-b6d2-4ccd-bd3a-511f871e4060">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="a2f5a3fe-8916-49bd-a960-fc3404ee6cd3" />
          <Property Name="AssetMasterParentFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="55ddc709-71bd-4667-8071-50c208fa82f7" />
          <Property Name="MdcContextFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="7c39b75f-4729-4acd-ab30-cf27b0e7e862" />
          <Property Name="Code" Type="String" Nullable="false" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="true" ed:Guid="9331b769-da86-429e-9cdf-7ab7d14511e8">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">code</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DescriptionInfo" Type="RIB.Visual.Basics.AssetMaster.BusinessComponents.DescriptionTranslateType" Nullable="false" ed:ValidateRequired="false" ed:Guid="aa96572e-19a7-406f-af18-f8b123f0077f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">translation</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="AddressFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="ef7f44f5-5450-429a-8cbf-3bfddae3e027">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsLive" Type="Boolean" Nullable="false" DefaultValue="true" ed:ValidateRequired="true" ed:Guid="ddac38d6-74c4-4bf0-b4e3-d60893699ffb">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="AssetMasterLevel1Fk" Type="Int32" ed:ValidateRequired="false" ed:Guid="f2ce4f54-3ef7-40c6-bd5e-40185c39dcef" />
          <Property Name="Remark" Type="String" Unicode="true" ed:ValidateRequired="false" ed:Guid="fc3d82f8-f370-4b03-8db0-5d5ff1fa6e19">
          </Property>
          <Property Name="UserDefined1" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="37334ebc-5ce6-4d3d-86ca-929fdecdddf3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefined2" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="e4309a77-1110-4047-be6d-dc6677180d9e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefined3" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="4952ca87-d640-4a51-918f-1585780caeb8">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefined4" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="7519ad0f-6e9a-4a60-bb24-12adce61a467">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefined5" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="e15f99dc-6b7a-4912-9e34-9264eb548c7a">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="AssetMasterLevel2Fk" Type="Int32" ed:ValidateRequired="false" ed:Guid="98c646d8-d604-4c82-9abc-4af278763e92" />
          <Property Name="AssetMasterLevel3Fk" Type="Int32" ed:ValidateRequired="false" ed:Guid="3214ff2d-3f46-4fe6-9291-c91127c60ae5" />
          <Property Name="AssetMasterLevel4Fk" Type="Int32" ed:ValidateRequired="false" ed:Guid="df5409b0-4fcd-4eef-949e-ae9643eec088" />
          <Property Name="AssetMasterLevel5Fk" Type="Int32" ed:ValidateRequired="false" ed:Guid="68dcbd17-03f6-4672-a0c4-82aa7c84e1b8" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="f5ac90f2-c377-476d-b2eb-6377570c5793" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="11d2c2b3-fcad-4ac5-8d18-3c9a94972318" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="a86777ae-1195-4863-bf11-295fee28f977" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="f45fd704-19fc-4864-a8e4-115666c06b8b" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="ee79f25f-67ad-49bc-8d38-23d180eb05b2" />
          <Property Name="AllowAssignment" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="53f45eb9-591d-42b7-a42e-edb945e38e15">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <NavigationProperty Name="AssetMasterChildren" Relationship="RIB.Visual.Basics.AssetMaster.BusinessComponents.MDC_ASSET_MASTER_FK2" FromRole="AssetMasterParent" ToRole="AssetMasterChildren" ed:Guid="56aee142-b9e7-4383-b405-7bea25a22f8c" ed:GenerateDTO="False" />
          <NavigationProperty Name="AssetMasterParent" Relationship="RIB.Visual.Basics.AssetMaster.BusinessComponents.MDC_ASSET_MASTER_FK2" FromRole="AssetMasterChildren" ToRole="AssetMasterParent" ed:Guid="7d9dc0b9-5044-48e8-bea5-ead988dfc55e" ed:GenerateDTO="False" />
          <NavigationProperty Name="DdTempIdsEntities" Relationship="RIB.Visual.Basics.AssetMaster.BusinessComponents.AssetMasterEntity_DdTempIdsEntity" FromRole="AssetMasterEntity" ToRole="DdTempIdsEntities" ed:Guid="df3de52b-3b92-473e-9f83-e9e58c297019" />
        </EntityType>
        <EntityType Name="DdTempIdsEntity" ed:Guid="61b2bbd6-1033-4e7d-8397-1b5ca8565c9e" ed:GenerateDTO="False">
          <Key>
            <PropertyRef Name="RequestId" />
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="RequestId" Type="String" Nullable="false" MaxLength="32" FixedLength="true" ed:ValidateMaxLength="32" ed:ValidateRequired="true" ed:Guid="a7054f00-2d54-45f3-9cd6-620746a4f9b0">
          </Property>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="1ec64393-2331-44ef-86ba-240c5340826b" />
          <Property Name="Key1" Type="Int32" ed:ValidateRequired="false" ed:Guid="cfb8b205-4bf2-4d35-9ec8-fed6d40bee97" />
          <Property Name="Key2" Type="Int32" ed:ValidateRequired="false" ed:Guid="ac48b097-6745-493d-9899-b50c3606c418" />
          <Property Name="Key3" Type="Int32" ed:ValidateRequired="false" ed:Guid="f9744b71-6c69-4206-8891-100a1e7af794" />
          <NavigationProperty Name="AssetMasterEntity" Relationship="RIB.Visual.Basics.AssetMaster.BusinessComponents.AssetMasterEntity_DdTempIdsEntity" FromRole="DdTempIdsEntities" ToRole="AssetMasterEntity" ed:Guid="f4bf64ba-64d3-4047-a94e-64d903ef15a7" />
        </EntityType>
        <EntityType Name="TranslationEntity" ed:Guid="9f74822e-3fe0-47b1-a2cb-6f587c5d4cbc" ed:GenerateDTO="False">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="BasLanguageFk" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="6ac3ecf6-a5cd-4a35-b599-79e2f266b3c2" />
          <Property Name="BasLanguageFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="7aaae8a6-d705-46ed-bc7a-19e3a3d558de" />
          <Property Name="Description" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="2efbef9c-e65d-4567-94de-bc1731dc78c6">
          </Property>
          <Property Name="SourceInfo" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="9ce7efda-ea45-466a-a71a-1db764d85be9">
          </Property>
          <Property Name="Maxlength" Type="Int32" ed:ValidateRequired="false" ed:Guid="b8fdc01c-7e3a-4f03-966e-50c3f3d15eb1" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="ce4d9808-cc3b-4dd0-93aa-cad146810b0c" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="8ceae599-4053-4713-bac4-d468ab589c0d" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="028a69d9-9e9b-47fc-9409-82c5f3b31550" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="172ce267-0704-4638-a4f9-89f2f5154d0e" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="e7ec60aa-bcd2-4da8-b373-e02253f7c02a" />
        </EntityType>
        <ComplexType Name="DescriptionTranslateType" ed:Guid="d503cfc6-f9ef-411c-af64-0e66c76468fc" ed:GenerateOnlyMapping="True">
          <Property Name="Description" Type="String" ed:ValidateRequired="false" ed:Guid="69283371-dcfd-487a-8900-4a9e28ba8105" />
          <Property Name="DescriptionTr" Type="Int32" ed:ValidateRequired="false" ed:Guid="7019d3f9-9cd2-4cd7-bd4a-5e52a2d17a1c" />
        </ComplexType>
        <Association Name="MDC_ASSET_MASTER_FK2" ed:Guid="af9510f7-b8ec-414c-bb54-6c187fe8834a">
          <End Role="AssetMasterParent" Type="RIB.Visual.Basics.AssetMaster.BusinessComponents.AssetMasterEntity" Multiplicity="0..1" />
          <End Role="AssetMasterChildren" Type="RIB.Visual.Basics.AssetMaster.BusinessComponents.AssetMasterEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="AssetMasterParent">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="AssetMasterChildren">
              <PropertyRef Name="AssetMasterParentFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="AssetMasterEntity_DdTempIdsEntity" ed:Guid="52fc6ddf-1b8c-4466-80f4-4464ebece863">
          <End Role="AssetMasterEntity" Type="RIB.Visual.Basics.AssetMaster.BusinessComponents.AssetMasterEntity" Multiplicity="1" />
          <End Role="DdTempIdsEntities" Type="RIB.Visual.Basics.AssetMaster.BusinessComponents.DdTempIdsEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="AssetMasterEntity">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="DdTempIdsEntities">
              <PropertyRef Name="Id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema>
    </edmx:ConceptualModels>
    <!-- MSL content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="DbContextStoreContainer" CdmEntityContainer="ModelBuilder">
          <EntitySetMapping Name="AssetMasterEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Basics.AssetMaster.BusinessComponents.AssetMasterEntity">
              <MappingFragment StoreEntitySet="MDC_ASSET_MASTERs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="AssetMasterParentFk" ColumnName="MDC_ASSET_MASTER_PARENT_FK" />
                <ScalarProperty Name="MdcContextFk" ColumnName="MDC_CONTEXT_FK" />
                <ScalarProperty Name="Code" ColumnName="CODE" />
                <ScalarProperty Name="AssetMasterLevel1Fk" ColumnName="MDC_ASSET_MASTER_LEVEL1_FK" />
                <ScalarProperty Name="AssetMasterLevel2Fk" ColumnName="MDC_ASSET_MASTER_LEVEL2_FK" />
                <ScalarProperty Name="AssetMasterLevel3Fk" ColumnName="MDC_ASSET_MASTER_LEVEL3_FK" />
                <ScalarProperty Name="AssetMasterLevel4Fk" ColumnName="MDC_ASSET_MASTER_LEVEL4_FK" />
                <ScalarProperty Name="AssetMasterLevel5Fk" ColumnName="MDC_ASSET_MASTER_LEVEL5_FK" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="AddressFk" ColumnName="BAS_ADDRESS_FK" />
                <ScalarProperty Name="IsLive" ColumnName="ISLIVE" />
                <ScalarProperty Name="UserDefined1" ColumnName="USERDEFINED1" />
                <ScalarProperty Name="UserDefined2" ColumnName="USERDEFINED2" />
                <ScalarProperty Name="UserDefined3" ColumnName="USERDEFINED3" />
                <ScalarProperty Name="UserDefined4" ColumnName="USERDEFINED4" />
                <ScalarProperty Name="UserDefined5" ColumnName="USERDEFINED5" />
                <ScalarProperty Name="AllowAssignment" ColumnName="ALLOW_ASSIGNMENT" />
                <ScalarProperty Name="Remark" ColumnName="REMARK" />
                <ComplexProperty Name="DescriptionInfo" TypeName="RIB.Visual.Basics.AssetMaster.BusinessComponents.DescriptionTranslateType">
                  <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                  <ScalarProperty Name="DescriptionTr" ColumnName="DESCRIPTION_TR" />
                </ComplexProperty>
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DdTempIdsEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Basics.AssetMaster.BusinessComponents.DdTempIdsEntity">
              <MappingFragment StoreEntitySet="BAS_DDTEMPIDS">
                <ScalarProperty Name="RequestId" ColumnName="REQUESTID" />
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="Key1" ColumnName="KEY1" />
                <ScalarProperty Name="Key2" ColumnName="KEY2" />
                <ScalarProperty Name="Key3" ColumnName="KEY3" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TranslationEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Basics.AssetMaster.BusinessComponents.TranslationEntity">
              <MappingFragment StoreEntitySet="BAS_TRANSLATIONs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="BasLanguageFk" ColumnName="BAS_LANGUAGE_FK" />
                <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                <ScalarProperty Name="SourceInfo" ColumnName="SOURCEINFO" />
                <ScalarProperty Name="Maxlength" ColumnName="MAXLENGTH" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <edmx:Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </edmx:Connection>
    <edmx:Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="false" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
      </DesignerInfoPropertySet>
    </edmx:Options>
    <edmx:Diagrams>
      <Diagram Name="Main" />
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>