using System.Collections.Generic;
using System;
using RIB.Visual.Awp.Main.BusinessComponents;
using RIB.Visual.Platform.Common;
using RIB.Visual.Cloud.Common.BusinessComponents.Entities;

namespace RIB.Visual.Awp.Main.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public class PackageStructureResourceDto
	{
		/// <summary>
		/// 
		/// </summary>
		public int Id { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string Code { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int EstResourceTypeFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public DescriptionTranslateType DescriptionInfo { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? Quantity { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityTotal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? CostUnit { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? UomFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? BasCurrencyFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal CostTotal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal Budget { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool Selected { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? MdcCostCodeFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? ProjectCostCodeFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? MdcMaterialFk {  get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<Tuple<int, int, int>> LineItemResourceIds { get; set; }


		/// <summary>
		/// 
		/// </summary>
		/// <param name="entity"></param>
		public PackageStructureResourceDto(PackageStructureResourceEntity entity)
		{
			this.Id = entity.Id;
			this.EstResourceTypeFk = entity.EstResourceTypeFk;
			this.Code = entity.Code;
			this.DescriptionInfo = entity.DescriptionInfo;
			this.Quantity = entity.Quantity;
			this.QuantityTotal = entity.QuantityTotal;
			this.CostUnit = entity.CostUnit;
			this.UomFk = entity.UomFk;
			this.BasCurrencyFk = entity.BasCurrencyFk;
			this.CostTotal = entity.CostTotal;
			this.Budget = entity.Budget;
			this.LineItemResourceIds = entity.LineItemResourceIds;
			this.MdcMaterialFk = entity.MdcMaterialFk;
			this.MdcCostCodeFk = entity.MdcCostCodeFk;
			this.ProjectCostCodeFk = entity.ProjectCostCodeFk;
			this.Selected = entity.Selected;
		}
	}
}
