﻿using RIB.Visual.Awp.Main.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents;

namespace RIB.Visual.Awp.Main.ServiceFacade.WebApi
{
    /// <summary>
    /// 
    /// </summary>
    public class AwpPackageBoqItemCompleteDto : BaseCompleteEntity
    {
        /// <summary>
        /// Gets or sets the package BOQ item.
        /// </summary>
        public AwpPackageBoqItemDto ServicePackages { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public AwpPackageBoqItemCompleteDto()
		{
			
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entity"></param>
		public AwpPackageBoqItemCompleteDto(AwpPackageBoqItemCompleteEntity entity)
		{
			this.ServicePackages = new AwpPackageBoqItemDto(entity.ServicePackages);
        }

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public AwpPackageBoqItemCompleteEntity Copy()
        {
            return new AwpPackageBoqItemCompleteEntity()
            {
                MainItemId = MainItemId,
                ServicePackages = this.ServicePackages?.Copy()
            };
        }
    }
}
