﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// 
	/// </summary>
	[Flags]
	public enum ApiDocPropertyIgnoreTypeEnum
	{
		/// <summary>
		/// 
		/// </summary>
		ForRequest = 1,

		/// <summary>
		/// 
		/// </summary>
		ForResponse = 2
	}

	/// <summary>
	/// Provides meta info from the webapi method to help webapihelp to generate docs automatically.
	/// </summary>
	[AttributeUsage(AttributeTargets.Property)]
	public class ApiDocDtoPropertyIgnoreAttribute : Attribute
	{
		/// <summary>
		/// 
		/// </summary>
		public ApiDocPropertyIgnoreTypeEnum IgnoreType { get; set; }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="ignoreType"></param>
		public ApiDocDtoPropertyIgnoreAttribute(ApiDocPropertyIgnoreTypeEnum ignoreType = ApiDocPropertyIgnoreTypeEnum.ForRequest|ApiDocPropertyIgnoreTypeEnum.ForResponse)
		{
			this.IgnoreType = ignoreType;
		}
	}
}
