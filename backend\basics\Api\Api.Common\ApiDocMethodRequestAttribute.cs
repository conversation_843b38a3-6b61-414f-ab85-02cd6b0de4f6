﻿/*
 * $Id: ApiDocMethodRequestAttribute.cs 577880 2020-03-02 06:09:59Z haagf $
 * Copyright(c): RIB Software SE
 */

using System;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Provides meta info from the webapi method to help webapihelp to generate docs automatically.
	/// </summary>
	[AttributeUsage(AttributeTargets.Method)]
	public class ApiDocMethodRequestAttribute : ApiDocAttribute, IDtoDescriptorAttribute
	{
		/// <summary>
		/// Initializes a new instance with a name.
		/// </summary>
		public ApiDocMethodRequestAttribute(string name)
		{
			this.Name = name;
		}

		/// <summary>
		/// Initializes a new instance with a name and a type.
		/// </summary>
		public ApiDocMethodRequestAttribute(string name, Type type) : this(name)
		{
			this.Type = type;
		}

		/// <summary>
		/// Specify the incoming request name.
		/// </summary>
		public string Name
		{
			get;
			set;
		}

		/// <summary>
		/// Specify the incoming request type.
		///   If <see langword="null"/>, the declared type of the underlying method will be used.
		/// </summary>
		public Type Type
		{
			get;
			set;
		}

		/// <summary>
		/// Specify the special request properties not shown in webAPI docs.
		/// </summary>
		public string[] ExcludingProperties
		{
			get;
			set;
		}

		/// <summary>
		/// Gets or sets a value that indicates whether properties that belong to the primary key should be excluded.
		/// </summary>
		public Boolean ExcludingKeyProperties
		{
			get;
			set;
		}

		/// <summary>
		/// If set, the type supplied for the specified type parameter will be used.
		/// </summary>
		public String TypeParamName { get; set; }

		/// <summary>
		/// Indicates whether an array of elements of the indicated type is used.
		/// </summary>
		public Boolean AsArray { get; set; }
	}
}
