using System;
using System.IO;
using System.IO.Compression;
using System.Net;
using System.Net.Http;
using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;
using System.Net.Http.Headers;

namespace RIB.Visual.Basics.Api.Client.Itwo40WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public static class RevitUploadServices
	{
		/// <summary/>
		public static string Token { get; set; }
		/// <summary/>
		// ReSharper disable once MemberCanBePrivate.Global
		public static string ServicesUrl { get; set; }
		/// <summary/>
		// ReSharper disable once MemberCanBePrivate.Global
		public static ServiceClientContext ServiceClientContext { get; set; }


		private static Response AnalyseHttpStatusCode(HttpResponseMessage response)
		{
			var fctRespnse = new Response() { Result = false };
			if (response.StatusCode == HttpStatusCode.BadRequest)
			{
				var result = response.Content.ReadAsStringAsync().Result;
				//Console.WriteLine(JArray.Parse(result));
				try
				{
					var jObject = JObject.Parse(result);
					var jResult = JsonConvert.SerializeObject(jObject, Formatting.Indented);
					fctRespnse.ResponseValue = response + "\n" + jResult;
				}
				catch (Exception)
				{
					fctRespnse.ResponseValue = result;
				}
				return fctRespnse;
			}
			fctRespnse.ResponseValue = response.ToString();
			return fctRespnse;
		}


		private static void SetTokenClientContext(HttpClient client)
		{
			//client.SetBearerToken(Token); // set token from identity server into web-api header
			client.DefaultRequestHeaders.Authorization= new AuthenticationHeaderValue("Bearer",Token);
			var clientContext = (ServiceClientContext ?? new ServiceClientContext()).ToString(Formatting.None);
			client.DefaultRequestHeaders.Add("Client-Context", clientContext);

		}

		private static Response ReadResponseValue(HttpResponseMessage response, Response fctResponse)
		{
			var theResponse = response.Content.ReadAsStringAsync().Result;
			fctResponse.ResponseValue = theResponse;
			fctResponse.ObjValue = JsonConvert.DeserializeObject(theResponse) as JObject;
			fctResponse.Result = true;
			return fctResponse;
		}


		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public static string GetTokenClaimsInfo()
		{
			var str = new StringBuilder();
			var x = new System.IdentityModel.Tokens.Jwt.JwtSecurityToken(Token);
			if (x.Payload != null)
			{
				foreach (var claim in x.Payload)
				{
					str.AppendLine(string.Format("Claim: {0}\t{1}", claim.Key, claim.Value));
				}
			}
			str.AppendLine(string.Format("ValidFrom: \t{0}", x.ValidFrom));
			str.AppendLine(string.Format("ValidTo:   \t{0}", x.ValidTo));
			return str.ToString();
		}


		#region Get Revit Models From Projects
		/// <summary> </summary>
		/// <returns></returns>
		public static async Task<Response> GetRevitModelsFromProjectsAsync()
		{
			string fctname = "GetRevitModelsFromProjects";

			Console.WriteLine("{0} - Start", fctname);

			var t = new Task<Response>(GetRevitModelsFromProjects);
			t.Start();
			var responseResult = await t;

			Console.WriteLine("{0} - Done", fctname);
			return responseResult;
		}

		/// <summary>
		/// 
		/// </summary>
		private static Response GetRevitModelsFromProjects()
		{
			var fctResponse = new Response() { Result = false };

			using (var client = new HttpClient())
			{
				SetTokenClientContext(client);

				try
				{
					// send Get Call to Backend
					var url = ServicesUrl + Constant.GetRevitModelsFromProjects;
					var response = client.GetAsync(url).Result;

					if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.ResetContent)
						return AnalyseHttpStatusCode(response);

					return ReadResponseValue(response, fctResponse);
				}
				catch (Exception ex)
				{
					fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
					return fctResponse;
				}
			}
		}

		#endregion


		#region Model Upload Begin

		/// <summary> </summary>
		/// <returns></returns>
		public static async Task<Response> UploadModelBeginAsync(int modelId)
		{
			Console.WriteLine("UploadModelBegin - Start");
			var t = new Task<Response>(() => UploadModelBegin(modelId));
			t.Start();
			var result = await t;
			Console.WriteLine("UploadModelBegin - Done");
			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="modelId"></param>
		/// <returns></returns>
		private static Response UploadModelBegin(int modelId)
		{
			var fctResponse = new Response() { Result = false };
			using (var client = new HttpClient())
			{
				SetTokenClientContext(client);

				try
				{
					// send Get Call to Backend
					var url = ServicesUrl + Constant.ModelUpLoadBegin + string.Format("?model={0}", modelId);
					var response = client.GetAsync(url).Result;

					if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.ResetContent)
						return AnalyseHttpStatusCode(response);

					var theResponse = response.Content.ReadAsStringAsync().Result;
					fctResponse.ResponseValue = JsonConvert.DeserializeObject(theResponse) as string;
					fctResponse.Result = true;

					return fctResponse;
				}
				catch (Exception ex)
				{
					fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
					return fctResponse;
				}
			}
		}

		#endregion

		#region Model Upload End

		/// <summary> </summary>
		/// <returns></returns>
		public static async Task<Response> UploadModelEndAsync(string uploadUuid, bool commit)
		{
			Console.WriteLine("UploadModelEnd - Start");
			var t = new Task<Response>(() => UploadModelEnd(uploadUuid, commit));
			t.Start();
			var result = await t;
			Console.WriteLine("UploadModelEnd - Done");
			return result;
		}


		private static Response UploadModelEnd(string uploadUuid, bool commit)
		{
			var fctResponse = new Response() { Result = false };
			using (var client = new HttpClient())
			{
				SetTokenClientContext(client);

				try
				{
					// send Get Call to Backend
					var url = ServicesUrl + Constant.ModelUpLoadEnd + string.Format("?uuid={0}&commit={1}", uploadUuid, commit ? "true" : "false");

					var response = client.GetAsync(url).Result;

					if (response.StatusCode == HttpStatusCode.OK || response.StatusCode == HttpStatusCode.ResetContent)
					{
						var theResponse = response.Content.ReadAsStringAsync().Result;
						fctResponse.ResponseValue = JsonConvert.DeserializeObject(theResponse) as string;
						fctResponse.Result = true;
						return fctResponse;
					}

					return AnalyseHttpStatusCode(response);
				}
				catch (Exception ex)
				{
					fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
					return fctResponse;
				}
			}
		}

		#endregion

		#region Model Upload Data

		//Compress stream
		private static MemoryStream Compress(MemoryStream ms)
		{
			byte[] buffer = new byte[ms.Length];
			// Use the newly created memory stream for the compressed data.
			GZipStream compressedzipStream = new GZipStream(ms, CompressionMode.Compress, true);
			compressedzipStream.Write(buffer, 0, buffer.Length);
			// Close the stream.
			compressedzipStream.Close();
			MemoryStream ms1 = new MemoryStream(buffer);
			return ms1;
		}

		// ReSharper disable once UnusedMember.Local
		private static void Test(string t)
		{
			using (var memStream = new MemoryStream())
			{
				// var data = new DataContractJsonSerializer(typeof(PostParam));
				//data.WriteObject(memStream, testParam);
				var ascii = new ASCIIEncoding();
				var buff = new byte[t.Length];
				ascii.GetBytes(t, 0, t.Length, buff, 0);

				memStream.Read(buff, 0, 56);
				var contentToPost = new StreamContent(Compress(memStream));
				contentToPost.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/json");
				contentToPost.Headers.Add("Content-Encoding", "gzip");
				//var response = client.PostAsync(new Uri("http://myapi/SAVE"), contentToPost).Result;

				// var dataReceived = response.EnsureSuccessStatusCode();
			}
		}

		public static async Task<Response> UploadModelDataAsync(string uploadUuid, int partIndex, string modelPart)
		{
			Console.WriteLine("UploadModelData - Start");
			var t = new Task<Response>(() => UploadModelData(uploadUuid, partIndex, modelPart));
			t.Start();
			var result = await t;
			Console.WriteLine("UploadModelData - Done");
			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="uploadUuid"></param>
		/// <param name="partIndex"></param>
		/// <param name="modelPart"></param>
		/// <returns></returns>
		private static Response UploadModelData(string uploadUuid, int partIndex, string modelPart)
		{
			var fctResponse = new Response() { Result = false };
			using (var client = new HttpClient())
			{
				SetTokenClientContext(client);

				var content = modelPart;
				//	String.Format(@"{{""partIndex"":{0},""content"":""{1}""}}",partIndex, modelPart);
				var myContent = new StringContent(content, Encoding.UTF8, "application/json");		// defined communication format and add parameter content into it

				try
				{
					// post call to iTWO Cloud backend server
					var response = client.PostAsync(ServicesUrl + Constant.ModelUpLoadData + string.Format("?uuid={0}&index={1}",
						uploadUuid, partIndex), myContent).Result;

					if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.ResetContent)
						return AnalyseHttpStatusCode(response);

					var theResponse = response.Content.ReadAsStringAsync().Result;
					fctResponse.ResponseValue = JsonConvert.DeserializeObject(theResponse) as string;
					fctResponse.Result = true;
					return fctResponse;

				}
				catch (Exception ex)
				{
					fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
					return fctResponse;
				}
			}
		}

		#endregion

		#region
		//		public CompanyCheckResult CheckCompanyToAssignedCompanies(int? requestedSignedInCompanyId = null, int? requestedCompanyId = null, int? requestedPermissionClientId = null, int? requestedRoleId = null)

		public static async Task<Response> CheckCompanyToAssignedCompaniesAsync(int requestedSignedInCompanyId, int requestedCompanyId, int requestedPermissionClientId, int requestedRoleId)
		{

			Console.WriteLine("CheckCompanyToAssignedCompanies - Start");
			// ReSharper disable once ConvertClosureToMethodGroup
			var t = new Task<Response>(() => CheckCompanyToAssignedCompanies(requestedSignedInCompanyId, requestedCompanyId, requestedPermissionClientId, requestedRoleId));
			t.Start();
			var responseResult = await t;
			Console.WriteLine("CheckCompanyToAssignedCompanies - Done");
			return responseResult;
		}

		private static Response CheckCompanyToAssignedCompanies(int requestedSignedInCompanyId, int requestedCompanyId, int requestedPermissionClientId, int requestedRoleId)
		{
			var fctResponse = new Response() { Result = false };
			using (var client = new HttpClient())
			{
				SetTokenClientContext(client);

				try
				{
					// send Get Call to Backend
					var url = ServicesUrl + Constant.CheckCompaniesWithRoles +
					string.Format("?requestedSignedInCompanyId={0}&requestedCompanyId={1}&requestedPermissionClientId={2}&requestedRoleId={3}",
						requestedSignedInCompanyId, requestedCompanyId, requestedPermissionClientId, requestedRoleId);

					var response = client.GetAsync(url).Result;

					if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.ResetContent)
						return AnalyseHttpStatusCode(response);

					return ReadResponseValue(response, fctResponse);

				}
				catch (Exception ex)
				{
					fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
					return fctResponse;
				}
			}
		}


		#endregion

		#region Get Companies With Roles
		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public static async Task<Response> GetCompaniesWithRolesAsync()
		{

			Console.WriteLine("GetCompaniesWithRoles - Start");
			// ReSharper disable once ConvertClosureToMethodGroup
			var t = new Task<Response>(() => GetCompaniesWithRoles());
			t.Start();
			var responseResult = await t;
			Console.WriteLine("GetCompaniesWithRoles - Done");

			return responseResult;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		private static Response GetCompaniesWithRoles()
		{
			var fctResponse = new Response() { Result = false };

			using (var client = new HttpClient())
			{
				SetTokenClientContext(client);

				try
				{
					// send Get Call to Backend
					var url = ServicesUrl + Constant.GetAssignedCompaniesWithRoles;
					var response = client.GetAsync(url).Result;

					if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.ResetContent)
						return AnalyseHttpStatusCode(response);

					return ReadResponseValue(response, fctResponse);

				}
				catch (Exception ex)
				{
					fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
					return fctResponse;
				}
			}
		}

		#endregion

		#region Model Upload Progress
		/// <summary> </summary>
		/// <returns></returns>
		public static async Task<Response> UploadModelProgressAsync(string txUuid)
		{
			string fctname = "GetModelUploadProgressAsync";
			Console.WriteLine("{0} - Start", fctname);
			var task = new Task<Response>(() => UploadModelProgress(txUuid));
			task.Start();
			var taskresult = await task;
			Console.WriteLine("{0} - Done", fctname);
			return taskresult;
		}


		/// <summary>
		/// 
		/// </summary>
		/// <param name="uploadUuid"></param>
		/// <returns></returns>
		private static Response UploadModelProgress(string uploadUuid)
		{
			var fctResponse = new Response() { Result = false };
			using (var client = new HttpClient())
			{
				SetTokenClientContext(client);

				try
				{
					// send Get Call to Backend
					var url = ServicesUrl + Constant.ModelUpLoadProgress + string.Format("?txUuid={0}", uploadUuid);

					var response = client.GetAsync(url).Result;

					if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.ResetContent)
						return AnalyseHttpStatusCode(response);

					return ReadResponseValue(response, fctResponse);

				}
				catch (Exception ex)
				{
					fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
					return fctResponse;
				}
			}
		}

		#endregion

		#region itwo40 login
		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		public static async Task<string> DoLoginAsync(Model theModel)
		{
			Console.WriteLine("DoLoginAsync - Start");
			var t = new Task<string>(() => DoLogin(theModel));
			t.Start();
			var result = await t;
			Console.WriteLine("DoLoginAsync - Done");
			return result;
		}

		/// <summary>
		/// This method call the iTWOCloud WEB-Api. 
		/// 
		/// Header will be enhanced by token and context
		/// 
		/// </summary>
		public static string DoLogin(Model theModel)
		{
			string result = null;

			using (var client = new HttpClient())
			{
				var logonUri = string.Format("{0}{1}?username={2}&password={3}", ServicesUrl, "/basics/api/apilogon", theModel.Username, theModel.GetUnsecurePassword());
				try
				{
					var response = client.GetAsync(logonUri).Result;
					if (response.StatusCode == HttpStatusCode.OK)
					{
						result = JsonConvert.DeserializeObject<string>(response.Content.ReadAsStringAsync().Result);
					}
				}
				catch (AccessViolationException ex)
				{
					result = ex.Message + "\r\nLogin failed wrong username/password! Error: " + ex.StackTrace;
					return result;
				}
				catch (Exception ex)
				{
					result = ex.Message + "\r\nLogin failed! other failure, Error: " + ex.StackTrace;
					return result;
				}
			}
			return result;
		}

		#endregion login
	}
}