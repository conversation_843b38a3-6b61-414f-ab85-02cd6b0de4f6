//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON>art Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Data.Common;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.ModelConfiguration;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Linq;
using System.Runtime.Serialization;
using System.Xml.Serialization;
using RIB.Visual.Platform.Common;
using System.Data.Entity.Core.EntityClient;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Core.Objects.DataClasses;
using RIB.Visual.Basics.Api.BusinessComponents;

namespace RIB.Visual.Basics.Api.BusinessComponents
{
    /// <summary/>
    public partial class ModelBuilder
    {
		#region Constructors
		/// <summary>
		/// Initialize a new ModelBuilder object.
		/// </summary>
		public ModelBuilder()
    {
		}
		#endregion

		static private readonly Object _locking = new Object();
		static private DbCompiledModel _model;

		/// <summary>
		/// Creates a compiled entity model
		/// </summary>
		static public DbCompiledModel DbModel
		{
			get
			{
				if (_model == null)
				{
					lock (_locking)
					{
						if (_model == null)
						{
							var modelBuilder = new DbModelBuilder();

							AddMappings(modelBuilder);
							AddAdditionalMappings(modelBuilder);

							modelBuilder.Conventions.Remove<StoreGeneratedIdentityKeyConvention>();

							_model = modelBuilder.Build(Platform.BusinessComponents.DbContext.CreateConnection()).Compile();
						}
					}
				}

				return _model;
			}
		}

		// partial method to add special/additional mappings
		static partial void AddAdditionalMappings(DbModelBuilder modelBuilder);

		/// <summary>
		/// Adds the mapping for each entity of this db context.
		/// </summary>
		/// <param name="modelBuilder"></param>
		static public void AddMappings(DbModelBuilder modelBuilder)
		{

            #region DdTempIdsEntity

            modelBuilder.Entity<DdTempIdsEntity>()
                .HasKey(p => new { p.Id, p.RequestId })
                .ToTable("BAS_DDTEMPIDS");
            // Properties:
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.RequestId)
                    .HasColumnName(@"REQUESTID")
                    .IsRequired()
                    .HasMaxLength(32)
                    .HasColumnType("char");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<DdTempIdsEntity>(modelBuilder);

            #endregion

            #region ApiRequestItemEntity

            modelBuilder.Entity<ApiRequestItemEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("BAS_APIREQUESTITEM");
            // Properties:
            modelBuilder.Entity<ApiRequestItemEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ApiRequestItemEntity>()
                .Property(p => p.RequestId)
                    .HasColumnName(@"REQUESTID")
                    .IsRequired()
                    .HasMaxLength(32)
                    .HasColumnType("varchar");
            modelBuilder.Entity<ApiRequestItemEntity>()
                .Property(p => p.ModuleName)
                    .HasColumnName(@"MODULENAME")
                    .IsRequired()
                    .HasMaxLength(252)
                    .HasColumnType("varchar");
            modelBuilder.Entity<ApiRequestItemEntity>()
                .Property(p => p.RequestContext)
                    .HasColumnName(@"REQUESTCONTEXT")
                    .HasColumnType("text");
            modelBuilder.Entity<ApiRequestItemEntity>()
                .Property(p => p.ItemData)
                    .HasColumnName(@"ITEMDATA")
                    .HasColumnType("text");
            modelBuilder.Entity<ApiRequestItemEntity>()
                .Property(p => p.Status)
                    .HasColumnName(@"STATUS")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ApiRequestItemEntity>()
                .Property(p => p.ValidUntil)
                    .HasColumnName(@"VALIDUNTIL")
                    .HasColumnType("datetime");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ApiRequestItemEntity>(modelBuilder);

            #endregion

            #region ComplexTypes

            modelBuilder.ComplexType<DescriptionTranslateType>();

            #endregion

            #region Disabled conventions


            #endregion

        }

    
        /// <summary>
        /// There are no comments for DdTempIdsEntity in the schema.
        /// </summary>
        public DbSet<DdTempIdsEntity> DdTempIdsEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ApiRequestItemEntity in the schema.
        /// </summary>
        public DbSet<ApiRequestItemEntity> ApiRequestItemEntities { get; set; }
    }
}
