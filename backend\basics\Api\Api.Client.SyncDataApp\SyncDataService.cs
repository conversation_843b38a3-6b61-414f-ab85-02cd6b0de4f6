﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;

namespace RIB.Visual.Basics.Api.Client.SyncDataApp
{
	/// <summary>
	/// 
	/// </summary>
	public class SyncDataService
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="token"></param>
		/// <param name="serviceUrl"></param>
		/// <param name="result"></param>
		/// <returns></returns>
		public static string SyncData(string token, IdentityModel model)
		{
			using (var client = new HttpClient())
			{
				//client.SetBearerToken(token);		// set token from identity server into web-api header
				client.DefaultRequestHeaders.Authorization= new AuthenticationHeaderValue("Bearer",token);
				client.DefaultRequestHeaders.Add("Client-Context", "{}"); // we set client context to empty, will be place into web-call client header
				//client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
				if (!model.CheckCertificate)
				{
					ServicePointManager.ServerCertificateValidationCallback += (sender, cert, chain, sslPolicyErrors) => true;
				}
				try
				{
					var response = client.GetAsync(model.ApiBaseUrl + "/procurement/package/publicapi/import").Result;
					if (response.IsSuccessStatusCode)
					{
						var result = response.Content.ReadAsStringAsync().Result;
						return result.ToLower().Replace("\"", "");
					}

					return "Call webapi failed,the reason is:\n" + response.ToString();
				}
				catch (AggregateException ex)
				{
					//The remote certificate is invalid according to the validation procedure.
					return ex.Message + "\r\n" + ex.StackTrace;
				}
				catch (Exception ex)
				{
					return ex.Message + "\r\n" + ex.StackTrace;
				}
			}
		}
	}
}
