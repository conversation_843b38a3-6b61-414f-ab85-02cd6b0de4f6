using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.InteropServices;
using System.Security;

namespace RIB.Visual.Basics.Api.Client.Itwo40WebApi
{
	/// <summary/> 
	public class Model : INotifyPropertyChanged
	{
		private string _baseUrl;
		private string _clientUrl;
		private string _jsonOutput;
		private string _identityServerUrl;
		private string _operation;
		private Dictionary<string, string> _operation2UrlMap = new Dictionary<string, string>();

		/// <summary>
		/// 
		/// </summary>
		public Model()
		{
			Password = new SecureString();
			SearchPattern = "Felde,";
			BaseUrls = new string[] { Constant.BaseUrl1, Constant.BaseUrl2, Constant.BaseUrl3, Constant.BaseUrl4, Constant.BaseUrl5, Constant.BaseUrl6 };
			IdentityServerUrls = new string[]
			{
				Constant.IdentityServerUrl1, Constant.IdentityServerUrl2, Constant.IdentityServerUrl3,
				Constant.IdentityServerUrl4, Constant.IdentityServerUrl5, Constant.IdentityServerUrl6,
				Constant.IdentityServerUrl7
			};
			Operations = new string[] { Constant.OperationInquiry, Constant.OperationLookup, Constant.OperationLookupMaterial };
			_operation2UrlMap.Add(Constant.OperationInquiry, Constant.OperationRelUrlInquiry);
			_operation2UrlMap.Add(Constant.OperationLookup, Constant.OperationRelUrlLookup);
			_operation2UrlMap.Add(Constant.OperationLookupMaterial, Constant.OperationRelUrlMaterial);
		}

		/// <summary/> 
		public Guid RequestId { get; set; }

		/// <summary/> 
		public string RequestIdAsString { get { return RequestId.ToString("N"); } }

		/// <summary/> 
		public string Username { get; set; }
		/// <summary/> 
		public SecureString Password { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string[] BaseUrls { get; set; }
		
		/// <summary>
		/// 
		/// </summary>
		public string[] IdentityServerUrls { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string[] Operations { get; set; } 
	
		/// <summary/> 
		public string BaseUrl
		{
			get { return _baseUrl; }
			set
			{
				if (_baseUrl != value)
				{
					_baseUrl = value.EndsWith(@"/") ? value : value + @"/";
					ClientUrl = _baseUrl + "client";
					ServicesUrl = _baseUrl + "services";

					OnPropertyChanged("BaseUrl");
					OnPropertyChanged("ClientUrl");
					OnPropertyChanged("ServicesUrl");
				}
			}
		}


		/// <summary/> 
		public string ClientUrl
		{
			get { return _clientUrl; }
			set { _clientUrl = value; OnPropertyChanged("ClientUrl"); }
		}

		/// <summary/> 
		public string ServicesUrl { get; set; }

		/// <summary/> 
		public string IdentityServerUrl
		{
			get { return _identityServerUrl; }
			set { _identityServerUrl = value; OnPropertyChanged("IdentityServerUrl"); }
		}

		/// <summary/> 
		public string Operation 
		{
			get { return _operation; }
			set
			{
				_operation = value;
				OperationRelUrl = _operation2UrlMap[_operation];

				OnPropertyChanged("Operation"); 
			}
		}

		public string OperationRelUrl { get; set; }

		/// <summary/> 
		public string CompanyCode { get; set; }

		/// <summary/> 
		public string SearchPattern { get; set; }

		/// <summary/> 
		public string JsonOutput
		{
			get { return _jsonOutput; }
			set{_jsonOutput = value;OnPropertyChanged("JsonOutput");}
		}

		/// <summary/> 
		public void SetToSecurePassword(string strPassword)
		{
			Password = new SecureString();
			if (strPassword.Length > 0)
			{
				foreach (var c in strPassword.ToCharArray()) Password.AppendChar(c);
			}
		}

		/// <summary/> 
		public string GetUnsecurePassword()
		{
			IntPtr unmanagedString = IntPtr.Zero;
			try
			{
				unmanagedString = Marshal.SecureStringToGlobalAllocUnicode(Password);
				return Marshal.PtrToStringUni(unmanagedString);
			}
			finally
			{
				Marshal.ZeroFreeGlobalAllocUnicode(unmanagedString);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		public event PropertyChangedEventHandler PropertyChanged;


		protected virtual void OnPropertyChanged(string propertyName)
		{
			if (this.PropertyChanged != null)
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
			}
		}

		/// <summary>
		/// 
		/// </summary>
		public void NotifyChanged()
		{
			OnPropertyChanged("SearchPattern");
			OnPropertyChanged("CompanyCode");
			OnPropertyChanged("Username");
			// OnPropertyChanged("IdentityServerUrl");
			OnPropertyChanged("BaseUrl");
		}
	}
}