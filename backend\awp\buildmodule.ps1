###################################################################################
## module build file
## author:      <PERSON>
## date:        04.03.2021
###################################################################################
param (
    [string]$cfg = "debug", # valid: debug | release
    [string]$buildopt = "rebuild"         # valid: build | rebuild
)

$buildtools = Join-Path $psscriptroot "..\..\binpool\buildtools\compilesolution.ps1"
if (-not (Test-Path $buildtools)) {
    Write-Error "module compile file not found, verify your enviroment"
    exit
}
.$buildtools
$modulesrootfolder = $PSScriptRoot

## put here your module list
$modulesolutions = @( @{ config = @{iscore = $false; assemblynamespace = "namespace" }; solutionlist = @( `
            @{Solution = "Main"; Solutionfile = "RIB.Visual.Awp.Main"; Remark = "" }; `
            #@{Solution = "submodule"; Solutionfile = "namespace.submodule"; Remark = "" }; `
            #@{Solution = "submodule"; Solutionfile = "namespace.submodule"; Remark = "" }; `
        )
    }; )

### force compilation of modules
CompileModule -config $cfg -buildopt $buildopt -modulefolder $modulesrootfolder -modulesolutionslist $modulesolutions



