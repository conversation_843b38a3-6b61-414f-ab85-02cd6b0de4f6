using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.InteropServices;
using System.Security;

namespace RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample
{

	/// <summary>
	/// This class holds all the information for the services like
	/// client Url and services Url calculate from BaseUrl
	/// 
	/// Make it easier to pass parameters via the asynch services....
	/// 
	/// </summary>
	public class ServiceData 
	{
		private string _baseUrl;
		private string _jsonOutput;

		/// <summary>
		/// 
		/// </summary>
		public ServiceData()
		{
			Password = new SecureString();
		}

		/// <summary/> 
		public string Username { get; set; }

		/// <summary/> 
		public SecureString Password { get; set; }
		
		/// <summary/> 
		public string BaseUrl
		{
			get { return _baseUrl; }
			set
			{
				if (_baseUrl != value)
				{
					_baseUrl = value.EndsWith(@"/") ? value : value + @"/";
					ClientUrl = _baseUrl + "client";
					ServicesUrl = _baseUrl + "services";
				}
			}
		}


		/// <summary/> 
		public string ClientUrl { get; set; }

		/// <summary/> 
		public string ServicesUrl { get; set; }

		/// <summary/> 
		public string CompanyCode { get; set; }


		/// <summary/> 
		public string JsonOutput
		{
			get { return _jsonOutput; }
			set { _jsonOutput = value; 
			}
		}

		/// <summary/> 
		public void SetToSecurePassword(string strPassword)
		{
			Password = new SecureString();
			if (strPassword.Length > 0)
			{
				foreach (var c in strPassword.ToCharArray()) Password.AppendChar(c);
			}
		}

		/// <summary/> 
		public string GetUnsecurePassword()
		{
			IntPtr unmanagedString = IntPtr.Zero;
			try
			{
				unmanagedString = Marshal.SecureStringToGlobalAllocUnicode(Password);
				return Marshal.PtrToStringUni(unmanagedString);
			}
			finally
			{
				Marshal.ZeroFreeGlobalAllocUnicode(unmanagedString);
			}
		}

	}
}