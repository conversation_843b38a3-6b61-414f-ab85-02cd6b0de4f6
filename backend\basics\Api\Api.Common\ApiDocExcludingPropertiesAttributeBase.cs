﻿
using System;
using System.Linq;

namespace RIB.Visual.Basics.Api.Common
{

	/// <summary>
	/// Declares properties will be excluding in API documentation comments.
	/// </summary>
	public abstract class ApiDocExcludingPropertiesAttributeBase : Attribute
	{
		/// <summary>
		/// Initializes a new instance.
		/// </summary>
		/// <param name="actionName">The name of the action that will be effected.</param>
		/// <param name="excludingProperties">The properties of dto that will be excluded in API documentation comment.</param>
		/// <exception cref="ArgumentNullException">Any of the arguments is <see langword="null"/>.</exception>
		protected ApiDocExcludingPropertiesAttributeBase(String actionName, String[] excludingProperties)
			: this(actionName, excludingProperties == null ? null : string.Join(",", excludingProperties))
		{

		}

		/// <summary>
		/// Initializes a new instance.
		/// </summary>
		/// <param name="actionName">The name of the action that will be effected.</param>
		/// <param name="excludingProperties">The properties of dto that will be excluded in API documentation comment. it is split by ','.</param>
		/// <exception cref="ArgumentNullException">Any of the arguments is <see langword="null"/>.</exception>
		protected ApiDocExcludingPropertiesAttributeBase(String actionName, String excludingProperties)
		{
			if (string.IsNullOrWhiteSpace(actionName))
			{
				throw new ArgumentNullException("actionName");
			}

			ActionName = actionName;

			//if (string.IsNullOrWhiteSpace(excludingProperties))
			//{
			//	throw new ArgumentNullException("excludingProperties");
			//}

			ExcludingProperties = excludingProperties;
		}

		/// <summary>
		/// The name of the action that will be effected.
		/// </summary>
		public String ActionName { get; private set; }

		/// <summary>
		/// The properties of dto that will be excluded in API documentation comment. it is split by ','.
		/// </summary>
		public String ExcludingProperties { get; private set; }

		/// <summary>
		/// Expands the value.
		/// </summary>
		/// <returns></returns>
		public virtual String[] Expand()
		{
			if (!string.IsNullOrWhiteSpace(ExcludingProperties))
			{
				var result = ExcludingProperties.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Select(e => e.Trim()).ToArray();
				return result;
			}

			return new string[0];
		}
	}
}
