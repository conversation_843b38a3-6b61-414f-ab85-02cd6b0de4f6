using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.AuditTrail.BusinessComponents
{

		/// <summary/>
    public partial class AudColumnEntity
    {

        ///// <summary>
        ///// uncomment this function for beeing called while constructing entity
        ///// </summary>
        //partial void OnConstruct() 
        //{
        //}

        ///// <summary>
        ///// uncomment this function for being called while cloning the entity
        ///// </summary>
        //partial void OnClone(AudColumnEntity clonedEntity) 
        //{
        //}
    }
}
