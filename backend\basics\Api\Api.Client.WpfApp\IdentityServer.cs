﻿using System;
using System.Text;
using IdentityModel;
using IdentityModel.Client;
using Newtonsoft.Json.Linq;
//using Thinktecture.IdentityModel.Client;

namespace RIB.Visual.Basics.Api.Client.WpfApp
{
	/// <summary/>
	public static class IdentityServer
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="response"></param>
		/// <returns></returns>
		public static string ShowResponse(TokenResponse response)
		{
			var msg = new StringBuilder();

			if (!response.IsError)
			{
				msg.AppendLine("Token response:");
				msg.AppendLine(response.Json.ToString());

				if (response.AccessToken.Contains("."))
				{
					msg.AppendLine("\nAccess Token (decoded):");

					var parts = response.AccessToken.Split('.');
					var header = parts[0];
					var claims = parts[1];

					msg.AppendLine(JObject.Parse(Encoding.UTF8.GetString(Base64Url.Decode(header))).ToString());
					msg.AppendLine(JObject.Parse(Encoding.UTF8.GetString(Base64Url.Decode(claims))).ToString());
				}
			}
			else
			{
				if (response.IsError)
				{
					msg.AppendLine("HTTP error: ");
					msg.AppendLine(response.HttpStatusCode.ToString());
					msg.AppendLine("HTTP error reason: ");
					msg.AppendLine(response.HttpErrorReason);
				}
				else
				{
					msg.AppendLine("Protocol error response:");
					msg.AppendLine(response.Json.ToString());
				}

			}
			return msg.ToString();
		}

		/// <summary>
		/// This method requests a token from iTWO Cloud identity server via username/password
		/// 
		/// clientId and clientSecret are const values and required for communication with identity server.
		/// 
		/// </summary>
		/// <param name="username"></param>
		/// <param name="password"></param>
		/// <param name="idsrv"></param>
		/// <returns></returns>
		private static TokenResponse RequestToken(string username, string password, string idsrv = Constant.IdentityServerUrl1)
		{
			const string clientId = "iTWO.Cloud";
			const string clientSecret = "{fec4c1a6-8182-4136-a1d4-81ad1af5db4a}";
			const string scope = "default";
			var client = new System.Net.Http.HttpClient();
			var ret = client.RequestPasswordTokenAsync(new PasswordTokenRequest
			{
				Address = idsrv,
				ClientId = clientId,
				ClientSecret = clientSecret,
				UserName = username,
				Password = password,
				Scope = scope
			}).Result;
			return ret;
		}


		/// <summary>
		/// 
		/// </summary>
		/// <param name="username"></param>
		/// <param name="password"></param>
		/// <returns></returns>
		public static TokenResponse Login(string username, string password)
		{
			TokenResponse token = RequestToken(username, password);

			return token;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <param name="token"></param>
		/// <returns></returns>
		public static string Login(Model theModel, out TokenResponse token)
		{
			token = null;
			try
			{
				token = RequestToken(theModel.Username, theModel.GetUnsecurePassword(), theModel.IdentityServerUrl);
				return "Login valid ";

			}
			catch (Exception ex)
			{
				return ex.Message + "\n\r" + ex.StackTrace;
			}
		}


	}
}