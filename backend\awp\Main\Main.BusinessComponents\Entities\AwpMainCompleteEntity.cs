using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.Core;

namespace RIB.Visual.Awp.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class AwpMainCompleteEntity : BaseCompleteEntity
	{
		/// <summary>
		/// 
		/// </summary>
		public AwpMainCompleteEntity()
		{

		}

		#region item assignment
		/// <summary>
		/// 
		/// </summary>

		[EntityUpdate(typeof(IDataBaseLogic), "Procurement.Common.PrcItemAssignmentLogic", EntityUpdateAttribute.UpdateFilter.Save)]
		public IEnumerable<IIdentifyable> AwpPackageItemAssignmentToSave
		{
			get; set;
		}
        #endregion

        /// <summary>
        /// 
        /// </summary>
        public IEnumerable<AwpPackageBoqItemCompleteEntity> ServicePackagesToSave { get; set; }
    }
}
