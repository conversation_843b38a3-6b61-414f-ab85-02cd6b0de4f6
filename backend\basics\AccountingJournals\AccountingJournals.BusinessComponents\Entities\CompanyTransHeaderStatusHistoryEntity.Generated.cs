﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.AccountingJournals.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Basics.AccountingJournals.BusinessComponents.CompanyTransHeaderStatusHistoryEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("BAS_COMPANYTRNHDSTATHSTY")]
    public partial class CompanyTransHeaderStatusHistoryEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new CompanyTransHeaderStatusHistoryEntity object.
        /// </summary>
        public CompanyTransHeaderStatusHistoryEntity()
        {
            OnConstruct(); // call partial constructor if present
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyTransheaderFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_TRANSHEADER_FK", TypeName = "int", Order = 1)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CompanyTransheaderFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasCompanytranshdrstatOldFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANYTRANSHDRSTAT_OLD_FK", TypeName = "int", Order = 2)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int BasCompanytranshdrstatOldFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasCompanytranshdrstatNewFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANYTRANSHDRSTAT_NEW_FK", TypeName = "int", Order = 3)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int BasCompanytranshdrstatNewFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string Remark {
            get; set;
        }


        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            CompanyTransHeaderStatusHistoryEntity obj = new CompanyTransHeaderStatusHistoryEntity();
            obj.Id = Id;
            obj.CompanyTransheaderFk = CompanyTransheaderFk;
            obj.BasCompanytranshdrstatOldFk = BasCompanytranshdrstatOldFk;
            obj.BasCompanytranshdrstatNewFk = BasCompanytranshdrstatNewFk;
            obj.Remark = Remark;
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(CompanyTransHeaderStatusHistoryEntity clonedEntity);

    }


}
