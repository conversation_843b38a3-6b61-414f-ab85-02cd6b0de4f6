using System;
using System.Collections.Generic;
using System.Web.Http;
using RIB.Visual.Awp.Main.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Platform.ServiceFacade.WebApi;

namespace RIB.Visual.Awp.Main.ServiceFacade.WebApi.Controllers
{
	/// <summary>
	/// 
	/// </summary>
	[RoutePrefix("awp/main/packagesstructureresource")]
	public class AwpMainPackageStructureResourceController : ApiControllerBase<AwpMainPackageStructureResourceLogic>
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="filter"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("list")]
		public IEnumerable<PackageStructureResourceDto> GetPackageStructureResource(FilterData filter)
		{
			return this.Logic.GetPackageStructureResources(filter).ToDtos(e => new PackageStructureResourceDto(e));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="filter"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("filterByCostCode")]
		public IEnumerable<Object> FilterByCostCode(FilterData filter)
		{
			return this.Logic.FilterByCostCode(filter);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="filter"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("filterByMaterial")]
		public IEnumerable<Object> FilterByMaterial(FilterData filter)
		{
			return this.Logic.FilterByMaterial(filter);
		}
	}
}
