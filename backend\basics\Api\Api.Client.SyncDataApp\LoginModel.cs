﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Configuration;

namespace RIB.Visual.Basics.Api.Client.SyncDataApp
{
	/// <summary>
	/// 
	/// </summary>
	public class IdentityModel
	{
		/// <summary>
		/// User name
		/// </summary>
		public string UserName { get; set; }

		/// <summary>
		/// Password
		/// </summary>
		public string Password { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool CheckCertificate { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string ApiBaseUrl { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IdentityModel()
		{
			this.UserName = ConfigurationManager.AppSettings["username"];
			this.Password = ConfigurationManager.AppSettings["password"];
			var checkCertificate = false;
			bool.TryParse(ConfigurationManager.AppSettings["checkcertificate"], out checkCertificate);
			this.CheckCertificate = checkCertificate;
			this.ApiBaseUrl = ConfigurationManager.AppSettings["apibaseurl"];
		}
	}
}
