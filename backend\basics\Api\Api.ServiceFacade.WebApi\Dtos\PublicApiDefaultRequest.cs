﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// Represents a default request with empty implementation.
	/// </summary>
	public class PublicApiDefaultRequest : PublicApiRequestBase
	{
		/// <summary>
		/// Constructor.
		/// </summary>
		public PublicApiDefaultRequest()
		{

		}
	}
}
