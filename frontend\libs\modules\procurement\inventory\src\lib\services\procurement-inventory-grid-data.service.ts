/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable, inject } from '@angular/core';
import { DataServiceFlatLeaf, IDataServiceOptions, ServiceRole, IDataServiceChildRoleOptions, IDataServiceEndPointOptions } from '@libs/platform/data-access';
import { IPrcInventoryEntity } from '../model/entities/prc-inventory-entity.interface';
import { IPrcInventoryHeaderEntity } from '../model/entities/prc-inventory-header-entity.interface';
import { ProcurementInventoryHeaderGridComplete } from '../model/procurement-inventory-header-grid-complete.class';
import { ProcurementInventoryHeaderDataService } from './procurement-inventory-header-data.service';
import { MainDataDto } from '@libs/basics/shared';
import { ProcurementInventoryReadonlyProcessor } from './processors/procurement-inventory-readonly-processor.class';
import { UiCommonMessageBoxService } from '@libs/ui/common';

/**
 * Procurement Inventory Grid Data Service
 */
@Injectable({
	providedIn: 'root',
})
export class ProcurementInventoryGridDataService extends DataServiceFlatLeaf<IPrcInventoryEntity, IPrcInventoryHeaderEntity, ProcurementInventoryHeaderGridComplete> {
	private readonly readonlyProcessor: ProcurementInventoryReadonlyProcessor;
	private readonly messageBoxService = inject(UiCommonMessageBoxService);

	public constructor(procurementInventoryHeaderDataService: ProcurementInventoryHeaderDataService) {
		const options: IDataServiceOptions<IPrcInventoryEntity> = {
			apiUrl: 'procurement/inventory',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: false,
			},
			createInfo: <IDataServiceEndPointOptions>{
				endPoint: 'create',
				usePost: true,
				prepareParam: () => {
					const selectedHeader = procurementInventoryHeaderDataService.getSelectedEntity();
					if (!selectedHeader) {
						throw new Error('No inventory header selected for creating inventory item');
					}
					return {
						MainItemId: selectedHeader.Id,
						PrjStockFk: selectedHeader.PrjStockFk,
						ProjectFk: selectedHeader.PrjProjectFk
					};
				}
			},
			deleteInfo: <IDataServiceEndPointOptions>{
				endPoint: 'delete',
			},
			roleInfo: <IDataServiceChildRoleOptions<IPrcInventoryEntity, IPrcInventoryHeaderEntity, ProcurementInventoryHeaderGridComplete>>{
				role: ServiceRole.Leaf,
				itemName: 'Inventory',
				parent: procurementInventoryHeaderDataService,
			},
		};
		super(options);

		// Initialize and register the readonly processor
		this.readonlyProcessor = new ProcurementInventoryReadonlyProcessor(this);

		// Add readonly processor to entity processor
		this.processor.addProcessor([this.readonlyProcessor]);
	}

	protected override provideLoadPayload(): object {
		const selection = this.getSelectedParent();
		if (selection) {
			return { mainItemId: selection.Id };
		}
		return { mainItemId: 0 };
	}
	
	protected override onLoadSucceeded(loaded: object): IPrcInventoryEntity[] {
		const dataDto = new MainDataDto<IPrcInventoryEntity>(loaded);
		return dataDto.main;
	}

	public override registerByMethod(): boolean {
		return true;
	}

	public override registerModificationsToParentUpdate(
		parentUpdate: ProcurementInventoryHeaderGridComplete,
		modified: IPrcInventoryEntity[],
		deleted: IPrcInventoryEntity[]
	): void {
		if (modified && modified.some(() => true)) {
			parentUpdate.InventoryToSave = modified;
		}
		if (deleted && deleted.some(() => true)) {
			parentUpdate.InventoryToDelete = deleted;
		}
	}

	public override getSavedEntitiesFromUpdate(parentUpdate: ProcurementInventoryHeaderGridComplete): IPrcInventoryEntity[] {
		return parentUpdate.InventoryToSave || [];
	}

  // Call this when Quantity1 or Quantity2 changes
  public updateDifferenceClerkQuantity(entity: IPrcInventoryEntity): void {
    entity.DifferenceClerkQuantity = (entity.Quantity1 ?? 0) - (entity.Quantity2 ?? 0);
  }

  // Call this when ActualQuantity changes
  public updateRecordedQuantity(entity: IPrcInventoryEntity): void {
    entity.RecordedQuantity = entity.ActualQuantity ?? null;
  }

  /**
   * Call this when ActualQuantity is set on a wizard record to show a warning.
   */
  public showUpdateActualQuantityWarning(entity: IPrcInventoryEntity): void {
    if (entity.isFromWizard) {
      this.messageBoxService.showMsgBox(
        'procurement.inventory.updateactualwarnning',
        'Warning',
        'ico-warning'
      );
    }
  }
}








