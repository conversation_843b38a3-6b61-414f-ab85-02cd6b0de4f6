﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Check Id or Code Existed.
	/// </summary>
	[AttributeUsage(AttributeTargets.Property)]
	public class CheckIdOrCodeAttribute : ValidationAttribute
	{
		/// <summary>
		/// Contructor.
		/// </summary>
		public CheckIdOrCodeAttribute()
		{
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="value"></param>
		/// <param name="validationContext"></param>
		/// <returns></returns>
		protected override ValidationResult IsValid(object value, ValidationContext validationContext)
		{
			string errorMsg = null;
			bool hasErrors = false;
			ValidationResult result = ValidationResult.Success;
			IQueryable collection = null;
			if (validationContext.ObjectInstance != null && value != null)
			{
				#region	get Data
				var properties = value.GetType().GetProperties(System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.SetProperty | System.Reflection.BindingFlags.GetProperty);
				if (value != null && value is IEnumerable)
				{
					collection = (value as IEnumerable).AsQueryable();
					properties = collection.ElementType.GetProperties(System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.SetProperty | System.Reflection.BindingFlags.GetProperty);
				}
				else if (value is object && !(value is IEnumerable))
				{
					var valueList = new List<object>();
					valueList.Add(value);
					collection = (valueList as IEnumerable).AsQueryable();
				}

				var keyProperties = new List<PropertyInfo>();
				var groupProperties = new Dictionary<string, IList<PropertyInfo>>();
				if (properties != null)
				{
					foreach (var property in properties)
					{
						var compositeKey = property.GetCustomAttribute<AnyOfIdOrCodeAttribute>();
						if (compositeKey != null)
						{
							keyProperties.Add(property);
							if (!string.IsNullOrEmpty(compositeKey.Group))
							{
								if (!groupProperties.ContainsKey(compositeKey.Group))
								{
									groupProperties[compositeKey.Group] = new List<PropertyInfo>();
								}
								groupProperties[compositeKey.Group].Add(property);
							}
						}
					}
				}
				#endregion

				#region check existed
				if (groupProperties.Any())
				{
					int index = 0;
					foreach (var item in collection)
					{
						foreach (KeyValuePair<string, IList<PropertyInfo>> kv in groupProperties)
						{
							var props = kv.Value;
							bool isValid = false;
							foreach (var prop in props)
							{
								object obj = prop.GetValue(item);
								if (obj != null && !string.IsNullOrEmpty(obj.ToString()))
								{
									isValid = true;
									break;
								}
							}
							if (!isValid)
							{
								hasErrors = true;
								var propsDescription = string.Join(" or ", props.Select(e => e.Name));
								errorMsg += string.Format("{0}[{1}].{2}" + " is Required.", validationContext.MemberName, index, propsDescription) + " \n ";
							}
						}
						index++;
					}
				}
				else if (keyProperties.Any())
				{
					int index = 0;
					foreach (var item in collection)
					{
						foreach (var key in keyProperties)
						{
							string name = key.Name;
							if (name.EndsWith("Id"))
							{
								string desName = null;
								string deaTemp = null;
								var temp = key.GetValue(item);
								if (temp != null)
								{
									continue;
								}
								else
								{
									string subName = name.Substring(0, name.Length - 2);
									var description = keyProperties.FirstOrDefault(e => e.Name == subName || e.Name == subName + "Code" || e.Name == subName + "Description");
									if (description != null)
									{
										desName = description.Name;
										deaTemp = (string)description.GetValue(item);
										if (!string.IsNullOrEmpty(deaTemp))
										{
											continue;
										}
										else
										{
											hasErrors = true;
											errorMsg += string.Format("{0}[{1}].{2} OR {3}" + " is Required.", validationContext.MemberName, index, name, desName) + " \n ";
										}
									}
								}
							}
						}
						index++;
					}
				}
				#endregion
			}
			if (hasErrors)
			{
				result = new ValidationResult(errorMsg, new List<string>() { validationContext.MemberName });
			}

			return result;
		}
	}
}
