///*
// * $Id: TestCommon.cs 624051 2021-02-18 09:01:24Z kh $
// * Copyright (c) RIB Software AG
// */

//using System;

//namespace RIB.Visual.Basics.Api.UnitTests
//{
//	/// <summary>
//	/// Test common functionality
//	/// </summary>
//	/// 
//	/// \since 20-01-2010 by rei 
//	internal class TestCommon
//	{
//		/// <summary>
//		/// Initializes application server as self hosted
//		/// </summary>
//		public static void InitializeSelfHosting()
//		{
//			Environment.SetEnvironmentVariable("RIBVISUAL_SERVICESSELFHOSTING", "1");
//			RIB.Visual.Platform.UnitTests.Common.TestEnvironment.InitTestEnvironment();
//		}


//	}
//}