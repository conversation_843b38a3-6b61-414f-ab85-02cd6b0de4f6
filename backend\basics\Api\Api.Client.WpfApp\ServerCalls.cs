using System;
using System.Net;
using System.Net.Http;
using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System.Security.Cryptography.X509Certificates;
using System.Net.Security;
using IdentityModel.Client;

namespace RIB.Visual.Basics.Api.Client.WpfApp
{
	/// <summary>
	/// 
	/// </summary>
	public static class ServerCalls
	{   
		/// <summary>
		/// This method call the iTWOCloud WEB-Api. 
		/// 
		/// Header will be enhanced by token and context
		/// 
		/// </summary>
		/// <param name="token">identity server token</param>
		/// <param name="serviceUrl">root url to web-services</param>
		/// <param name="operationRelUrl"></param>
		/// <param name="companyCode">the company code</param>
		/// <param name="requestId">unique requestid , max 32 charaters</param>
		/// <param name="autoDeleteInquiry">true, delete inquiry reocrd in database to this request id after the call</param>
		/// <param name="result">result in json format</param>
		/// <returns></returns>
		public static bool CallService(string token, string serviceUrl, string operationRelUrl, string companyCode, string requestId, bool autoDeleteInquiry, out string result)
		{
			using (var client = new HttpClient())
			{
				client.SetBearerToken(token);		// set token from identity server into web-api header
				client.DefaultRequestHeaders.Add("Client-Context", "{}"); // we set client context to empty, will be place into web-call client header


				var content = String.Format("{{\"requestId\":\"{0}\",\"companyCode\":\"{1}\", \"autoDeleteInquiry\": \"{2}\"}}",
					requestId, companyCode, autoDeleteInquiry.ToString().ToLower());

				var myContent = new StringContent(content, Encoding.UTF8, "application/json");		// defined communication format and add parameter content into it

				//activate it for local machine debugging to ignore ssl certificate  validation
				//ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(RemoteCertificateValidation);

				try
				{
					// post call to iTWO Cloud backend server
					var response = client.PostAsync(serviceUrl + operationRelUrl, myContent).Result;

					if (response.StatusCode == HttpStatusCode.OK || response.StatusCode == HttpStatusCode.ResetContent)
					{
						result = response.Content.ReadAsStringAsync().Result;
						//Console.WriteLine(JArray.Parse(result));
						return true;
					}
					if (response.StatusCode == HttpStatusCode.BadRequest)
					{
						result = response.Content.ReadAsStringAsync().Result;
						//Console.WriteLine(JArray.Parse(result));
						string jResult;
						try
						{
							var jObject = JObject.Parse(result);
							jResult = JsonConvert.SerializeObject(jObject, Formatting.Indented);
						}
						catch (Exception)
						{
							jResult = result;
						}

						result = response + "\n" + jResult;
						return false;
					}
					result = response.ToString();
					return false;
				}
				catch (AggregateException ex)
				{
					result = ex.Message + "\r\n" + ex.StackTrace;
					return false;
				}
				catch (Exception ex)
				{
					result = ex.Message + "\r\n" + ex.StackTrace;
					return false;
				}
			}
		}

		static bool RemoteCertificateValidation(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
		{
			return true;
		}

		/// <summary>
		/// This method call the iTWOCloud WEB-Api. 
		/// 
		/// Header will be enhanced by token and context
		/// 
		/// </summary>
		public static string DoLogin(Model theModel)
		{
			string result = null;

			using (var client = new HttpClient())
			{
				var logonUri = string.Format("{0}{1}?username={2}&password={3}", theModel.ServicesUrl, "/basics/api/apilogon", theModel.Username, theModel.GetUnsecurePassword());
				try
				{
					// post call to iTWO Cloud backend server
					var response = client.GetAsync(logonUri).Result;
					if (response.StatusCode == HttpStatusCode.OK)
					{
						result = response.Content.ReadAsStringAsync().Result;
					}
					else
					{
						try
						{
							result = response.Content.ReadAsStringAsync().Result;
							result = JsonConvert.DeserializeObject<string>(result);
						}catch (Exception)
						{
							;// 
						}
					}
				}
				catch (AccessViolationException ex)
				{
					result = ex.Message + "\r\nLogin failed wrong username/password! Error: " + ex.StackTrace;
					return result;
				}
				catch (Exception ex)
				{
					result = ex.Message + "\r\nLogin failed! other failure, Error: " + ex.StackTrace;
					return result;
				}
			}
			return result;
		}

	}
}