﻿/*
 * $Id: ApiDocVariables.cs 583836 2020-04-21 10:35:10Z haagf $
 * Copyright (c) RIB Software SE
 */

using System;

namespace RIB.Visual.Basics.Api.Core
{
	/// <summary>
	/// Provides name constants for commonly used API doc variables.
	/// </summary>
	public static class ApiDocVariables
	{
		/// <summary>
		/// The human-readable name of an API entity.
		/// </summary>
		public const String EntityName = "EntityName";

		#region documentation extensions

		/// <summary>
		/// Custom documentation for the OData filter endpoint.
		/// </summary>
		public const String FilterDoc = "Doc_Filter";

		/// <summary>
		/// Custom documentation for the item load endpoint.
		/// </summary>
		public const String LoadDoc = "Doc_Load";

		/// <summary>
		/// Custom documentation for the OData item count endpoint.
		/// </summary>
		public const String CountDoc = "Doc_Count";

		/// <summary>
		/// Custom documentation for the item creation endpoint.
		/// </summary>
		public const String CreateDoc = "Doc_Create";

		/// <summary>
		/// Custom documentation for the item update endpoint.
		/// </summary>
		public const String UpdateDoc = "Doc_Update";

		/// <summary>
		/// Custom documentation for the item patch endpoint.
		/// </summary>
		public const String PatchDoc = "Doc_Patch";

		/// <summary>
		/// Custom documentation for the item deletion endpoint.
		/// </summary>
		public const String DeleteDoc = "Doc_Delete";

		#endregion
	}
}
