﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RIB.Visual.Awp.Main.BusinessComponents</RootNamespace>
    <AssemblyName>RIB.Visual.Awp.Main.BusinessComponents</AssemblyName>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <TargetFramework>net8.0</TargetFramework>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\RIB.Visual.Awp.Main.BusinessComponents.xml</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Entities\AwpMainCompleteEntity.cs" />
    <Compile Include="Entities\AwpPackageBoqItemCompleteEntity.cs" />
    <Compile Include="Entities\AwpPackageBoqItemEntity.cs" />
    <Compile Include="Entities\FilterDataConversionEntity.cs" />
    <Compile Include="Entities\DdTempIdsEntity.cs" />
    <Compile Include="Entities\DdTempIdsEntity.Generated.cs">
      <DependentUpon>DdTempIdsEntity.cs</DependentUpon>
    </Compile>
    <Compile Include="Entities\FilterData.cs" />
    <Compile Include="Entities\PackageStructureResourceEntity.cs" />
    <Compile Include="Entities\PackageStructureResourceFilterEntity.cs" />
    <Compile Include="EntityModel\EntityModel.ModelBuilder.cs" />
    <Compile Include="Logic\Grouping\GroupingStructureNode.cs" />
    <Compile Include="Logic\AwpMainPackageItemAssignmentLogic.cs" />
    <Compile Include="Logic\AwpMainPackageStructureResourceLogic.cs" />
    <Compile Include="Logic\Grouping\PackageStructureLineItemGroupingLogic.cs" />
    <Compile Include="Logic\Grouping\PackageStructureLineItemGroupingRequest.cs" />
    <Compile Include="Logic\Grouping\PackageStructureLineItemGroupingType.cs" />
    <Compile Include="Logic\Grouping\PackageStructureLineItemLogic.cs" />
    <Compile Include="Logic\Grouping\PackageStructureLineItemNode.cs" />
    <Compile Include="Logic\Helper\AwpMainPackageItemAssignmentHelper.cs" />
    <Compile Include="Logic\Helper\PackageStructureResourceFilterType.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Basics.Common.Localization">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Localization.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.CostCodes.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.CostCodes.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Customize.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Customize.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Material.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Material.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.MaterialCatalog.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.MaterialCatalog.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.ProcurementStructure.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.ProcurementStructure.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Boq.Main.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Boq.Main.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Core, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessComponents, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.AppServer.Runtime, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.OperationalManagement, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.OperationalManagement.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessEnvironment, Version=1.0.0.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessEnvironment.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Cloud.Common.BusinessComponents, Version=5.0.13.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common, Version=1.0.0.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.BusinessComponents, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Common, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Core, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.BusinessComponents, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Common, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.SqlServer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Server.Common">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Server.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.CodeDom">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.CodeDom.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Configuration.ConfigurationManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.SqlClient">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Data.SqlClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Main.Common\RIB.Visual.Awp.Main.Common.csproj">
      <Project>{7DAC796E-F383-4330-97A9-840454663121}</Project>
      <Name>RIB.Visual.Awp.Main.Common</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\Main.Core\RIB.Visual.Awp.Main.Core.csproj">
      <Project>{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}</Project>
      <Name>RIB.Visual.Awp.Main.Core</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\Main.Localization\RIB.Visual.Awp.Main.Localization.csproj">
      <Project>{86A7C400-52BC-4558-8467-F44D7D5168E5}</Project>
      <Name>RIB.Visual.Awp.Main.Localization</Name>
      <Private>False</Private>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="EntityModel\DataTransferObject.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\DbContext.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <DevartEntityDeploy Include="EntityModel\EntityModel.edml">
      <Generator>DevartEfGenerator</Generator>
      <LastGenOutput>EntityModel.info</LastGenOutput>
      <SubType>Designer</SubType>
    </DevartEntityDeploy>
    <None Include="EntityModel\EntityModel.edps">
      <DependentUpon>EntityModel.edml</DependentUpon>
      <SubType>Designer</SubType>
    </None>
    <None Include="EntityModel\EntityModel.info">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\EntityModel.MainDiagram.view">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\Validation.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\GroupingAttributes.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\LookupFilterKeysAttributes.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="RIBvisual.snk">
    </None>
  </ItemGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F" />
  </Target>
</Project>