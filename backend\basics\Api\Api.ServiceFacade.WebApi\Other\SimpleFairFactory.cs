using System;
using System.IO;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using IdentityModel.Client;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.Common;
using RIB.Visual.Services.Platform.BusinessComponents;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RIB.Visual.Platform.Server.Common;
using System.Net.Http;
using System.Net;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// SimpleFair Factory handles all service and methods used for doing single sign on and 
	/// authenticate a SimpleFair user for itwo4.0
	/// 
	/// Services avialable:
	///   read parameters from web.config file and save it locally into properties
	///   
	///   SimpleFair Ticket validation service 
	///   SimpleFair Logout service 
	///   
	/// </summary>
	internal class SimpleFairFactory : TokenFactoryBase
	{
		/// <summary>
		/// reads the configuration parameters from the web.config file of WEB-Server
		/// 
		/// <example>
		/// <code>
		///		<appSettings>
		///			<add key="simplefair:sso.providerurl" value="https://rib-w0918.rib-software.com/myhome/ssoserver/sso/"/>
		///			<add key="simplefair:sso.serviceurl" value="https://rib-w0918.rib-software.com/myhome/ssoserver/sso/"/>
		///		</appSettings>
		/// </code>
		/// </example>
		/// </summary>
		protected override void ReadConfig()
		{
			_ssoServiceUrl = AppSettingsReader.ReadString("simplefair:sso.providerurl");
			_ssoServiceServiceUrl = AppSettingsReader.ReadString("simplefair:sso.serviceurl");
			_idpId = AppSettingsReader.ReadInt("idm:identityproviderid");
		}

		/// <summary>
		/// This method validate the ticket received from caller against the myHome Single Sign On (SSO) service.
		/// If ticket is valid, it returns the user name.
		/// </summary>
		/// <param name="ticket"></param>
		/// <returns></returns>
		protected override ValidateResult ValidateTicket(string ticket)
		{
			ValidateResult validateResult = new ValidateResult() { StatusCode = HttpStatusCode.Unauthorized };

			var UserTicketItems = ticket.Split(':');
			if (UserTicketItems.Length == 2) //user:ticket pair e.g. <EMAIL>:DDD123434
			{
				validateResult.LogonName = UserTicketItems[0];

				// build sso servie url
				var validateUrl = string.Format("{0}?appName=itwo&token={1}&userName={2}",
							_ssoServiceUrl, UserTicketItems[1], validateResult.LogonName);

				ServicePointManager.ServerCertificateValidationCallback = MyRemoteCertificateValidationCallback;
				HttpClient httpClient = new HttpClient();
				httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
				var response = httpClient.GetAsync(validateUrl).Result;
				if (null != response && response.StatusCode == HttpStatusCode.OK)
				{					
					string result = response.Content.ReadAsStringAsync().Result;

					if (!string.IsNullOrWhiteSpace(result))
					{
						var jsonResult = JsonConvert.DeserializeObject<JObject>(result);
						if (jsonResult["msg"].ToString() == "success")
						{
							validateResult.StatusCode = HttpStatusCode.OK;
						}
						else
						{
							validateResult.ErrorCode = jsonResult["errorMsg"].ToString();
						}
					}
				}
			}

			return validateResult;
		}

		private static bool MyRemoteCertificateValidationCallback(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
		{
			return true;
		}
	}
}
