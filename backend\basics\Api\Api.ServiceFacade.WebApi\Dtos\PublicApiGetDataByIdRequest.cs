﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public class GetByIdPublicApiRequest : PublicApiRequestBase
	{
		/// <summary>
		/// Specify id for the query.
		/// </summary>
		[Required]
		public int Id { get; set; }
	}
}
