using RIB.Visual.Basics.Common.BusinessComponents.Final;
using RIB.Visual.Basics.Company.BusinessComponents;
using RIB.Visual.Basics.Company.BusinessComponents.Logic;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Core;
using System;
using System.Collections.Generic;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.AccountingJournals.BusinessComponents
{
    /// <summary>
    /// 
    /// </summary>
    public class AccountingJournalsLogic : EntityUpdateLogic<CompanyTransheaderEntity, IdentificationData>
    {
        /// <summary>
        /// Initializes the logic instance.
        /// </summary>
        public AccountingJournalsLogic()
        {

        }

        /// <summary>
        /// Returns the RegionCatalog sorted by the Sorted filed.
        /// </summary>
        /// <returns>
        /// if filter is not null, this expression is used for filtering the data
        /// if filter is null, all items will be returned 
        /// </returns>
        public IEnumerable<CompanyTransheaderEntity> GetFilteredCompanyTransheader(Expression<Func<CompanyTransheaderEntity, bool>> filter = null)
        {
            var logic = new BasicsCompanyTransheaderLogic();
            var entities = logic.GetFilteredCompanyTransheader(filter);
			foreach (var entity in entities)
			{
				if (entity.CompanyTransheaderFk != null)
				{
					var companyTransHeader = logic.GetSearchList(e => e.Id == entity.CompanyTransheaderFk).FirstOrDefault();
					if (companyTransHeader != null)
					{
						entity.BasCompanyTransheader = companyTransHeader.PostingDate.ToString("dd/MM/yyyy") + (companyTransHeader.Description == null ? "" : " " + companyTransHeader.Description);
					}
				}
			}
            return entities;
        }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="filterIn"></param>
		/// <param name="filterOut"></param>
		/// <param name="execInfo"></param>
		/// <returns></returns>
		/// <exception cref="Platform.OperationalManagement.BusinessLayerException"></exception>
		public IEnumerable<CompanyTransheaderEntity> GetListBySearchFilter(FilterRequest<Int32> filterIn, ref FilterResponse<Int32> filterOut, FilterExecutionInfo<int> execInfo = null)
		{
			if (execInfo == null)
			{
				execInfo = new FilterExecutionInfo<int>(filterIn, filterOut);
			}
			var logic = new BasicsCompanyTransheaderLogic();
			execInfo.CreateHint("GetListBySearchFilter()");
			try
			{
				var loginCompanyFk = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
				using (var dbContext = new DbContext(logic.GetDbModel()))
				{
					IQueryable<CompanyTransheaderEntity> query = dbContext.Entities<CompanyTransheaderEntity>();
					query = query.Where(e => e.CompanyFk == loginCompanyFk);
					if (!string.IsNullOrWhiteSpace(filterIn.Pattern))
					{
						query = FilterRequest<Int32>.ExpandToSearchPattern(filterIn, query,
							filterVal => "description.Contains(\"" + filterVal + "\") or commentText.Contains(\"" + filterVal + "\")");
					}
					if (filterIn.PKeys != null)
					{
						query = query.Where(e => filterIn.PKeys.Contains(e.Id));
					}
					//var entities = query.ToList();
					IList<CompanyTransheaderEntity> entities = null;
					entities = filterIn.OrderBy == null ?
							  FilterRequest<Int32>.RetrieveEntities(filterIn, filterOut, query, e => e.InsertedAt, e => e.Id)
							  : FilterRequest<Int32>.RetrieveEntities(filterIn, filterOut, query);
					foreach (var entity in entities)
					{
						if (entity.CompanyTransheaderFk != null)
						{
							var companyTransHeader = logic.GetSearchList(e => e.Id == entity.CompanyTransheaderFk).FirstOrDefault();
							if (companyTransHeader != null)
							{
								entity.BasCompanyTransheader = companyTransHeader.PostingDate.ToString("dd/MM/yyyy") + (companyTransHeader.Description == null ? "" : " " + companyTransHeader.Description);
							}
						}
					}
					return entities;
				}
			}
			catch (Exception ex)
			{
				throw new Platform.OperationalManagement.BusinessLayerException(ex.Message, ex) { ErrorCode = (Int32)Platform.OperationalManagement.ExceptionErrorCodes.BusinessFatalError };
			}
		}
        /// <summary>
        /// Creates a new CompanyTransheaderEntity based upon settings from a given instance.
        /// </summary>
        /// <param name="periodId">period Id</param>
        /// <param name="companyId">company Id</param>
        /// <returns></returns>
        public CompanyTransheaderEntity CreateCompanyTransheader(int periodId, int companyId)
        {
            var logic = new BasicsCompanyTransheaderLogic();
            var newEntity = logic.Create(periodId, companyId);
            return newEntity;
        }

        /// <summary>
        /// Creates a new CompanyPeriodEntity based upon settings from a given instance.
        /// </summary>
        /// <param name="yearId"></param>
        /// <param name="traidingYear"></param>
        /// <param name="startDateClient"></param>
        /// <param name="year"></param>
        /// <returns></returns>
        public CompanyPeriodEntity CreateCompanyPeriod(int yearId, int traidingYear, DateTime? startDateClient, CompanyYearEntity year)
        {
            var logic = new BasicsCompanyPeriodLogic();
            var newEntity = logic.Create(yearId, traidingYear, startDateClient, year);
            return newEntity;
        }

        /// <summary>
        /// Delete Entities
        /// </summary>
        /// <param name="Entities">Entities</param>
        public void DeleteEntities(IEnumerable<CompanyTransheaderEntity> Entities)
        {
            var logic = new BasicsCompanyTransheaderLogic();
            logic.DeleteTransheader(Entities);
        }

        /// <summary>
        /// Saves a lost of Accounting Journals without dependend data
        /// </summary>
        /// <param name="accountingJournalCompletes">The element to be saved</param>
        /// <returns></returns>
        public AccountingJournalsCompleteEntity SaveAccountingJournals(AccountingJournalsCompleteEntity accountingJournalCompletes)
        {

            var transactionLogic = new BasicsCompanyTransactionLogic();
            var transheaderLogic = new BasicsCompanyTransheaderLogic();

            if (accountingJournalCompletes.CompanyTransheader != null)
            {
                transheaderLogic.Save(accountingJournalCompletes.CompanyTransheader);
				var basCompanyTransHeaderlogic = new BasicsCompanyTransheaderLogic();
				if (accountingJournalCompletes.CompanyTransheader.CompanyTransheaderFk != null)
				{
					var companyTransHeader = basCompanyTransHeaderlogic.GetSearchList(e => e.Id == accountingJournalCompletes.CompanyTransheader.CompanyTransheaderFk).FirstOrDefault();
					if (companyTransHeader != null)
					{
						accountingJournalCompletes.CompanyTransheader.BasCompanyTransheader = companyTransHeader.PostingDate.ToString("dd/MM/yyyy") + (companyTransHeader.Description == null ? "" : " " + companyTransHeader.Description);
					}
				}
            }

            if (accountingJournalCompletes.TransactionToSave != null)
            {
                accountingJournalCompletes.TransactionToSave = transactionLogic.Save(accountingJournalCompletes.TransactionToSave);
            }

            if (accountingJournalCompletes.TransactionToDelete != null)
            {
                accountingJournalCompletes.TransactionToDelete = transactionLogic.DeleteTransaction(accountingJournalCompletes.TransactionToDelete);
            }

            return accountingJournalCompletes;
        }

		/// <summary>
		/// Delete Company Transheader by Id
		/// </summary>
		/// <param name="companyTransheaderId">Company Transheader Id</param>
		/// <returns></returns>
		public int Delete(int companyTransheaderId)
		{
			var logic = new BasicsCompanyTransheaderLogic();
			var entity = logic.GetItemByKey(companyTransheaderId);
			var companyTransheaderIds = new List<int>();
			companyTransheaderIds.Add(companyTransheaderId);
			logic.CheckCompanyTransheader(companyTransheaderIds);
			var IPrrHeader2TranHeaderLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<RIB.Visual.Basics.Core.Core.IPrrHeader2TranHeaderLogic>();
			IPrrHeader2TranHeaderLogic.DeletePrrHedaer2TranHeaderByTranHeader(companyTransheaderId);
			return logic.Delete(entity);
		}

		/// <summary>
		/// Get New Company Transheader Id
		/// </summary>
		/// <returns>New Id</returns>
		public int GetNewCompanyTransheaderId()
        {
            return this.SequenceManager.GetNext("BAS_COMPANY_TRANSHEADER");
        }

        /// <summary>
        /// Get DbModel
        /// </summary>
        /// <returns>DbModel</returns>
        public override DbCompiledModel GetDbModel()
        {
            var logic = new BasicsCompanyTransheaderLogic();
            return logic.GetDbModel();
        }
    }
}
