//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Basics.Api.BusinessComponents;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{

    /// <summary>
    /// Represents a Dto class.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("BAS_APIREQUESTITEM")]
    public partial class ApiRequestItemDto : RIB.Visual.Platform.Core.ITypedDto<ApiRequestItemEntity>
    {
        #region Constructors
       
        /// <summary>
        /// Initializes an instance of class ApiRequestItemDto.
        /// </summary>
        public ApiRequestItemDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class ApiRequestItemDto.
        /// </summary>
        /// <param name="entity">the instance of class ApiRequestItemEntity</param>
        public ApiRequestItemDto(ApiRequestItemEntity entity)
        {
            Id = entity.Id;
            RequestId = entity.RequestId;
            ModuleName = entity.ModuleName;
            RequestContext = entity.RequestContext;
            ItemData = entity.ItemData;
            Status = entity.Status;
            ValidUntil = entity.ValidUntil;
            InsertedBy = entity.InsertedBy;
            UpdatedBy = entity.UpdatedBy;
            InsertedAt = entity.InsertedAt;
            UpdatedAt = entity.UpdatedAt;
            Version = entity.Version;
            
            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion 

        #region Properties

        /// <summary>
        /// Gets or Sets Id.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }

        /// <summary>
        /// Gets or Sets RequestId.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQUESTID", TypeName = "varchar(32)", Order = 1)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string RequestId { get; set; }

        /// <summary>
        /// Gets or Sets ModuleName.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MODULENAME", TypeName = "varchar(252)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string ModuleName { get; set; }

        /// <summary>
        /// Gets or Sets RequestContext.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQUESTCONTEXT", TypeName = "text", Order = 11)]
        public string RequestContext { get; set; }

        /// <summary>
        /// Gets or Sets ItemData.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ITEMDATA", TypeName = "text", Order = 3)]
        public string ItemData { get; set; }

        /// <summary>
        /// Gets or Sets Status.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("STATUS", TypeName = "int", Order = 4)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Status { get; set; }

        /// <summary>
        /// Gets or Sets ValidUntil.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VALIDUNTIL", TypeName = "datetime", Order = 5)]
        public System.DateTime? ValidUntil { get; set; }

        /// <summary>
        /// Gets or Sets InsertedBy.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 6)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }

        /// <summary>
        /// Gets or Sets UpdatedBy.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 7)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// Gets or Sets InsertedAt.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 8)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public global::System.DateTime InsertedAt { get; set; }

        /// <summary>
        /// Gets or Sets UpdatedAt.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 9)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Gets or Sets Version.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 10)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(ApiRequestItemEntity); }
        }

        /// <summary>
        /// Copy the current ApiRequestItemDto instance to a new ApiRequestItemEntity instance.
        /// </summary>
        /// <returns>a new instance of class ApiRequestItemEntity</returns>
        public ApiRequestItemEntity Copy() 
        {
          var entity = new ApiRequestItemEntity();

          entity.Id = this.Id;
          entity.RequestId = this.RequestId;
          entity.ModuleName = this.ModuleName;
          entity.RequestContext = this.RequestContext;
          entity.ItemData = this.ItemData;
          entity.Status = this.Status;
          entity.ValidUntil = this.ValidUntil;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedBy = this.UpdatedBy;
          entity.InsertedAt = this.InsertedAt;
          entity.UpdatedAt = this.UpdatedAt;
          entity.Version = this.Version;

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(ApiRequestItemEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(ApiRequestItemEntity entity);
    }

}
