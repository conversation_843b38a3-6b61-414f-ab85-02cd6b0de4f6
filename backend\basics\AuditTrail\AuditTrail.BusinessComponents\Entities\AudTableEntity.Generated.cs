//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.AuditTrail.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Basics.AuditTrail.BusinessComponents.AudTableEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("AUD_TABLE")]
    public partial class AudTableEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new AudTableEntity object.
        /// </summary>
        public AudTableEntity()
        {
          this.Isenabletracking = false;
            this.AudColumnEntities = new HashSet<AudColumnEntity>();
            this.AudContainer2AudTableEntities = new HashSet<AudContainer2AudTableEntity>();
            OnConstruct(); // call partial constructor if present             
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Tablename in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TABLENAME", TypeName = "varchar(30)", Order = 1)]
        [System.ComponentModel.DataAnnotations.StringLength(30)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string Tablename
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Logtablename in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LOGTABLENAME", TypeName = "varchar(30)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(30)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string Logtablename
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for DescriptionTr in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION_TR", TypeName = "int", Order = 3)]
        public virtual int? DescriptionTr
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Description
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Isenabletracking in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISENABLETRACKING", TypeName = "bit", Order = 5)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool Isenabletracking
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Archivedays in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ARCHIVEDAYS", TypeName = "smallint", Order = 6)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual short Archivedays
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Purgedays in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PURGEDAYS", TypeName = "smallint", Order = 7)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual short Purgedays
        {
            get;
            set;
        }


        #endregion

        #region Navigation Properties
    
        /// <summary>
        /// There are no comments for AudColumnEntities in the schema.
        /// </summary>
        public virtual ICollection<AudColumnEntity> AudColumnEntities
        {
            get;
            set;
        }
    
        /// <summary>
        /// There are no comments for AudContainer2AudTableEntities in the schema.
        /// </summary>
        public virtual ICollection<AudContainer2AudTableEntity> AudContainer2AudTableEntities
        {
            get;
            set;
        }

        #endregion
    
        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            AudTableEntity obj = new AudTableEntity();
            obj.Id = Id;
            obj.Tablename = Tablename;
            obj.Logtablename = Logtablename;
            obj.DescriptionTr = DescriptionTr;
            obj.Description = Description;
            obj.Isenabletracking = Isenabletracking;
            obj.Archivedays = Archivedays;
            obj.Purgedays = Purgedays;
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(AudTableEntity clonedEntity);

    }


}
