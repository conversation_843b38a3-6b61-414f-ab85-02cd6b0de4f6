using System;
using System.Net;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample
{
	internal class Program
	{
		private static void Main(string[] args)
		{
			var runP = false;

			if (runP)
			{
				RunParallel();
			}
			else
			{
				var theUsername = "ribadmin";
				var theServiceData = new ServiceData()
				{
					BaseUrl = Constant.BaseUrl1,
					Username = theUsername
				};

				Console.WriteLine("Please Enter password for user: " + theUsername);
				var password = Console.ReadLine();

				theServiceData.SetToSecurePassword(password);

				var webApi = new ConsumeItwo40WebApi() { TheServiceData = theServiceData };

				webApi.LogonwithCompanyRoleSelectAwait();
				//webApi.DealwithBusinessPartnerManagement();

				webApi.DealwithUserManagement();
			}
			Console.WriteLine("Press Any Key to Finish");
			var result = Console.ReadLine();
			Console.WriteLine("done...");
		}

		private static int LogonAndCheckCompany(string info, string baseUrl, string username, string password)
		{
			var theSecureString = new NetworkCredential("", password).SecurePassword;
			//var theServiceData = new ServiceData() { BaseUrl = baseUrl, Username = username, Password = theSecureString };
			var theServiceData = new ServiceData() { BaseUrl = baseUrl, Username = username };
			theServiceData.SetToSecurePassword(password);

			var webApi = new ConsumeItwo40WebApi() { TheServiceData = theServiceData };
			webApi.DealwithCheckCompany(info, "901", 1);
			return 0;
		}

		private static void RunParallel()
		{
			int loopCount = 500;
			string user = "ribadmin";
			string pwd = "ribadmin";
			string url = Constant.BaseUrl1;
			//url = Constant.BaseUrl1;

			Action<int> action = (int s) => LogonAndCheckCompany(string.Format("Loop: {0}",s),url, user, pwd);
			var loopResult = Parallel.For(0, loopCount, action );
		}
	}
}
