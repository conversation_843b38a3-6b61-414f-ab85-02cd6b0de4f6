using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Core;

namespace RIB.Visual.Basics.AccountingJournals.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	[Export(typeof(IStatusConfigurationProvider))]
	public class StatusWorkflowConfigurationProvider : IStatusConfigurationProvider
	{
		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		IEnumerable<IStatusConfiguration> IStatusConfigurationProvider.GetConfiguration()
		{
			return new List<IStatusConfiguration>
				{
					new StatusConfiguration("companytransheaderstatus", "BAS_COMPANY_TRANSHEADER", "BAS_COMPANYTRANSHDRSTAT", true, false)
					{
						RoleTableName= "BAS_COMPANYTRNHDSTATROLE",
						RuleTableName= "BAS_COMPANYTRNHDSTATRULE",
						WorkFlowTableName= "BAS_COMPANYTRNHDSTATWF",
						HistoryTableName= "BAS_COMPANYTRNHDSTATHSTY",
						WorkFlowTableStatusRuleFkColumName = "BAS_COMPANYTRNHDSTATRULE_FK",
						RoleTableStatusRuleFkColumName = "BAS_COMPANYTRNHDSTATRULE_FK",
						HasIsLive = false
					}
				 };
		}
	}
}
