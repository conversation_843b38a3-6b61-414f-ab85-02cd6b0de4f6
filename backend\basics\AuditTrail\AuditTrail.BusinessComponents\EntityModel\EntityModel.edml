﻿<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="BusinessComponents.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2008" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:devart="http://devart.com/schemas/edml/StorageSchemaExtensions/1.0" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityContainer Name="DbContextStoreContainer">
          <EntitySet Name="BAS_DDTEMPIDS" EntityType="BusinessComponents.Store.BAS_DDTEMPIDS" store:Type="Tables" Table="BAS_DDTEMPIDS" />
          <EntitySet Name="AUD_COLUMNs" EntityType="BusinessComponents.Store.AUD_COLUMN" store:Type="Tables" Table="AUD_COLUMN" />
          <EntitySet Name="AUD_CONTAINERs" EntityType="BusinessComponents.Store.AUD_CONTAINER" store:Type="Tables" Table="AUD_CONTAINER" />
          <EntitySet Name="AUD_CONTAINER2AUD_TABLEs" EntityType="BusinessComponents.Store.AUD_CONTAINER2AUD_TABLE" store:Type="Tables" Table="AUD_CONTAINER2AUD_TABLE" />
          <EntitySet Name="AUD_TABLEs" EntityType="BusinessComponents.Store.AUD_TABLE" store:Type="Tables" Table="AUD_TABLE" />
          <AssociationSet Name="AUD_TABLE_FK00" Association="BusinessComponents.Store.AUD_TABLE_FK00">
            <End Role="AUD_TABLE" EntitySet="AUD_TABLEs" />
            <End Role="AUD_COLUMN" EntitySet="AUD_COLUMNs" />
          </AssociationSet>
          <AssociationSet Name="AUD_CONTAINER_FK00" Association="BusinessComponents.Store.AUD_CONTAINER_FK00">
            <End Role="AUD_CONTAINER" EntitySet="AUD_CONTAINERs" />
            <End Role="AUD_CONTAINER2AUD_TABLE" EntitySet="AUD_CONTAINER2AUD_TABLEs" />
          </AssociationSet>
          <AssociationSet Name="AUD_TABLE_FK01" Association="BusinessComponents.Store.AUD_TABLE_FK01">
            <End Role="AUD_TABLE" EntitySet="AUD_TABLEs" />
            <End Role="AUD_CONTAINER2AUD_TABLE" EntitySet="AUD_CONTAINER2AUD_TABLEs" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="BAS_DDTEMPIDS">
          <Key>
            <PropertyRef Name="REQUESTID" />
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="REQUESTID" Type="char" Nullable="false" MaxLength="32" />
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="KEY1" Type="int" />
          <Property Name="KEY2" Type="int" />
          <Property Name="KEY3" Type="int" />
        </EntityType>
        <EntityType Name="AUD_COLUMN">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="AUD_TABLE_FK" Type="int" Nullable="false" />
          <Property Name="COLUMNNAME" Type="varchar" Nullable="false" MaxLength="32" />
          <Property Name="ISENABLETRACKING" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISDELETED" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="AUD_CONTAINER">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="CONTAINER_UUID" Type="char" Nullable="false" MaxLength="32" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="DESCRIPTION_TR" Type="int" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="AUD_CONTAINER2AUD_TABLE">
          <Key>
            <PropertyRef Name="AUD_COINTAINER_FK" />
            <PropertyRef Name="AUD_TABLE_FK" />
          </Key>
          <Property Name="AUD_COINTAINER_FK" Type="int" Nullable="false" />
          <Property Name="AUD_TABLE_FK" Type="int" Nullable="false" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="AUD_TABLE">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="TABLENAME" Type="varchar" Nullable="false" MaxLength="30" />
          <Property Name="LOGTABLENAME" Type="varchar" Nullable="false" MaxLength="30" />
          <Property Name="DESCRIPTION_TR" Type="int" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="ISENABLETRACKING" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ARCHIVEDAYS" Type="smallint" Nullable="false" />
          <Property Name="PURGEDAYS" Type="smallint" Nullable="false" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <Association Name="AUD_TABLE_FK00">
          <End Role="AUD_TABLE" Type="BusinessComponents.Store.AUD_TABLE" Multiplicity="1" />
          <End Role="AUD_COLUMN" Type="BusinessComponents.Store.AUD_COLUMN" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="AUD_TABLE">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="AUD_COLUMN">
              <PropertyRef Name="AUD_TABLE_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="AUD_CONTAINER_FK00">
          <End Role="AUD_CONTAINER" Type="BusinessComponents.Store.AUD_CONTAINER" Multiplicity="1" />
          <End Role="AUD_CONTAINER2AUD_TABLE" Type="BusinessComponents.Store.AUD_CONTAINER2AUD_TABLE" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="AUD_CONTAINER">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="AUD_CONTAINER2AUD_TABLE">
              <PropertyRef Name="AUD_COINTAINER_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="AUD_TABLE_FK01">
          <End Role="AUD_TABLE" Type="BusinessComponents.Store.AUD_TABLE" Multiplicity="1" />
          <End Role="AUD_CONTAINER2AUD_TABLE" Type="BusinessComponents.Store.AUD_CONTAINER2AUD_TABLE" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="AUD_TABLE">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="AUD_CONTAINER2AUD_TABLE">
              <PropertyRef Name="AUD_TABLE_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Function Name="AUD_POPULATECOLUMNS_SP" IsComposable="false" BuiltIn="false" Aggregate="false" NiladicFunction="false" ParameterTypeSemantics="AllowImplicitConversion" StoreFunctionName="AUD_POPULATECOLUMNS_SP">
          <Parameter Name="UserId" Type="int" Mode="In" />
        </Function>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="RIB.Visual.Basics.AuditTrail.BusinessComponents" Alias="Self" d4p1:ViewGeneration="true" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:devart="http://devart.com/schemas/edml/ConceptualSchemaExtensions/1.0" xmlns:ed="http://devart.com/schemas/EntityDeveloper/1.0" annotation:UseStrongSpatialTypes="false" xmlns:d4p1="http://devart.com/schemas/edml/ConceptualSchemaExtensions/1.0" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="ModelBuilder" ed:Namespace="RIB.Visual.Basics.AuditTrail.BusinessComponents" annotation:LazyLoadingEnabled="false" ed:Guid="2c8b7c01-1421-4d2d-b86b-3d5c5b084643">
          <EntitySet Name="DdTempIdsEntities" EntityType="RIB.Visual.Basics.AuditTrail.BusinessComponents.DdTempIdsEntity" />
          <EntitySet Name="AudColumnEntities" EntityType="RIB.Visual.Basics.AuditTrail.BusinessComponents.AudColumnEntity" />
          <EntitySet Name="AudContainerEntities" EntityType="RIB.Visual.Basics.AuditTrail.BusinessComponents.AudContainerEntity" />
          <EntitySet Name="AudContainer2AudTableEntities" EntityType="RIB.Visual.Basics.AuditTrail.BusinessComponents.AudContainer2AudTableEntity" />
          <EntitySet Name="AudTableEntities" EntityType="RIB.Visual.Basics.AuditTrail.BusinessComponents.AudTableEntity" />
          <AssociationSet Name="AUD_TABLE_FK00Set" Association="RIB.Visual.Basics.AuditTrail.BusinessComponents.AUD_TABLE_FK00">
            <End Role="AudTableEntity" EntitySet="AudTableEntities" />
            <End Role="AudColumnEntities" EntitySet="AudColumnEntities" />
          </AssociationSet>
          <AssociationSet Name="AUD_CONTAINER_FK00Set" Association="RIB.Visual.Basics.AuditTrail.BusinessComponents.AUD_CONTAINER_FK00">
            <End Role="AudContainerEntity" EntitySet="AudContainerEntities" />
            <End Role="AudContainer2AudTableEntities" EntitySet="AudContainer2AudTableEntities" />
          </AssociationSet>
          <AssociationSet Name="AUD_TABLE_FK01Set" Association="RIB.Visual.Basics.AuditTrail.BusinessComponents.AUD_TABLE_FK01">
            <End Role="AudTableEntity" EntitySet="AudTableEntities" />
            <End Role="AudContainer2AudTableEntities" EntitySet="AudContainer2AudTableEntities" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="DdTempIdsEntity" ed:Guid="6902c783-fe1a-4395-9200-d7ba04c3ee9a" ed:GenerateDTO="False">
          <Key>
            <PropertyRef Name="RequestId" />
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="RequestId" Type="String" Nullable="false" MaxLength="32" FixedLength="true" ed:ValidateMaxLength="32" ed:ValidateRequired="True" ed:Guid="fadd9d49-dad6-4f9d-aaea-b56f8bdf6269" />
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="06c83e50-aa8d-4e66-b740-2bac6cd8ce19" />
          <Property Name="Key1" Type="Int32" ed:ValidateRequired="False" ed:Guid="a48d2dd3-ce74-4a63-88df-10e6f66ecd7e" />
          <Property Name="Key2" Type="Int32" ed:ValidateRequired="False" ed:Guid="427bf956-5fe5-4a93-922a-2b61a6f85b9b" />
          <Property Name="Key3" Type="Int32" ed:ValidateRequired="False" ed:Guid="8dd84372-cc1d-4790-b0cb-a439dc68f140" />
        </EntityType>
        <EntityType Name="AudColumnEntity" ed:Guid="a46408d5-6f01-4c34-a8af-fdb3a4d3d939">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="ddc2d9d7-9de5-4866-ba1c-10f0789cc43c" />
          <Property Name="AudTableFk" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="9a2d618e-62e6-4848-b49d-65758cc85289" />
          <Property Name="Columnname" Type="String" Nullable="false" MaxLength="32" ed:ValidateMaxLength="32" ed:ValidateRequired="True" ed:Guid="6ad95ff6-9131-4a63-89e4-34efb79e09b1" />
          <Property Name="Isenabletracking" Type="Boolean" Nullable="false" ed:ValidateRequired="True" ed:Guid="55f18771-8e6e-4420-ad81-0a8e12d38ed8" />
          <Property Name="Isdeleted" Type="Boolean" Nullable="false" ed:ValidateRequired="True" ed:Guid="b24dcd1a-f602-4971-8ca2-9de7d3a26092" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="True" ed:Guid="12e90913-4f9b-48e2-8f63-a34f1789b99b" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="8dd78c4f-b44e-44ca-bf1a-9c708601712f" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="False" ed:Guid="9e42a1ac-a19e-49a3-b87f-d8a344f97393" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="False" ed:Guid="0cc73e1c-cfff-4a7d-97c3-4bbfca947c8a" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="c769ed5e-5a8c-4725-a8ef-a8f3a9fcf027" />
          <NavigationProperty Name="AudTableEntity" Relationship="RIB.Visual.Basics.AuditTrail.BusinessComponents.AUD_TABLE_FK00" FromRole="AudColumnEntities" ToRole="AudTableEntity" ed:Guid="eac0237e-6749-48e2-b0d0-e8171de18015" />
        </EntityType>
        <EntityType Name="AudContainerEntity" ed:Guid="a303b554-89b7-4e65-a212-0979b43c6a2c">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="ae3f990d-1be1-41ef-ad00-650e1ad80d0f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ContainerUuid" Type="String" Nullable="false" MaxLength="32" FixedLength="true" ed:ValidateMaxLength="32" ed:ValidateRequired="True" ed:Guid="a88c0244-c1c4-4538-94d8-640ef58ca066">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">guid</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DescriptionInfo" Type="RIB.Visual.Basics.AuditTrail.BusinessComponents.DescriptionTranslateType" Nullable="false" ed:ValidateRequired="False" ed:Guid="deaa4313-0066-416c-ad81-31ff9ade21b7">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">translation</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="True" ed:Guid="52c563f8-5121-46da-818b-c38404d564a6" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="cd250cb2-d075-4055-bb34-cac4a0e6e157" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="False" ed:Guid="9cacd282-2e18-4985-aea2-731877e9c49b" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="False" ed:Guid="c304a66d-66f2-4f4a-be07-5e18e0fc079f" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="d478adbd-c08f-4344-81fd-7505faac4c23" />
          <NavigationProperty Name="AudContainer2AudTableEntities" Relationship="RIB.Visual.Basics.AuditTrail.BusinessComponents.AUD_CONTAINER_FK00" FromRole="AudContainerEntity" ToRole="AudContainer2AudTableEntities" ed:Guid="ab49fbdc-a4f9-42fc-82e0-207fa2467f48" />
        </EntityType>
        <EntityType Name="AudContainer2AudTableEntity" ed:Guid="4f24b7ac-82c2-4261-8bf2-e20abeb4c044">
          <Key>
            <PropertyRef Name="AudCointainerFk" />
            <PropertyRef Name="AudTableFk" />
          </Key>
          <Property Name="AudCointainerFk" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="89146be3-e117-4a84-9b30-c729048080bf" />
          <Property Name="AudTableFk" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="bffc8505-67ed-4840-a01c-8bb627b6c9a4" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="True" ed:Guid="cc562548-4a0e-47e5-b2b9-c52b76a6b1d7" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="3f7ae667-9917-4fcb-a97a-57429f3bd8e9" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="False" ed:Guid="e6af7043-b178-4f83-8e06-2babdf09a96b" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="False" ed:Guid="64ac636c-517e-4555-803c-76347a40bf33" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="ae87c074-22e8-4bbf-969f-7acf637c5620" />
          <NavigationProperty Name="AudContainerEntity" Relationship="RIB.Visual.Basics.AuditTrail.BusinessComponents.AUD_CONTAINER_FK00" FromRole="AudContainer2AudTableEntities" ToRole="AudContainerEntity" ed:Guid="e7e20b5c-a025-4371-b77c-593a715e7f1b" />
          <NavigationProperty Name="AudTableEntity" Relationship="RIB.Visual.Basics.AuditTrail.BusinessComponents.AUD_TABLE_FK01" FromRole="AudContainer2AudTableEntities" ToRole="AudTableEntity" ed:Guid="2c0edeac-f623-4c15-a4cf-86e7a9caa5e2" />
        </EntityType>
        <EntityType Name="AudTableEntity" ed:Guid="e600a82f-a1df-406f-8a00-1c7fed50e2a9">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="f7ee740c-64ff-493a-812f-041d7fa72829" />
          <Property Name="Tablename" Type="String" Nullable="false" MaxLength="30" ed:ValidateMaxLength="30" ed:ValidateRequired="True" ed:Guid="7b28f38e-d216-4d96-a664-0f0a88b04642" />
          <Property Name="Logtablename" Type="String" Nullable="false" MaxLength="30" ed:ValidateMaxLength="30" ed:ValidateRequired="True" ed:Guid="bcb3cfba-e4e1-4dce-9c09-812fa723a0bf" />
          <Property Name="DescriptionTr" Type="Int32" ed:ValidateRequired="False" ed:Guid="8cdf4117-c877-48a1-a060-11cf2a82471d" />
          <Property Name="Description" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="False" ed:Guid="15b238fc-3506-4be0-978c-11ee4c4550f5" />
          <Property Name="Isenabletracking" Type="Boolean" Nullable="false" ed:ValidateRequired="True" ed:Guid="b0371f39-69f3-488f-8c7e-869cf963becb" />
          <Property Name="Archivedays" Type="Int16" Nullable="false" ed:ValidateRequired="True" ed:Guid="e0054344-54a5-4d9c-a679-d5b23c8b2527" />
          <Property Name="Purgedays" Type="Int16" Nullable="false" ed:ValidateRequired="True" ed:Guid="ded1d317-a1b1-46de-aead-75fc4122f0cc" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="True" ed:Guid="fb75e82d-ade5-4e04-9ebe-92f15a152e7b" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="738ef874-399f-4bbf-a83a-40239a092653" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="False" ed:Guid="2d8be6b2-6179-4e9b-929e-61fde023f7be" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="False" ed:Guid="5f3e1183-6896-4cb4-9e86-61f424aaeec7" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="True" ed:Guid="984ac572-9586-405d-ae84-c70baef2d47a" />
          <NavigationProperty Name="AudColumnEntities" Relationship="RIB.Visual.Basics.AuditTrail.BusinessComponents.AUD_TABLE_FK00" FromRole="AudTableEntity" ToRole="AudColumnEntities" ed:Guid="5cfd0d73-b872-423f-9be3-a9f6d933acd6" />
          <NavigationProperty Name="AudContainer2AudTableEntities" Relationship="RIB.Visual.Basics.AuditTrail.BusinessComponents.AUD_TABLE_FK01" FromRole="AudTableEntity" ToRole="AudContainer2AudTableEntities" ed:Guid="c90d06b8-ee50-4895-ab7d-20df40e720f0" />
        </EntityType>
        <ComplexType Name="DescriptionTranslateType" ed:Guid="d503cfc6-f9ef-411c-af64-0e66c76468fc" ed:GenerateDTO="True" ed:GenerateOnlyMapping="True">
          <Property Name="Description" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="False" ed:Guid="69283371-dcfd-487a-8900-4a9e28ba8105" />
          <Property Name="DescriptionTr" Type="Int32" ed:ValidateRequired="False" ed:Guid="7019d3f9-9cd2-4cd7-bd4a-5e52a2d17a1c" />
        </ComplexType>
        <Association Name="AUD_TABLE_FK00" ed:Guid="6f41a971-4a56-4450-81d1-375b61be25c3">
          <End Role="AudTableEntity" Type="RIB.Visual.Basics.AuditTrail.BusinessComponents.AudTableEntity" Multiplicity="1" />
          <End Role="AudColumnEntities" Type="RIB.Visual.Basics.AuditTrail.BusinessComponents.AudColumnEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="AudTableEntity">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="AudColumnEntities">
              <PropertyRef Name="AudTableFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="AUD_CONTAINER_FK00" ed:Guid="d67dee76-80f2-4f8e-ae56-b31a6067089b">
          <End Role="AudContainerEntity" Type="RIB.Visual.Basics.AuditTrail.BusinessComponents.AudContainerEntity" Multiplicity="1" />
          <End Role="AudContainer2AudTableEntities" Type="RIB.Visual.Basics.AuditTrail.BusinessComponents.AudContainer2AudTableEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="AudContainerEntity">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="AudContainer2AudTableEntities">
              <PropertyRef Name="AudCointainerFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="AUD_TABLE_FK01" ed:Guid="ac5af28a-da3f-4666-ad6d-e35b1bfb589c">
          <End Role="AudTableEntity" Type="RIB.Visual.Basics.AuditTrail.BusinessComponents.AudTableEntity" Multiplicity="1" />
          <End Role="AudContainer2AudTableEntities" Type="RIB.Visual.Basics.AuditTrail.BusinessComponents.AudContainer2AudTableEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="AudTableEntity">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="AudContainer2AudTableEntities">
              <PropertyRef Name="AudTableFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema>
    </edmx:ConceptualModels>
    <!-- MSL content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="DbContextStoreContainer" CdmEntityContainer="ModelBuilder">
          <EntitySetMapping Name="DdTempIdsEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Basics.AuditTrail.BusinessComponents.DdTempIdsEntity">
              <MappingFragment StoreEntitySet="BAS_DDTEMPIDS">
                <ScalarProperty Name="RequestId" ColumnName="REQUESTID" />
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="Key1" ColumnName="KEY1" />
                <ScalarProperty Name="Key2" ColumnName="KEY2" />
                <ScalarProperty Name="Key3" ColumnName="KEY3" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="AudColumnEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Basics.AuditTrail.BusinessComponents.AudColumnEntity">
              <MappingFragment StoreEntitySet="AUD_COLUMNs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="AudTableFk" ColumnName="AUD_TABLE_FK" />
                <ScalarProperty Name="Columnname" ColumnName="COLUMNNAME" />
                <ScalarProperty Name="Isenabletracking" ColumnName="ISENABLETRACKING" />
                <ScalarProperty Name="Isdeleted" ColumnName="ISDELETED" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="AudContainerEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Basics.AuditTrail.BusinessComponents.AudContainerEntity">
              <MappingFragment StoreEntitySet="AUD_CONTAINERs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ContainerUuid" ColumnName="CONTAINER_UUID" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ComplexProperty Name="DescriptionInfo" TypeName="RIB.Visual.Basics.AuditTrail.BusinessComponents.DescriptionTranslateType">
                  <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                  <ScalarProperty Name="DescriptionTr" ColumnName="DESCRIPTION_TR" />
                </ComplexProperty>
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="AudContainer2AudTableEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Basics.AuditTrail.BusinessComponents.AudContainer2AudTableEntity">
              <MappingFragment StoreEntitySet="AUD_CONTAINER2AUD_TABLEs">
                <ScalarProperty Name="AudCointainerFk" ColumnName="AUD_COINTAINER_FK" />
                <ScalarProperty Name="AudTableFk" ColumnName="AUD_TABLE_FK" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="AudTableEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Basics.AuditTrail.BusinessComponents.AudTableEntity">
              <MappingFragment StoreEntitySet="AUD_TABLEs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="Tablename" ColumnName="TABLENAME" />
                <ScalarProperty Name="Logtablename" ColumnName="LOGTABLENAME" />
                <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                <ScalarProperty Name="Isenabletracking" ColumnName="ISENABLETRACKING" />
                <ScalarProperty Name="Archivedays" ColumnName="ARCHIVEDAYS" />
                <ScalarProperty Name="Purgedays" ColumnName="PURGEDAYS" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="DescriptionTr" ColumnName="DESCRIPTION_TR" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <edmx:Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </edmx:Connection>
    <edmx:Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="false" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
      </DesignerInfoPropertySet>
    </edmx:Options>
    <edmx:Diagrams>
      <Diagram Name="Main" />
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>