using System;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample;


/// <summary>
/// Read Business Partner sample 
/// </summary>
public static class BusinessPartner
{
	public static LogonServices LogonServices { get; set; } 

	/// <summary>
	/// 
	/// </summary>
	/// <returns></returns>
	public static async Task<Response> GetBusinessPartnerAsync(string companyCode, int id)
	{
		const string name = "GetBusinessPartnerAsync";
		Console.WriteLine("{0} - Start", name);
		var responseResult = await Task.Run<Response>(() => GetBusinessPartner(companyCode, id));
		Console.WriteLine("{0} - Done", name);
		return responseResult;
	}

	/// <summary>
	/// Read Business Partner via PublicApi as postasynch call
	/// </summary>
	/// <returns></returns>
	private static Response GetBusinessPartner(string companyCode, int id)
	{
		var fctResponse = new Response() { Result = false };

		using (var client = new HttpClient())
		{
			LogonServices.SetTokenClientContext(client);
			try
			{
				// send Get Call to Backend
				var url = LogonServices.ServicesUrl + "/businesspartner/publicapi/businesspartner/1.0/get";
				var data = string.Format("{{\"companyCode\": \"{0}\",\"id\": {1}}}", companyCode, id);
				var myContent =
					new StringContent(data, Encoding.UTF8,
						"application/json"); // defined communication format and add parameter content into it

				var response = client.PostAsync(url, myContent).Result;

				if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.ResetContent)
					return LogonServices.AnalyseHttpStatusCode(response);

				return LogonServices.ReadResponseValue(response, fctResponse);
			}
			catch (Exception ex)
			{
				fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
				return fctResponse;
			}
		}

	}

	/// <summary>
	/// </summary>
	/// <returns></returns>
	public static async Task<Response> CreateBusinessPartnerAsync(string companyCode, string BpName1, string BpName2)
	{
		const string name = "CreateBusinessPartnerAsync";
		Console.WriteLine("{0} - Start", name);
		var responseResult = await Task.Run<Response>(() => CreateBusinessPartner(companyCode, BpName1, BpName2));
		Console.WriteLine("{0} - Done", name);
		return responseResult;
	}

	private static Response CreateBusinessPartner(string companyCode, string BpName1, string BpName2)
	{
		var fctResponse = new Response() { Result = false };

		using (var client = new HttpClient())
		{
			LogonServices.SetTokenClientContext(client);
			try
			{
				// send Get Call to Backend
				var url = LogonServices.ServicesUrl + "/businesspartner/publicapi/businesspartner/1.0/create";
				string bpdObject = @"{	'LogOptions': 1, 'CompanyCode' : '"+companyCode+@"','UserDataLanguage' : 'en',"+
															@"'BusinessPartner' : {"+
															@"  'id': 0, 'BusinessPartnerName1': '"+BpName1+@"'"+
															@" ,'BusinessPartnerName2': '"+BpName2+@"'"+
															@" ,'CompanyCode' : '901'"+
														@"}}";
				//var data = 

				//var data = string.Format("{{\"companyCode\": \"{0}\",\"id\": {1}}}", companyCode, id);
				var myContent = new StringContent(bpdObject, Encoding.UTF8, "application/json");		// defined communication format and add parameter content into it

				var response = client.PostAsync(url, myContent).Result;

				if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.ResetContent)
					return LogonServices.AnalyseHttpStatusCode(response);

				return LogonServices.ReadResponseValue(response, fctResponse);
			}
			catch (Exception ex)
			{
				fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
				return fctResponse;
			}

		}

	}
}