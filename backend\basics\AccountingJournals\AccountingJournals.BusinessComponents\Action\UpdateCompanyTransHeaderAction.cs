using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Basics.Company.BusinessComponents;
using RIB.Visual.Basics.Company.BusinessComponents.Logic;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Workflow.Core;
using RIB.Visual.Platform.BusinessComponents;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Transactions;

namespace RIB.Visual.Sales.Billing.BusinessComponents
{
	/// <summary>
	///
	/// </summary>
	[Export(typeof(IAction))]
	public class UpdateCompanyTransHeaderAction : IAction
	{
		/// <summary>
		///
		/// </summary>
		protected readonly string[] Inputs = { "TransIds", "Description", "IsSuccess", "ReturnValue", "CommentText" };

		/// <summary>
		///
		/// </summary>
		protected readonly string[] Outputs = { "Result" };

		/// <summary>
		///
		/// </summary>
		int IAction.ActionType
		{
			get { return ActionType.External; }
		}

		/// <summary>
		///
		/// </summary>
		///

		string IAction.Id
		{
			get { return "FA784F38081847FE911CB077B269139B"; }
		}

		/// <summary>
		///
		/// </summary>
		string IAction.Description
		{
			get { return "Update Company.Trans Header"; }
		}

		/// <summary>
		///
		/// </summary>
		string[] IAction.Input
		{
			get { return Inputs; }
		}

		/// <summary>
		///
		/// </summary>
		bool IAction.IsLongRunning
		{
			get { return false; }
		}

		/// <summary>
		///
		/// </summary>
		string[] IAction.Output
		{
			get { return Outputs; }
		}

		/// <summary>
		///
		/// </summary>
		string IAction.Version
		{
			get { return "1.0@13.12.2019"; }
		}

		private string _logger;

		/// <summary>
		///
		/// </summary>
		public string Logger
		{
			get { return _logger; }
			set { _logger = value; }
		}

		private BasicsCompanyTransheaderLogic tranHeaderLogic;
		/// <summary>
		///
		/// </summary>
		/// <param name="parameters"></param>
		/// <returns></returns>
		IDictionary<string, object> IAction.Execute(IDictionary<string, object> parameters)
		{
			IDictionary<string, object> results = new ConcurrentDictionary<string, object>();
			tranHeaderLogic = new BasicsCompanyTransheaderLogic();
			var description = parameters["Description"].ToString();
			var transIds = parameters["TransIds"].ToString();
			var isSuccess = parameters["IsSuccess"].ToString();
			var returnValue = parameters["ReturnValue"].ToString();
			var commentText = parameters["CommentText"].ToString();

			int[] _transIds; bool _isSuccess;
			_logger = string.Empty;
			if (this.Validate(transIds.ToString(), out _transIds, isSuccess, out _isSuccess, description))
			{
				try
				{
					using (var transaction = TransactionScopeFactory.CreateRequiresNew())
					{
						var transHeaderEntities = this.GetTransByIdOrDes(_transIds, transIds.ToString(), description);
						if (transHeaderEntities != null && transHeaderEntities.Any())
						{
							foreach (var transHeaderEntity in transHeaderEntities)
							{
								if (isSuccess != null && isSuccess.IsNonEmpty())
								{
									transHeaderEntity.IsSuccess = _isSuccess;
								}
								if (returnValue != null)
								{
									transHeaderEntity.ReturnValue = returnValue.Length > 2000 ? returnValue.Substring(0, 2000) : returnValue;
								}
								if (commentText != null)
								{
									transHeaderEntity.CommentText = commentText.Length > 255 ? commentText.Substring(0, 255) : commentText;
								}
							}
							tranHeaderLogic.Save(transHeaderEntities);
							_logger = "Update Successed!";
						}
						transaction.Complete();
					}
				}
				catch (Exception ex)
				{
					while (ex.InnerException != null)
					{
						ex = ex.InnerException;
					}
					_logger = ex.Message.ToString();
				}
			}

			results["Result"] = _logger;
			return results;
		}

		private List<CompanyTransheaderEntity> GetTransByIdOrDes(int[] _transIds, string transIds, string description)
		{
			var transactions = new List<CompanyTransheaderEntity>();
			if (_transIds.Length > 0)
			{
				transactions = tranHeaderLogic.GetSearchList(e => _transIds.Contains(e.Id)).ToList();
			}
			if (transactions.Count == 0)
			{
				transactions = tranHeaderLogic.GetSearchList(e => e.Description == description).ToList();
			}
			if (transactions.Count == 0)
			{
				_logger = string.Format("Cannot find the transHeadre Ids :{0} and description:{1} in login company ! please have a check.", transIds, description);
				return null;
			}
			return transactions;
		}

		private bool Validate(string transIds, out int[] _transIds, string isSuccess, out bool _isSuccess, string description)
		{
			_transIds = new int[] { };
			_isSuccess = false;

			if (transIds.IsEmpty() && description.IsEmpty())
			{
				_logger = "Incorrect transHeadre Ids or description! please have a check.";
				return false;
			}

			if (isSuccess.IsNonEmpty())
			{
				if (!bool.TryParse(isSuccess, out _isSuccess))
				{
					_logger = "the IsSuccess  is error!!";
					return false;
				}
			}

			try
			{
				if (transIds.IsNonEmpty())
				{
					_transIds = Array.ConvertAll(transIds.Split(','), s => int.Parse(s));
				}
			}
			catch (Exception)
			{
				_logger = "the transHeadre Ids  is error!!";
				return false;
			}
			return true;
		}
	}
}
