/*
 * $Id$
 * Copyright (c) RIB Software AG
 * 
 */

using RIB.Visual.Basics.AssetMaster.BusinessComponents;
using System.Collections.Generic;
using System.Linq;
namespace RIB.Visual.Basics.AssetMaster.ServiceFacade.WebApi
{
	/// <summary/>
	public partial class AssetMasterDto
	{
        /// <summary>
        /// Gets and Sets AddressEntity
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public RIB.Visual.Basics.Common.ServiceFacade.WebApi.AddressDto AddressEntity { get; set; }

        /// <summary>
        /// Gets and Sets DeliveryAddressDescription
        /// </summary>
        public string DeliveryAddressDescription { get; set; }

		/// <summary>
		/// AssetMasterCount
		/// </summary>
		/// 
		public int AssetMasterCount { get; set; }
		/// <summary>
		/// 
		/// Description
		/// </summary>
		public string Description { get; set; }

		/// <summary>
		/// Child itmes
		/// </summary>
		public virtual ICollection<AssetMasterDto> AssetMasterChildren
		{
			get;
			set;
		}

		private bool _hasChildren = false;
		/// <summary>
		/// Boolean value if item has children
		/// </summary>
		public bool HasChildren
		{
			get
			{
				return _hasChildren;
			}
			set
			{
				_hasChildren = value;
			}
		}

		internal static List<AssetMasterDto> CopyChildren(AssetMasterEntity entity)
		{
			return entity.AssetMasterChildren.Select(e => new AssetMasterDto(e)).ToList();
		}

		internal void AddChildren(AssetMasterEntity entity)
		{
			if (entity.AssetMasterChildren != null && entity.AssetMasterChildren.Count > 0)
			{
				AssetMasterChildren = CopyChildren(entity).OrderBy(e => e.Code).ToList();
				HasChildren = true;
			}
			else
			{
				HasChildren = false;
			}
		}

		/// <summary>
		/// OnConstruct
		/// </summary>
		/// <param name="entity"></param>
		partial void OnConstruct(AssetMasterEntity entity)
		{
			this.AssetMasterCount = entity.AssetMasterCount;
			this.Description = entity.DescriptionInfo.Description;
			AddChildren(entity);
            if (entity.AddressEntity != null)
                AddressEntity = new RIB.Visual.Basics.Common.ServiceFacade.WebApi.AddressDto(entity.AddressEntity);
		}

		/// <summary>
		/// OnConstruct
		/// </summary>
		/// <param name="entity"></param>
		partial void OnCopy(AssetMasterEntity entity)
		{
			if (AssetMasterChildren != null)
				entity.AssetMasterChildren = AssetMasterChildren.Select(e => e.Copy()).ToList();
            if (this.AddressEntity != null)
                entity.AddressEntity = this.AddressEntity.Copy();
		}


	}
}
