﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{82E922C9-0BA9-4603-A332-08E73541434B}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RIB.Visual.Basics.AccountingJournals.BusinessComponents</RootNamespace>
    <AssemblyName>RIB.Visual.Basics.AccountingJournals.BusinessComponents</AssemblyName>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFramework>net8.0</TargetFramework>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\RIB.Visual.Basics.AccountingJournals.BusinessComponents.xml</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Action\UpdateCompanyTransHeaderAction.cs" />
    <Compile Include="Configurations\StatusWorkflowConfigurationProvider.cs" />
    <Compile Include="Logic\CompanyTransHeaderStatusHistoryLogic.cs" />
    <Compile Include="Entities\AccountingJournalsCompleteEntity.cs" />
    <Compile Include="Entities\CompanyTransHeaderStatusHistoryEntity.cs" />
    <Compile Include="Entities\CompanyTransHeaderStatusHistoryEntity.Generated.cs">
      <DependentUpon>CompanyTransHeaderStatusHistoryEntity.cs</DependentUpon>
    </Compile>
    <Compile Include="Entities\CompanyTransHeaderVEntity.cs" />
    <Compile Include="Entities\CompanyTransHeaderVEntity.Generated.cs">
      <DependentUpon>CompanyTransHeaderVEntity.cs</DependentUpon>
    </Compile>
    <Compile Include="EntityModel\EntityModel.ModelBuilder.cs" />
    <Compile Include="Logic\AccountingJournalsLogic.cs" />
    <Compile Include="Logic\CompanyTransheaderLogic.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="../../../AssemblyVersion.cs" Link="Properties/AssemblyVersion.cs" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Platform.Core">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessComponents">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.AppServer.Runtime">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.OperationalManagement">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.OperationalManagement.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessEnvironment">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessEnvironment.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Cloud.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Company.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Company.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Workflow.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Workflow.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.SqlServer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.CodeDom">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.CodeDom.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Configuration.ConfigurationManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.SqlClient">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Data.SqlClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Common">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Drawing.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AccountingJournals.Common\RIB.Visual.Basics.AccountingJournals.Common.csproj">
      <Project>{8E7DAD1C-868F-4936-B235-7873223F7FDF}</Project>
      <Name>RIB.Visual.Basics.AccountingJournals.Common</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\AccountingJournals.Core\RIB.Visual.Basics.AccountingJournals.Core.csproj">
      <Project>{BC408BF6-A751-4E17-87F7-706A8A06CF44}</Project>
      <Name>RIB.Visual.Basics.AccountingJournals.Core</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\AccountingJournals.Localization\RIB.Visual.Basics.AccountingJournals.Localization.csproj">
      <Project>{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}</Project>
      <Name>RIB.Visual.Basics.AccountingJournals.Localization</Name>
      <Private>False</Private>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="EntityModel\DataTransferObject.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\DbContext.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <DevartEntityDeploy Include="EntityModel\EntityModel.edml">
      <Generator>DevartEfGenerator</Generator>
      <LastGenOutput>EntityModel.info</LastGenOutput>
      <SubType>Designer</SubType>
    </DevartEntityDeploy>
    <None Include="EntityModel\EntityModel.edps">
      <DependentUpon>EntityModel.edml</DependentUpon>
      <SubType>Designer</SubType>
    </None>
    <None Include="EntityModel\EntityModel.info">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\EntityModel.MainDiagram.view">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\Validation.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\GroupingAttributes.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\LookupFilterKeysAttributes.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="RIBvisual.snk">
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="EntityModel\.MetaModel.xml" />
  </ItemGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F" />
  </Target>
</Project>