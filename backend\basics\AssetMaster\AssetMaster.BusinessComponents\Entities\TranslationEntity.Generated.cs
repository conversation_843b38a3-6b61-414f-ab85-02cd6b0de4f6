﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.AssetMaster.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Basics.AssetMaster.BusinessComponents.TranslationEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("BAS_TRANSLATION")]
    public partial class TranslationEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new TranslationEntity object.
        /// </summary>
        public TranslationEntity()
        {
            OnConstruct(); // call partial constructor if present
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 1)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int BasLanguageFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string Description {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SourceInfo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SOURCEINFO", TypeName = "nvarchar(255)", Order = 3)]
        [System.ComponentModel.DataAnnotations.StringLength(255)]
        public virtual string SourceInfo {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Maxlength in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MAXLENGTH", TypeName = "int", Order = 4)]
        public virtual int? Maxlength {
            get; set;
        }


        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            TranslationEntity obj = new TranslationEntity();
            obj.Id = Id;
            obj.BasLanguageFk = BasLanguageFk;
            obj.Description = Description;
            obj.SourceInfo = SourceInfo;
            obj.Maxlength = Maxlength;
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(TranslationEntity clonedEntity);

    }


}
