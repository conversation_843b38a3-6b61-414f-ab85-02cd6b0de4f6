﻿using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Basics.Core.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.AssetMaster.BusinessComponents
{
	/// <summary>
	/// Construction system script logic
	/// MdcAssetMaster foreign key selector.
	/// </summary>
	[Export("MdcAssetMaster", typeof(IScriptEntityFacade))]
	public class AssetMasterFacade : IScriptEntityFacade
	{
		private AssetMasterLogic proxy = null;

		/// <summary>
		/// 
		/// </summary>
		public AssetMasterFacade()
		{
			proxy = new AssetMasterLogic();
		}

		/// <summary>
		/// 
		/// </summary>
		public string[] Properties
		{
			get
			{
				return _entityProperties.GetPropertyNames();
			}
		}

		/// <summary>
		/// IScriptEntityFacade
		/// </summary>
		/// <param name="queryString"></param>
		/// <returns></returns>
		public IEnumerable<IDictionary<string, object>> GetSearchList(string queryString)
		{
			return proxy.GetSearchList(queryString).Select(e => e.AsDictionary(_entityProperties));
		}

		internal static ConvertProperties _entityProperties = new ConvertProperties()
				.Add("Id", "id", true)
				.Add("AssetMasterParentFk", "assetMasterParentFk", true)
				.Add("MdcContextFk", "mdcContextFk", true)
				.Add("Code", "code", true)
				.Add("AssetMasterLevel1Fk", "assetMasterLevel1Fk", true)
				.Add("AssetMasterLevel2Fk", "assetMasterLevel2Fk", true)
				.Add("AssetMasterLevel3Fk", "assetMasterLevel3Fk", true)
				.Add("AssetMasterLevel4Fk", "assetMasterLevel4Fk", true)
				.Add("AssetMasterLevel5Fk", "assetMasterLevel5Fk", true);

	}
}
