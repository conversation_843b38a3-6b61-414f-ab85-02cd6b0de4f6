﻿using RIB.Visual.Basics.Api.Common;
using RIB.Visual.Basics.Core.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public abstract class GenericRequestBase
	{
		/// <summary>
		/// Constructor
		/// </summary>
		public GenericRequestBase()
		{
			_outputOptions = LogOutputInfoFlag.Default;
		}

		private LogOutputInfoFlag _outputOptions;

		/// <summary>
		/// Determines what kind of log info can be output or not. This is used for trouble shooting when error.
		/// </summary>
		public LogOutputInfoFlag LogOptions
		{
			get { return _outputOptions; }
			set { _outputOptions = value; }
		}
	}

	/// <summary>
	/// Represents a general request for public api.
	/// </summary>
	public abstract class PublicApiRequestBase : GenericRequestBase
	{
		/// <summary>
		/// Constructor
		/// </summary>
		protected PublicApiRequestBase()
		{
		}
	}

	/// <summary>
	/// 
	/// </summary>
	public class RequestTranslationOptions
	{
		/// <summary>
		/// Specify the other languages. default to null.
		/// if set to null, other languages will not be loaded.
		/// if set to an empty array, all the other languages will be loaded. eg: []
		/// if set an specify languages array, the specify languages will be loaded. eg: ["de","zh"]
		/// </summary>
		public IEnumerable<string> Languages
		{
			get;
			set;
		}
	}
}
