﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public class PublicApiSearchRequest
	{
		/// <summary>
		/// Constructor
		/// </summary>
		public PublicApiSearchRequest()
		{
			this.PageNumber = 0;
			this.PageSize = 2147483647;
		}

		/// <summary>
		/// Filter string.
		/// i.e:
		/// "{\"operator\":\"and\",\"criterions\":[{\"operator\":\"in\",\"attribute\":\"Title\",\"valueList\":[\"Frau\",\"Unternehmen\"]},{\"operator\":\"in\",\"attribute\":\"Clerk\",\"valueList\":[\"<PERSON>erman<PERSON>, <PERSON>\"]}]}"
		/// it means Title has value "Frau" or "Unternehmen" and Clerk has value "<PERSON><PERSON><PERSON>, <PERSON>".
		/// </summary>
		public string Filter { get; set; }

		/// <summary>
		/// if not null, this indicates the page to be returned, Start record is (PageNumber*
		/// PageSize)+1 first page is 0.
		/// </summary>
		public int? PageNumber { get; set; }
		/// <summary>
		/// if not null, paging is used, only Pagesize amount of records will be returned.
		/// </summary>
		public int? PageSize { get; set; }

		/// <summary>
		/// do not add IsLive=true in query, if set to true
		/// </summary>
		public bool? IncludeNonActiveItems { get; set; }
	}
}
