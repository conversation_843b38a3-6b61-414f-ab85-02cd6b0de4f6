﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.Core.Core;
using System.Reflection;
using RIB.Visual.Basics.Api.Common;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	internal class PublicApiModelValidator
	{

		private static string[] IdentityFields = new string[] { "Code", "DescriptionInfo", "Id", "Reference" };

		private class ApiValidationContext
		{
			// init ctor.
			public ApiValidationContext()
			{
				Validators = new List<ValidationAttribute>();
				ValidationResultList = new List<ApiValidationResult>();
				Visited = new List<object>();
				this.ModelNodes = new Stack<string>();
			}

			// the property of model value to be validate.
			public string PropertyName { get; set; }

			// validation context for validation attribute.
			public ValidationContext ValidationContext { get; set; }

			// validation attribute for current model value.
			public IEnumerable<ValidationAttribute> Validators { get; set; }

			// all validation result.
			public IList<ApiValidationResult> ValidationResultList { get; set; }

			// the object list is validating.
			public IList<object> Visited { get; set; }

			/// <summary>
			/// Validate model full path.
			/// </summary>
			public Stack<string> ModelNodes { get; set; }

		}

		/// <summary>
		/// Api validatation result.
		/// </summary>
		private class ApiValidationResult : ValidationResult
		{

			/// <summary>
			/// Validate model full path.
			/// </summary>
			public Stack<string> ModelNodes { get; set; }

			public ApiValidationResult(ValidationResult result)
				: base(result)
			{
				this.ModelNodes = new Stack<string>();
			}

			/// <summary>
			/// Gets message.
			/// </summary>
			/// <returns></returns>
			public override string ToString()
			{
				string message = this.ErrorMessage;
				if (this.ModelNodes.Count > 0)
				{
					List<string> nodes = this.ModelNodes.ToList();
					nodes.Reverse();
					message = string.Join("", nodes.ToArray()) + " => " + message;
				}

				return message;
			}

		}

		/// <summary>
		/// Validate Model for ValidationAttribute.
		/// </summary>
		/// <param name="model"></param>
		/// <param name="logger"></param>
		/// <returns></returns>
		public static bool Validate(object model, IPublicApiExecutionLogger logger)
		{
			var context = new ApiValidationContext();
			//context.ModelNodes.Push("model");

			var valid = ValidateNodeAndChildren(model, context);
			if (!valid)
			{
				foreach (var msg in context.ValidationResultList)
				{
					logger.WriteError(msg.ToString());
				}
			}
			return valid;
		}

		private static bool ValidateNodeAndChildren(object model, ApiValidationContext context)
		{
			if (model == null)
			{
				return ShallowValidate(model, context);
			}

			Type type = model.GetType();

			if (IsSimpleType(type))
			{
				return ShallowValidate(model, context);
			}
			if (context.Visited.Contains(model))
			{
				return true;
			}
			context.Visited.Add(model);
			IEnumerable enumerable = model as IEnumerable;
			bool flag;
			if (enumerable == null)
			{
				flag = ValidateProperties(model, context);
			}
			else
			{
				flag = ValidateElements(enumerable, context);
			}
			if (flag)
			{
				flag = ShallowValidate(model, context);
			}
			context.Visited.Remove(model);
			return flag;
		}

		private static bool ValidateProperties(object model, ApiValidationContext apiValidationContext)
		{
			bool result = true;

			Type type = model.GetType();
			var properties = type.GetProperties(BindingFlags.Public | BindingFlags.SetProperty | BindingFlags.Instance);
			foreach (var property in properties)
			{
				var attributes = property.GetCustomAttributes(false);

				if (attributes != null && attributes.Length > 0)
				{
					var validationAttributes = attributes.OfType<ValidationAttribute>();
					if (validationAttributes != null && validationAttributes.Count() > 0)
					{
						var propValue = property.GetValue(model);

						ApiValidationContext context = new ApiValidationContext()
						{
							PropertyName = property.Name,
							ValidationContext = new ValidationContext(model)
							{
								DisplayName = property.Name,
								MemberName = property.Name
							},
							ValidationResultList = apiValidationContext.ValidationResultList,
							Validators = validationAttributes
						};

						List<string> nodes = apiValidationContext.ModelNodes.ToList();
						nodes.Reverse();

						foreach (var item in nodes)
						{
							context.ModelNodes.Push(item);
						}

						List<string> nodeInfos = new List<string>() { };
						var identityProps = properties.Where(e =>
						{
							return IdentityFields.Any(x => e.Name.EndsWith(x));
						}).ToList();
						if (identityProps.Any())
						{
							identityProps.ForEach(e =>
							{
								var value = e.GetValue(model);
								if (value != null && value is TranslateTextDescriptorApiDto)
								{
									value = ((TranslateTextDescriptorApiDto)value).Description;
								}
								if (value != null && !string.IsNullOrEmpty(value.ToString()))
								{
									nodeInfos.Add(string.Format("{0}:{1}", e.Name, value));
								}
							});
						}

						if (nodeInfos.Count > 0)
						{
							context.ModelNodes.Push("(" + string.Join(", ", nodeInfos.ToArray()) + ")");
						}
						context.ModelNodes.Push("." + property.Name);

						if (!ValidateNodeAndChildren(propValue, context))
						{
							result = false;
						}
					}
				}
			}
			return result;
		}

		private static bool ValidateElements(IEnumerable model, ApiValidationContext context)
		{
			bool result = true;

			int index = 0;
			foreach (object current in model)
			{
				if (current != null)
				{
					if (index > 0)
					{
						context.ModelNodes.Pop();
					}
					context.ModelNodes.Push("[" + index + "]");
					if (!ValidateNodeAndChildren(current, context))
					{
						result = false;
					}
				}
				index++;
			}
			return result;
		}

		private static bool ShallowValidate(object model, ApiValidationContext context)
		{
			bool result = true;

			foreach (ValidationAttribute current in context.Validators)
			{
				var validationResult = current.GetValidationResult(model, context.ValidationContext);
				if (validationResult != ValidationResult.Success)
				{
					var apiResult = new ApiValidationResult(validationResult);
					apiResult.ModelNodes = context.ModelNodes;
					context.ValidationResultList.Add(apiResult);
					result = false;
				}
			}
			return result;
		}

		private static bool IsSimpleType(Type type)
		{
			return type.IsPrimitive
				|| type.Equals(typeof(string))
				|| type.Equals(typeof(DateTime))
				|| type.Equals(typeof(decimal))
				|| type.Equals(typeof(Guid))
				|| type.Equals(typeof(DateTimeOffset))
				|| type.Equals(typeof(TimeSpan));
		}

		/// <summary>
		/// Process TruncateAttribute
		/// </summary>
		/// <param name="request"></param>
		/// <param name="logger"></param>
		/// <param name="propertyPath"></param>
		public static void TruncateRequestDto(object request, IPublicApiExecutionLogger logger, string propertyPath) //todo-stone: refactor and try to resue the same logic.
		{
			const string Length_Template = "The length of <{0}> is truncated from <{1}> to <{2}>";
			const string Value_Template = "The value of <{0}> is truncated from <{1}> to <{2}>; ";

			var properties = request.GetType().GetProperties(BindingFlags.Public | BindingFlags.SetProperty | BindingFlags.Instance);
			foreach (var property in properties)
			{
				if (property.PropertyType == typeof(string))
				{
					var attribute = property.GetCustomAttribute<TruncateAttribute>();
					if (attribute != null)
					{
						var propertyValue = property.GetValue(request) as string;
						if (!string.IsNullOrEmpty(propertyValue))
						{
							if (propertyValue.Length > attribute.MaxLength)
							{
								int oldLength = propertyValue.Length;
								string oldValue = propertyValue;
								propertyValue = propertyValue.Substring(0, attribute.MaxLength);
								//truncate value
								property.SetValue(request, propertyValue);
								string currentPropertyPath = string.IsNullOrEmpty(propertyPath) ? property.Name : propertyPath + "." + property.Name;
								var msg1 = string.Format(Value_Template, currentPropertyPath, oldValue, propertyValue);
								var msg2 = string.Format(Length_Template, propertyPath, oldLength, attribute.MaxLength);
								logger.WriteWarning(msg1 + msg2);
							}
						}
					}
				}
				else if (property.PropertyType == typeof(TranslateTextDescriptorApiDto))
				{
					var attribute = property.GetCustomAttribute<TruncateAttribute>();
					if (attribute != null)
					{
						var trDescriptor = property.GetValue(request) as TranslateTextDescriptorApiDto;
						if (trDescriptor != null && !string.IsNullOrEmpty(trDescriptor.Description))
						{
							if (trDescriptor.Description.Length > attribute.MaxLength)
							{
								int oldLength = trDescriptor.Description.Length;
								string oldValue = trDescriptor.Description;
								//truncate value
								trDescriptor.Description = trDescriptor.Description.Substring(0, attribute.MaxLength);

								string currentPropertyPath = string.IsNullOrEmpty(propertyPath) ? property.Name : propertyPath + "." + property.Name + ".Description";
								var msg1 = string.Format(Value_Template, currentPropertyPath, oldValue, trDescriptor.Description);
								var msg2 = string.Format(Length_Template, currentPropertyPath, oldLength, attribute.MaxLength);
								logger.WriteWarning(msg1 + msg2);

								if (trDescriptor.OtherLanguages != null)
								{
									currentPropertyPath = string.IsNullOrEmpty(propertyPath) ? property.Name : propertyPath + "." + property.Name + ".OtherLanguages.Description";
									foreach (var otherItem in trDescriptor.OtherLanguages)
									{
										if (!string.IsNullOrEmpty(otherItem.Description))
										{
											if (otherItem.Description.Length > attribute.MaxLength)
											{
												oldLength = otherItem.Description.Length;
												oldValue = otherItem.Description;
												//truncate value
												otherItem.Description = otherItem.Description.Substring(0, attribute.MaxLength);
												msg1 = string.Format(Value_Template, currentPropertyPath, oldValue, otherItem.Description);
												msg2 = string.Format(Length_Template, currentPropertyPath, oldLength, attribute.MaxLength);
												logger.WriteWarning(msg1 + msg2);
											}
										}
									}
								}
							}
						}
					}
				}
				else if (property.PropertyType.IsClass || property.PropertyType.IsInterface)
				{
					var attribute = property.GetCustomAttribute<TruncatableObjectAttribute>();
					if (attribute != null)
					{
						var propertyInstance = property.GetValue(request);
						if (propertyInstance != null)
						{
							if (propertyInstance is IEnumerable)
							{
								//it's IEnumerable
								var realInstance = propertyInstance as IEnumerable;
								string currentPropertyPath = string.IsNullOrEmpty(propertyPath) ? property.Name : propertyPath + "." + property.Name;
								foreach (var item in realInstance)
								{
									TruncateRequestDto(item, logger, currentPropertyPath);
								}
							}
							else
							{
								//it's a object
								string currentPropertyPath = string.IsNullOrEmpty(propertyPath) ? property.Name : propertyPath + "." + property.Name;
								TruncateRequestDto(propertyInstance, logger, currentPropertyPath);
							}
						}
					}
				}
			}
		}
	}
}
