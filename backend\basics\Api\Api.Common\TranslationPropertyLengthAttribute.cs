﻿using RIB.Visual.Basics.Core.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Represent a string or translation property which can be truncated if the length of value exceed the max length.
	/// </summary>
	[AttributeUsage(AttributeTargets.Property)]
	public class TranslationPropertyLengthAttribute : ValidationAttribute
	{
		private const string DescriptionProperty = "Description";
		private const string OtherLanguagesProperty = "OtherLanguages";

		/// <summary>
		/// Contructor.
		/// </summary>
		/// <param name="maxLength">The allowed max length of string or translation property</param>
		public TranslationPropertyLengthAttribute(int maxLength)
			: this(0, maxLength)
		{
		}

		/// <summary>
		/// Contructor.
		/// </summary>
		/// <param name="minLength">The allowed min length of string or translation property</param>
		/// <param name="maxLength">The allowed max length of string or translation property</param>
		public TranslationPropertyLengthAttribute(int minLength, int maxLength)
		{
			MinLength = minLength;
			MaxLength = maxLength;
			AllowMaxLengthCheck = true;
		}

		/// <summary>
		/// Gets or sets MaxLength.
		/// </summary>
		public int MaxLength
		{
			get;
			set;
		}

		/// <summary>
		/// Gets or sets MinLength.
		/// </summary>
		public int MinLength
		{
			get;
			set;
		}

		/// <summary>
		/// Gets or sets AllowMaxLengthCheck.
		/// </summary>
		public bool AllowMaxLengthCheck
		{
			get;
			set;
		}

		/// <summary>
		/// Determines whether a specified object is valid.
		/// </summary>
		/// <param name="value">The object to validate.</param>
		/// <param name="validationContext">The validation context</param>
		/// <returns></returns>
		protected override ValidationResult IsValid(object value, ValidationContext validationContext)
		{
			ValidationResult result = ValidationResult.Success;

			if (value != null)
			{
				var translationProperty = validationContext.ObjectType.GetProperty(validationContext.MemberName);
				StringBuilder errorListSb = new StringBuilder();

				if (validationContext.ObjectInstance != null)
				{
					var property = translationProperty.GetValue(validationContext.ObjectInstance);
					if (property != null)
					{
						var propertyValue = (string)property.GetType().GetProperty(DescriptionProperty).GetValue(property);

						if (string.IsNullOrEmpty(propertyValue))
						{
							if (MinLength > 0)
							{
								string errorMsg = string.Format("[{0}.{1}] is required.", validationContext.MemberName, DescriptionProperty);
								errorListSb.AppendLine(errorMsg);
							}
						}
						else
						{
							if (MinLength > propertyValue.Length)
							{
								string errorMsg = string.Format("The length of [{0}.{1}] must be greater than [{2}]", validationContext.MemberName, DescriptionProperty, MinLength);
								errorListSb.AppendLine(errorMsg);
							}

							if (AllowMaxLengthCheck)
							{
								if (MaxLength < propertyValue.Length)
								{
									string errorMsg = string.Format("The length of [{0}.{1}] must not be greater than [{2}]", validationContext.MemberName, DescriptionProperty, MaxLength);
									errorListSb.AppendLine(errorMsg);
								} 
							}
						}
					}

					var otherLangPropertyValues = (IEnumerable<Object>)property.GetType().GetProperty(OtherLanguagesProperty).GetValue(property);
					if (otherLangPropertyValues != null && otherLangPropertyValues.Any())
					{
						int index = 0;

						foreach(var otherLangItem in otherLangPropertyValues)
						{
							var text = (string)otherLangItem.GetType().GetProperty(DescriptionProperty).GetValue(otherLangItem);
							if (string.IsNullOrEmpty(text))
							{
								if (MinLength > 0)
								{
									string errorMsg = string.Format("[{0}.{1}[{2}]] is required.", validationContext.MemberName, OtherLanguagesProperty, index);
									errorListSb.AppendLine(errorMsg);
								}
							}
							else
							{
								if (MinLength > text.Length)
								{
									string errorMsg = string.Format("The length of [{0}.{1}[{2}]] must be greater than [{3}]", validationContext.MemberName, OtherLanguagesProperty, index, MinLength);
									errorListSb.AppendLine(errorMsg);
								}
							
								if (AllowMaxLengthCheck)
								{
									if (MaxLength < text.Length)
									{
										string errorMsg = string.Format("The length of [{0}.{1}[{2}]]  must be not greater than [{3}]", validationContext.MemberName, OtherLanguagesProperty, index, MaxLength);
										errorListSb.AppendLine(errorMsg);
									}
								}
							}

							index++;
						}
					}

					if (errorListSb.Length > 0)
					{
						result = new ValidationResult(errorListSb.ToString(), new List<string>() { validationContext.MemberName });
					}
				}
			}

			return result;
		}
	}
}
