﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Client.Itwo40WebApi
{
	class Program
	{
		static void Main(string[] args)
		{
			Model theModel = new Model()
			{
				BaseUrl = Constant.BaseUrl1,
				IdentityServerUrl = Constant.IdentityServerUrl2,
				//BaseUrl = Constant.BaseUrl5,
				//IdentityServerUrl = Constant.IdentityServerUrl5,
				//BaseUrl = Constant.BaseUrl2,
				//IdentityServerUrl = Constant.IdentityServerUrl2,
				Username = "ribadmin"
			};
			theModel.SetToSecurePassword("ribadmin");

			Utility util = new Utility() { TheModel = theModel };

			//util.LoginAppServerviaAsync();

			//util.GetCompaniesWithRolesAwait();

			//util.GetProjectWithRevitModelAwait();
			//util.LoginAppServer();

			util.UploadModelAwait();

			Console.WriteLine("Press Any Key to Finish");
			var result = Console.ReadLine();
			Console.WriteLine("done...");


		}
	}
}
