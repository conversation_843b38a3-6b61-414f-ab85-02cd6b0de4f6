﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// 
	/// </summary>
	public class MultipartFormDataType
	{
		/// <summary />
		public const string Boolean = "boolean";

		/// <summary />
		public const string Integer = "integer";

		/// <summary />
		public const string Number = "number";

		/// <summary />
		public const string String = "string";

		/// <summary />
		public const string Object = "object";

		/// <summary />
		public const string File = "file";
	}

	/// <summary>
	/// 
	/// </summary>
	[AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
	public class ApiDocMultipartFormDataAttribute : Attribute
	{
		/// <summary />
		public string Name { get; set; }

		/// <summary />
		public string Description { get; set; }

		/// <summary />
		public bool Required { get; set; }

		/// <summary />
		public string Type { get; set; }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="name">name</param>
		/// <param name="description">description</param>
		/// <param name="required">required</param>
		/// <param name="type">see class MultipartFormDataType</param>
		public ApiDocMultipartFormDataAttribute(string name, string description, bool required, string type)
		{
			this.Name = name;
			this.Description = description;
			this.Required = required;
			this.Type = type;
		}
	}
}
