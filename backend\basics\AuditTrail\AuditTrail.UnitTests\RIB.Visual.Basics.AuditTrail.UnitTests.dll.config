<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<configSections>
		<section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />

	</configSections>>
	<system.serviceModel>
		<diagnostics>
			<messageLogging logMalformedMessages="true" logMessagesAtServiceLevel="true" logMessagesAtTransportLevel="true" maxSizeOfMessageToLog="50000000" />
		</diagnostics>
		<extensions>
			<behaviorExtensions>
				<add name="customFaultHandling" type="RIB.Visual.Platform.ServiceFacade.Wcf.FaultBehavior, RIB.Visual.Platform.ServiceFacade.Wcf" />
			</behaviorExtensions>
		</extensions>
		<behaviors>
			<endpointBehaviors>
				<behavior name="Default">
					<customFaultHandling />
				</behavior>
			</endpointBehaviors>
			<serviceBehaviors>
				<behavior name="Default">
					<dataContractSerializer maxItemsInObjectGraph="200000" />
					<serviceThrottling maxConcurrentCalls="1000" maxConcurrentSessions="1000" maxConcurrentInstances="1000" />
					<serviceMetadata httpGetEnabled="true" />
				</behavior>
			</serviceBehaviors>
		</behaviors>
		<bindings>
			<basicHttpBinding>
				<binding name="Default" sendTimeout="00:20:00" bypassProxyOnLocal="true" maxBufferSize="10000000" maxBufferPoolSize="50000000" maxReceivedMessageSize="10000000">
					<readerQuotas maxStringContentLength="10000000" maxArrayLength="10000000" />
				</binding>
				<binding name="Default-Large" sendTimeout="00:30:00" bypassProxyOnLocal="true" maxBufferSize="50000000" maxBufferPoolSize="50000000" maxReceivedMessageSize="50000000">
					<readerQuotas maxStringContentLength="50000000" maxArrayLength="50000000" />
				</binding>
				<binding name="Streaming" sendTimeout="12:00:00" bypassProxyOnLocal="true" maxBufferSize="10000000" maxBufferPoolSize="50000000" maxReceivedMessageSize="2147483648" transferMode="Streamed">
					<readerQuotas maxStringContentLength="10000000" maxArrayLength="10000000" />
				</binding>
			</basicHttpBinding>
			<netTcpBinding>
				<binding name="Default" sendTimeout="00:20:00" maxBufferPoolSize="50000000" maxBufferSize="10000000" maxConnections="1000" maxReceivedMessageSize="10000000" portSharingEnabled="true">
					<readerQuotas maxStringContentLength="10000000" maxArrayLength="10000000" />
					<security mode="None" />
				</binding>
				<binding name="Default-Large" sendTimeout="00:30:00" maxBufferPoolSize="50000000" maxBufferSize="50000000" maxConnections="1000" maxReceivedMessageSize="50000000" portSharingEnabled="true">
					<readerQuotas maxStringContentLength="50000000" maxArrayLength="50000000" />
					<security mode="None" />
				</binding>
				<binding name="Streaming" sendTimeout="12:00:00" maxBufferPoolSize="50000000" maxBufferSize="10000000" maxConnections="1000" maxReceivedMessageSize="2147483648" transferMode="Streamed" portSharingEnabled="true">
					<readerQuotas maxStringContentLength="10000000" maxArrayLength="10000000" />
					<security mode="None" />
				</binding>
			</netTcpBinding>
		</bindings>
		<serviceHostingEnvironment aspNetCompatibilityEnabled="true" multipleSiteBindingsEnabled="true" />
	</system.serviceModel>
	<runtime>
		<generatePublisherEvidence enabled="false" />
	</runtime>
	<system.webServer>
		<defaultDocument>
			<files>
				<clear />
				<add value="default.aspx" />
			</files>
		</defaultDocument>
		<urlCompression doDynamicCompression="false" />
	</system.webServer>
	<system.web>
		<identity impersonate="false" />
		<httpRuntime executionTimeout="72000" maxRequestLength="10000" />
	</system.web>

	<connectionStrings>
		<!--<add connectionString="Server=.\;Database=itwocloud;Integrated Security=true" name="Default" />-->
		<!--<add connectionString="Server=************\DEV;Database=itwocloud;Integrated Security=true" name="Default" />-->
		<add connectionString="Server=rib-s-sql-dev\DEV1;Database=itwocloud;Integrated Security=true" name="Default" />
	</connectionStrings>

	<entityFramework>
		<interceptors>
			<interceptor type="System.Data.Entity.Infrastructure.Interception.DatabaseLogger, EntityFramework">
				<parameters>
					<parameter value="C:\Temp\EF61-SQLLog-Output.txt"/>
				</parameters>
			</interceptor>
		</interceptors>
	</entityFramework>

</configuration>
