using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.ComponentModel;

namespace RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample
{

	/// <summary>
	/// 
	/// </summary>
	public class ConsumeItwo40WebApi
	{
		/// <summary/>
		public ServiceData TheServiceData { get; set; }

		/// <summary/>
		// ReSharper disable once MemberCanBePrivate.Global
		public string CompanyRolesResponse { get; set; }

		public LogonServices LogonServices { get; set; }

		private void worker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
		{
			Console.WriteLine("Logon done...");
			Console.WriteLine(string.Format("Token: {0}", LogonServices.Token));
		}

		public ConsumeItwo40WebApi()
		{
			LogonServices = new LogonServices();
		}

		/// <summary>
		/// 
		/// </summary>
		public async void LoginAppServerviaAsync()
		{
			TheServiceData.JsonOutput = "Login via Application Server running...   Please wait...";
			TheServiceData.SetToSecurePassword("ribadmin");

			LogonServices.Token = await LogonServices.DoLoginAsync(TheServiceData);

			Console.WriteLine(LogonServices.GetTokenClaimsInfo());

		}

		/// <summary>
		/// Sample method, 
		/// just login and read the available Companies to the logged in user.
		/// </summary>
		public async void GetCompaniesWithRolesAwait()
		{
			LogonServices.ServicesUrl = TheServiceData.ServicesUrl;
			LogonServices.Token = await LogonServices.DoLoginAsync(TheServiceData);
			Console.WriteLine(LogonServices.GetTokenClaimsInfo());

			Console.WriteLine("Logon Done....");

			var response = await LogonServices.GetCompaniesWithRolesAsync();
			WritelineObjectResult(response);
			CompanyRolesResponse = response.ResponseValue;

		}

		private void WritelineArrayResult(Response response)
		{
			var xyz = JArray.Parse(response.ResponseValue);
			var res = JsonConvert.SerializeObject(xyz, Formatting.Indented);
			Console.WriteLine(res);
		}

		private void WritelineObjectResult(Response response)
		{
			if (response.ObjValue != null)
			{
				var res = JsonConvert.SerializeObject(response.ObjValue, Formatting.Indented);
				Console.WriteLine(res);
				return;
			}
			if (response.ArrValue != null)
			{
				var res = JsonConvert.SerializeObject(response.ArrValue, Formatting.Indented);
				Console.WriteLine(res);
				return;
			}
			try
			{
				var xyz = JObject.Parse(response.ResponseValue);
				var res1 = JsonConvert.SerializeObject(xyz, Formatting.Indented);
				Console.WriteLine(res1);
			}
			catch (Exception ex)
			{
				Console.WriteLine(ex.Message);
				Console.WriteLine(response.ResponseValue);
			}
		}

		/// <summary>
		/// Complete sample with 
		/// Login
		/// reading available companies and roles to logged in user
		/// analyzing the company tree.
		/// showing rolesLookup
		/// 
		/// creating a context and init it with a company context
		/// validate that company context via a server round trip
		/// after successful validation create a secureContext and
		/// use this secure context for consecutive web-api calls.
		/// 
		/// </summary>
		public async void LogonwithCompanyRoleSelectAwait()
		{
			// 1. Step Login via Services Url
			LogonServices.ServicesUrl = TheServiceData.ServicesUrl;
			LogonServices.Token = await LogonServices.DoLoginAsync(TheServiceData);
			Console.WriteLine(LogonServices.GetTokenClaimsInfo());
			Console.WriteLine("Logon Done....");

			// 2. Step:  read Companies and Role Info
			var response = await LogonServices.GetCompaniesWithRolesAsync();
			WritelineObjectResult(response);

			// sample for iterating thru JObject....
			var companies = response.ObjValue["companies"];

			#region iterate over companies and roleslookup
			foreach (var company in companies)
			{
				var companyId = company["id"].Value<int>();
				var companyType = company["companyType"].Value<int>();
				var canLogin = company["canLogin"].Value<bool>();
				var name = company["name"];

				// check for a valid login company:  only copmany =1 and profit Center =3 are valid 
				if (canLogin && (companyType == 1 || companyType == 3))
				{
					Console.WriteLine("Is Login Company:    Id: {0} Type: {1} Name: {2} canLogin: {3}", companyId, companyType, name, canLogin);
				}
				else
				{
					Console.WriteLine("Not a Login Company: Id: {0} Type: {1} Name: {2} canLogin: {3}", companyId, companyType, name, canLogin);
				}
			}

			var roles = response.ObjValue["roles"];
			var rolesLookup = response.ObjValue["rolesLookup"];
			foreach (var role in rolesLookup)
			{
				var roleId = role["key"];
				var roleName = role["value"];
				Console.WriteLine("Role: {0}={1}", roleId, roleName);
			}

			#endregion


			// 3. Step: Check Company via ClientIds
			// select a company and role from above company / Role List and request secure client context
			// public ServiceClientContext(int signedInClientId = 0, int clientId = 0, int permissionClient = 0, int permissionRoleId = 0,
			//                             int dataLanguageId = 0, string language = "en", string culture = "en-gb")
			LogonServices.ServiceClientContext = new ServiceClientContext();

			// we assume login with Company:  SigninCompany,ClientCompany,PermissionCompany Id: 1, Role : 1
			var checkResult = await LogonServices.CheckCompanyToAssignedCompaniesAsync(1, 1, 1, 1);
			WritelineObjectResult(checkResult);

			// check for valid company to this login, please check result
			var isValid = checkResult.ObjValue["isValid"].Value<bool>();
			if (isValid)  // everything is ok
			{
				// continue.....
			}

			// 3'. Step: Check Company via Company Code
			// we login via the company code only with Role Id, in this case role Id is the admin role.
			// all the value have to be check from the previous call GetCompaniesWithRolesAsync.
			var checkResult2 = await LogonServices.CheckCompanyviaCompanyCodeAsync("901", 1); // roleId is optional, if missing first role will be taken
			WritelineObjectResult(checkResult2);

			// check for valid company to this login, please check result
			var isValid2 = checkResult2.ObjValue["isValid"].Value<bool>();
			if (isValid2)  // everything is ok
			{

			}

			// 4. Step: Create ClientContext with SecureCompanyRolePart 
			// Handle   S e c u r e C l i e n t   C o n t e x t   S e c u r e C l i e n t   C o n t e x t 
			//
			// 1. read SecureClientRole info from checkResult of previous call to CheckCompanyToAssignedCompaniesAsync()
			// 2. create ClientContext with SecureClientRole
			// 3. set ServiceClientContext
			// 4. perform all subsequent calls
			//
			// pickup secure context from result
			// var securePart = JObject.Parse(checkResult.ResponseValue).Value<string>("secureClientRolePart");
			// 
			// both checkResult and checkResult2 containing the same result, take your choice
			var securePart = checkResult.ObjValue["secureClientRolePart"].ToString();

			//myContext = new ServiceClientContext(securePart, 1, "en", "en-gb");
			LogonServices.ServiceClientContext = new ServiceClientContext(securePart, 1);

			SystemInfo.LogonServices = LogonServices;
			// 5. Step: call your business web-api's 
			var systemInfo = await SystemInfo.GetSystemInfoAsync();
			WritelineObjectResult(systemInfo);

			BusinessPartner.LogonServices = LogonServices;
			var bpds = await BusinessPartner.GetBusinessPartnerAsync("901", 24);
			WritelineObjectResult(bpds);

		}

		/// <summary>
		/// Sample with creation and reading of a User
		///		Login
		///		set conext and validate it
		///		read user 
		///		create new user
		/// 
		/// </summary>
		public async void DealwithUserManagement()
		{
			// 1. Step Login via Services Url
			LogonServices.ServicesUrl = TheServiceData.ServicesUrl;
			LogonServices.Token = await LogonServices.DoLoginAsync(TheServiceData);
			Console.WriteLine(LogonServices.GetTokenClaimsInfo());
			Console.WriteLine("Logon Done....");

			// 2'. Step: Check Company via Compnay Code
			// we login via the company code only with Role Id, in this case role Id is the admin role.
			// all the value have to be check from the previous call GetCompaniesWithRolesAsync.
			var checkResult = await LogonServices.CheckCompanyviaCompanyCodeAsync("901", 1);
			WritelineObjectResult(checkResult);

			// check for valid company to this login, please check result
			var isValid = checkResult.ObjValue["isValid"].Value<bool>();
			if (isValid)  // everything is ok
			{

			}

			var securePart = checkResult.ObjValue["secureClientRolePart"].ToString();
			LogonServices.ServiceClientContext = new ServiceClientContext(securePart, 1);

			UserManagement.LogonServices = LogonServices;
			var theUserInfo = await UserManagement.GetUserByLogonNameAsynch("ribadmin");
			WritelineObjectResult(theUserInfo);

			var userdto = new UserDto() { Name = "rei superman", Description = "New User create via web-api", EMail = "<EMAIL>", LogonName = "<EMAIL>", Password = "nevermind" };
			var theUser = await UserManagement.CreateUserAsynch(userdto);

		}

		/// <summary>
		/// Sample with read a business partner
		///		Login
		///		set conext and validate it
		///		read user 
		///		create new user
		/// 
		/// </summary>
		public async void DealwithBusinessPartnerManagement()
		{
			// 1. Step Login via Services Url
			LogonServices.ServicesUrl = TheServiceData.ServicesUrl;
			LogonServices.Token = await LogonServices.DoLoginAsync(TheServiceData);
			Console.WriteLine(LogonServices.GetTokenClaimsInfo());
			Console.WriteLine("Logon Done....");

			// 2'. Step: Check Company via Compnay Code
			// we login via the company code only with Role Id, in this case role Id is the admin role.
			// all the value have to be check from the previous call GetCompaniesWithRolesAsync.
			var checkResult = await LogonServices.CheckCompanyviaCompanyCodeAsync("901", 1);
			WritelineObjectResult(checkResult);

			// check for valid company to this login, please check result
			var isValid = checkResult.ObjValue["isValid"].Value<bool>();
			if (isValid)  // everything is ok
			{

			}

			var securePart = checkResult.ObjValue["secureClientRolePart"].ToString();
			LogonServices.ServiceClientContext = new ServiceClientContext(securePart, 1);
			BusinessPartner.LogonServices = LogonServices;
			var bpd = await BusinessPartner.GetBusinessPartnerAsync("901", 5);
			WritelineObjectResult(bpd);


			bpd = await BusinessPartner.CreateBusinessPartnerAsync("901", "SuperMan", "the second");
			WritelineObjectResult(bpd);

		}

		/// <summary>
		/// Sample with read a business partner
		///		Login
		///		set conext and validate it
		///		read user 
		///		create new user
		/// 
		/// </summary>
		public async void DealwithCheckCompany(string info, string companyCode, int roleId)
		{
			// 1. Step Login via Services Url
			LogonServices.ServicesUrl = TheServiceData.ServicesUrl;
			LogonServices.Token = await LogonServices.DoLoginAsync(TheServiceData);
			if (LogonServices.Token != null)
			{
				Console.WriteLine(LogonServices.GetTokenClaimsInfo());
				Console.WriteLine("Logon " + info + " done...");
			}
			else
			{
				Console.WriteLine("Logon " + info + " faield! ");
			}

			// 2'. Step: Check Company via Compnay Code
			// we login via the company code only with Role Id, in this case role Id is the admin role.
			// all the value have to be check from the previous call GetCompaniesWithRolesAsync.
			var checkResult = await LogonServices.CheckCompanyviaCompanyCodeAsync(companyCode, roleId);
			WritelineObjectResult(checkResult);

			// check for valid company to this login, please check result
			var isValid = checkResult.ObjValue["isValid"].Value<bool>();
			if (isValid)  // everything is ok
			{
				Console.WriteLine("CheckCompany with CompanyCode: {0}, RoleId: {1} ok ", companyCode, roleId);
			}
			else
			{
				Console.WriteLine("CheckCompany with CompanyCode: {0}, RoleId: {1} failed", companyCode, roleId);
			}
			var securePart = checkResult.ObjValue["secureClientRolePart"].ToString();
			LogonServices.ServiceClientContext = new ServiceClientContext(securePart, 1);
		}
	}
}