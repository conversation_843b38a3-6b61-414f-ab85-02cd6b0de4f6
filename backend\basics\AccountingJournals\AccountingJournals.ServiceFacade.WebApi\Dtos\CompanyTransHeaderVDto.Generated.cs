﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Basics.AccountingJournals.BusinessComponents;

namespace RIB.Visual.Basics.AccountingJournals.ServiceFacade.WebApi
{


    /// <summary>
    /// There are no comments for CompanyTransHeaderVEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("BAS_COMPANY_TRANSHEADER_V")]
    public partial class CompanyTransHeaderVDto : RIB.Visual.Platform.Core.ITypedDto<CompanyTransHeaderVEntity>
    {
        #region Constructors

        /// <summary>
        /// Initializes an instance of class CompanyTransHeaderVDto.
        /// </summary>
        public CompanyTransHeaderVDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class CompanyTransHeaderVDto.
        /// </summary>
        /// <param name="entity">the instance of class CompanyTransHeaderVEntity</param>
        public CompanyTransHeaderVDto(CompanyTransHeaderVEntity entity)
        {
            Id = entity.Id;
            Description = entity.Description;
            CompanyFk = entity.CompanyFk;
            CompanyPeriodFk = entity.CompanyPeriodFk;
            PostingDate = entity.PostingDate;
            TransactiontypeFk = entity.TransactiontypeFk;
            Issuccess = entity.Issuccess;
            ReturnValue = entity.ReturnValue;
            CompanyTransheaderFk = entity.CompanyTransheaderFk;
            CommentText = entity.CommentText;
            CompanyTransHeaderStatusFk = entity.CompanyTransHeaderStatusFk;
            StartDate = entity.StartDate;
            EndDate = entity.EndDate;
            TradingPeriod = entity.TradingPeriod;
            TradingYear = entity.TradingYear;
            TypeAbbreviation = entity.TypeAbbreviation;

            if (entity.TypeDescriptionInfo != null )
            {
                TypeDescriptionInfo = new DescriptionTranslateTypeDto(entity.TypeDescriptionInfo);
            }

            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }
    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 1)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Description { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_FK", TypeName = "int", Order = 2)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyFk { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyPeriodFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_PERIOD_FK", TypeName = "int", Order = 3)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyPeriodFk { get; set; }
    
        /// <summary>
        /// There are no comments for PostingDate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("POSTING_DATE", TypeName = "date", Order = 4)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime PostingDate { get; set; }
    
        /// <summary>
        /// There are no comments for TransactiontypeFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_TRANSACTIONTYPE_FK", TypeName = "int", Order = 5)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int TransactiontypeFk { get; set; }
    
        /// <summary>
        /// There are no comments for Issuccess in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISSUCCESS", TypeName = "bit", Order = 6)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool Issuccess { get; set; }
    
        /// <summary>
        /// There are no comments for ReturnValue in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RETURN_VALUE", TypeName = "nvarchar(2000)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ReturnValue { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyTransheaderFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_TRANSHEADER_FK", TypeName = "int", Order = 8)]
        public int? CompanyTransheaderFk { get; set; }
    
        /// <summary>
        /// There are no comments for CommentText in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("COMMENT_TEXT", TypeName = "nvarchar(255)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(255)]
        public string CommentText { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyTransHeaderStatusFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("COMPANYTRANSHEADERSTATUSFK", TypeName = "int", Order = 10)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyTransHeaderStatusFk { get; set; }
    
        /// <summary>
        /// There are no comments for StartDate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("START_DATE", TypeName = "date", Order = 11)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime StartDate { get; set; }
    
        /// <summary>
        /// There are no comments for EndDate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("END_DATE", TypeName = "date", Order = 12)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime EndDate { get; set; }
    
        /// <summary>
        /// There are no comments for TradingPeriod in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TRADING_PERIOD", TypeName = "int", Order = 13)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int TradingPeriod { get; set; }
    
        /// <summary>
        /// There are no comments for TradingYear in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TRADING_YEAR", TypeName = "int", Order = 14)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int TradingYear { get; set; }
    
        /// <summary>
        /// There are no comments for TypeDescriptionInfo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TYPE_DESCRIPTION", TypeName = "nvarchar(252)", Order = 15, TranslationColumnName = "TYPE_DESCRIPTION_TR")]
        public DescriptionTranslateTypeDto TypeDescriptionInfo { get; set; }
    
        /// <summary>
        /// There are no comments for TypeAbbreviation in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TYPE_ABBREVIATION", TypeName = "nvarchar(16)", Order = 17)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string TypeAbbreviation { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(CompanyTransHeaderVEntity); }
        }

        /// <summary>
        /// Copy the current CompanyTransHeaderVDto instance to a new CompanyTransHeaderVEntity instance.
        /// </summary>
        /// <returns>a new instance of class CompanyTransHeaderVEntity</returns>
        public CompanyTransHeaderVEntity Copy()
        {
          var entity = new CompanyTransHeaderVEntity();

          entity.Id = this.Id;
          entity.Description = this.Description;
          entity.CompanyFk = this.CompanyFk;
          entity.CompanyPeriodFk = this.CompanyPeriodFk;
          entity.PostingDate = this.PostingDate;
          entity.TransactiontypeFk = this.TransactiontypeFk;
          entity.Issuccess = this.Issuccess;
          entity.ReturnValue = this.ReturnValue;
          entity.CompanyTransheaderFk = this.CompanyTransheaderFk;
          entity.CommentText = this.CommentText;
          entity.CompanyTransHeaderStatusFk = this.CompanyTransHeaderStatusFk;
          entity.StartDate = this.StartDate;
          entity.EndDate = this.EndDate;
          entity.TradingPeriod = this.TradingPeriod;
          entity.TradingYear = this.TradingYear;
          entity.TypeAbbreviation = this.TypeAbbreviation;

          if (this.TypeDescriptionInfo != null )
          {

               entity.TypeDescriptionInfo = new DescriptionTranslateType(this.TypeDescriptionInfo);
          }

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(CompanyTransHeaderVEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(CompanyTransHeaderVEntity entity);
    }

}
