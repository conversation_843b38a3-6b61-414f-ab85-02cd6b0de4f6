﻿using RIB.Visual.Platform.Common;
using System.Collections.Generic;
using System;
using RIB.Visual.Awp.Main.BusinessComponents;

namespace RIB.Visual.Awp.Main.ServiceFacade.WebApi
{
    /// <summary>
    /// a simple class with id and briefInof about a BOQ item in an AWPPackage
    /// </summary>
    public class AwpPackageBoqItemDto
    {
        /// <summary>
        /// 
        /// </summary>
        public Int32 BoqHeaderFk { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Int32 Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DescriptionTranslateType BriefInfo { get; set; }

        /// <summary>
        /// 
        /// </summary>
		public AwpPackageBoqItemDto()
		{
			
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entity"></param>
		public AwpPackageBoqItemDto(AwpPackageBoqItemEntity entity)
		{
            if(entity != null)
            {
                this.BoqHeaderFk = entity.BoqHeaderFk;
                this.Id = entity.Id;
                this.BriefInfo = entity.BriefInfo;
            }
		}

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public AwpPackageBoqItemEntity Copy()
        {
            return new AwpPackageBoqItemEntity()
            {
                BoqHeaderFk = this.BoqHeaderFk,
                Id = this.Id,
                BriefInfo = this.BriefInfo
            };
        }
	}
}
