﻿using System.ComponentModel.Composition;
using RIB.Visual.Basics.AssetMaster.BusinessComponents;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using RIB.Visual.Basics.LookupData.Core;
using RIB.Visual.Basics.LookupData.ServiceFacade.WebApi;

namespace RIB.Visual.Basics.AssetMaster.ServiceFacade.WebApi.LookupServices
{
	/// <summary>
	/// A lookup service
	/// </summary>
	[LookupType("AssertMaster")]
	public class AssetMasterLookupService : LookupServiceBaseNew<AssetMasterEntity, AssetMasterDto>
	{
		/// <summary>
		/// 
		/// </summary>
		public AssetMasterLookupService()
			: base(new AssetMasterLogic())
		{
			this.ChildrenGetterFunc = (e) => e.AssetMasterChildren;
		}

		/// <summary>
		/// Maps an entity to a Dto
		/// </summary>
		/// <param name="entity"></param>
		/// <returns></returns>
		protected override AssetMasterDto MapEntityTo(AssetMasterEntity entity)
		{
			return new AssetMasterDto(entity);
		}
	}
}
