using RIB.Visual.Basics.Core.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.Threading;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// 
	/// </summary>
	public class ImportContentLogger : IImportContentLogger
	{
		// config for logger.
		private const int Capacity = 800;
		private const int StartProcessCount = 500;
		private const int LogCount = 50;
		private const int InfoBeforeCount = 10;
		private const int InfoAfterCount = 10;
		private const int WarningBeforeCount = 10;
		private const int WarningAfterCount = 10;
		private const int DebugBeforeCount = 10;
		private const int DebugAfterCount = 10;


		private IList<PublicApiValidationResult> _buffer = null;
		private BlockingCollection<PublicApiValidationResult> _validationResults = null;
		private bool outOfCapacity = false;
		private Task processTask = null;

		private bool isStarted = false;

		private bool hasError = false;


		/// <summary>
		/// Constructor.
		/// </summary>
		public ImportContentLogger()
		{
			this._validationResults = new BlockingCollection<PublicApiValidationResult>();
			this._buffer = new List<PublicApiValidationResult>();
			this.ValidationResults = new List<PublicApiValidationResult>();
		}

		/// <summary>
		/// 
		/// </summary>
		public void StartWrite(ImportMasterDataContext context)
		{
			if (isStarted)
			{
				return;
			}
			isStarted = true;
			processTask = Task.Factory.StartNew(() =>
			{
				bool outOfStartProcessCount = false;

				while (!_validationResults.IsAddingCompleted)
				{
					Console.WriteLine("Count: {0}.", _validationResults.Count);
					if (_validationResults.Count > StartProcessCount)
					{
						outOfStartProcessCount = true;
						break;
					}
					else
					{
						Thread.Sleep(100);
					}
				}
				if (_validationResults.Count > StartProcessCount)
				{
					outOfStartProcessCount = true;
				}
				Console.WriteLine("Count: {0}.", _validationResults.Count);
				Console.WriteLine("OutOfStartProcessCount: {0}.", outOfStartProcessCount);
				var logList = new List<PublicApiValidationResult>();
				var lastInfoFromError = -1;
				var lastWarningFromError = -1;
				var lastDebugFromError = -1;
				foreach (var log in _validationResults.GetConsumingEnumerable())
				{
					if (logList.Count <= LogCount)
					{
						logList.Add(log);
						continue;
					}

					if (logList.Count >= Capacity)
					{
						outOfCapacity = true;

						_validationResults.CompleteAdding();
						break;
					}

					Console.WriteLine(log);
					if (!outOfStartProcessCount)
					{
						logList.Add(log);
					}
					else
					{
						if (log.MessageType == ValidationMessageType.Information)
						{
							if (lastInfoFromError != -1 && lastInfoFromError <= InfoAfterCount)
							{
								logList.Add(log);
								lastInfoFromError++;
								continue;
							}
							else
							{
								lastInfoFromError = -1;
								EnqueueBufferByCapacity(logList, log);
							}
						}
						else if (log.MessageType == ValidationMessageType.Error)
						{
							WriteBufferToList(logList);
							logList.Add(log);

							lastInfoFromError = 1;
							lastWarningFromError = 1;
							lastDebugFromError = 1;
						}
						else if (log.MessageType == ValidationMessageType.Warning)
						{
							// filter warning log
							if (lastWarningFromError != -1 && lastWarningFromError <= WarningAfterCount)
							{
								logList.Add(log);
								lastWarningFromError++;
								continue;
							}
							else
							{
								lastWarningFromError = -1;
								EnqueueBufferByCapacity(logList, log);
							}

						}
						else if (log.MessageType == ValidationMessageType.Debug)
						{
							if (lastDebugFromError != -1 && lastDebugFromError <= DebugAfterCount)
							{
								logList.Add(log);
								lastDebugFromError++;
								continue;
							}
							else
							{
								lastDebugFromError = -1;
								EnqueueBufferByCapacity(logList, log);
							}
						}
					}
				}

				WriteBufferToList(logList);

				this.ValidationResults = logList;
			});
		}

		private void WriteBufferToList(List<PublicApiValidationResult> logList)
		{
			foreach (var log in _buffer)
			{
				logList.Add(log);
			}
		}

		private void EnqueueBufferByCapacity(List<PublicApiValidationResult> logList, PublicApiValidationResult log)
		{
			if (log.MessageType == ValidationMessageType.Information &&
				_buffer.Count(e => e.MessageType == ValidationMessageType.Information) >= InfoBeforeCount)
			{
				var infoLog = _buffer.First(e => e.MessageType == ValidationMessageType.Information);
				_buffer.Remove(infoLog);
			}

			if (log.MessageType == ValidationMessageType.Warning &&
				_buffer.Count(e => e.MessageType == ValidationMessageType.Warning) >= WarningBeforeCount)
			{
				var warningLog = _buffer.First(e => e.MessageType == ValidationMessageType.Warning);
				_buffer.Remove(warningLog);
			}

			if (log.MessageType == ValidationMessageType.Debug &&
				_buffer.Count(e => e.MessageType == ValidationMessageType.Debug) >= DebugBeforeCount)
			{
				var debugLog = _buffer.First(e => e.MessageType == ValidationMessageType.Debug);
				_buffer.Remove(debugLog);
			}
			_buffer.Add(log);
		}

		/// <summary>
		/// 
		/// </summary>
		public void FinishAndWaitCompleted()
		{
			if (!this._validationResults.IsAddingCompleted)
			{
				this._validationResults.CompleteAdding();
			}

			processTask.Wait();

			Console.WriteLine("IsCompleted: {0}.", this._validationResults.IsCompleted);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public bool HasError()
		{
			return this.hasError;
		}

		#region Properties

		/// <summary>
		/// The validation result.
		/// </summary>
		public IEnumerable<PublicApiValidationResult> ValidationResults { get; set; }


		#endregion

		#region IPublicApiExecutionLogger members

		/// <summary>
		/// Writes warning message.
		/// </summary>
		/// <param name="message"></param>
		public void WriteWarning(string message)
		{
			WriteLog(true, ValidationMessageType.Warning, message);
		}

		/// <summary>
		/// Writes info message.
		/// </summary>
		/// <param name="message"></param>
		public void WriteInfo(string message)
		{
			WriteLog(true, ValidationMessageType.Information, message);
		}

		/// <summary>
		/// Writes error message.
		/// </summary>
		/// <param name="message"></param>
		public void WriteError(string message)
		{
			WriteLog(false, ValidationMessageType.Error, message);
		}

		/// <summary>
		/// Writes debug message.
		/// </summary>
		/// <param name="message"></param>
		public void WriteDebug(string message)
		{
			WriteLog(true, ValidationMessageType.Debug, message);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="ex"></param>
		public void WriteException(Exception ex)
		{
			WriteLog(false, ValidationMessageType.Error, ex.Message + ex.StackTrace);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="isValid"></param>
		/// <param name="messageType"></param>
		/// <param name="message"></param>
		public void WriteLog(bool isValid, ValidationMessageType messageType, string message)
		{
			if (processTask == null)
			{
				throw new Exception("Please call StartWrite first, and then call WriteLog, finally call FinishAndWaitCompleted. ");
			}

			if (outOfCapacity)
			{
				return;
			}

			_validationResults.Add(new PublicApiValidationResult() { IsValid = isValid, MessageType = messageType, ErrorContent = message });

			if (!isValid)
			{
				hasError = true;
			}
		}

		#endregion

	}
}
