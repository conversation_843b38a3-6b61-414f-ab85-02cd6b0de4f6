﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Basics.AccountingJournals.BusinessComponents;

namespace RIB.Visual.Basics.AccountingJournals.ServiceFacade.WebApi
{


    /// <summary>
    /// There are no comments for CompanyTransHeaderStatusHistoryEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("BAS_COMPANYTRNHDSTATHSTY")]
    public partial class CompanyTransHeaderStatusHistoryDto : RIB.Visual.Platform.Core.ITypedDto<CompanyTransHeaderStatusHistoryEntity>
    {
        #region Constructors

        /// <summary>
        /// Initializes an instance of class CompanyTransHeaderStatusHistoryDto.
        /// </summary>
        public CompanyTransHeaderStatusHistoryDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class CompanyTransHeaderStatusHistoryDto.
        /// </summary>
        /// <param name="entity">the instance of class CompanyTransHeaderStatusHistoryEntity</param>
        public CompanyTransHeaderStatusHistoryDto(CompanyTransHeaderStatusHistoryEntity entity)
        {
            Id = entity.Id;
            CompanyTransheaderFk = entity.CompanyTransheaderFk;
            BasCompanytranshdrstatOldFk = entity.BasCompanytranshdrstatOldFk;
            BasCompanytranshdrstatNewFk = entity.BasCompanytranshdrstatNewFk;
            Remark = entity.Remark;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;

            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyTransheaderFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_TRANSHEADER_FK", TypeName = "int", Order = 1)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyTransheaderFk { get; set; }
    
        /// <summary>
        /// There are no comments for BasCompanytranshdrstatOldFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANYTRANSHDRSTAT_OLD_FK", TypeName = "int", Order = 2)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int BasCompanytranshdrstatOldFk { get; set; }
    
        /// <summary>
        /// There are no comments for BasCompanytranshdrstatNewFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANYTRANSHDRSTAT_NEW_FK", TypeName = "int", Order = 3)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int BasCompanytranshdrstatNewFk { get; set; }
    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string Remark { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 5)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime InsertedAt { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 6)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 7)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }
    
        /// <summary>
        /// There are no comments for Version in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 9)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(CompanyTransHeaderStatusHistoryEntity); }
        }

        /// <summary>
        /// Copy the current CompanyTransHeaderStatusHistoryDto instance to a new CompanyTransHeaderStatusHistoryEntity instance.
        /// </summary>
        /// <returns>a new instance of class CompanyTransHeaderStatusHistoryEntity</returns>
        public CompanyTransHeaderStatusHistoryEntity Copy()
        {
          var entity = new CompanyTransHeaderStatusHistoryEntity();

          entity.Id = this.Id;
          entity.CompanyTransheaderFk = this.CompanyTransheaderFk;
          entity.BasCompanytranshdrstatOldFk = this.BasCompanytranshdrstatOldFk;
          entity.BasCompanytranshdrstatNewFk = this.BasCompanytranshdrstatNewFk;
          entity.Remark = this.Remark;
          entity.InsertedAt = this.InsertedAt;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedAt = this.UpdatedAt;
          entity.UpdatedBy = this.UpdatedBy;
          entity.Version = this.Version;

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(CompanyTransHeaderStatusHistoryEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(CompanyTransHeaderStatusHistoryEntity entity);
    }

}
