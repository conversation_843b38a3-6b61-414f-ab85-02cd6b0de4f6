﻿using EntityFrameworkExtras.EF6;

namespace RIB.Visual.Basics.AuditTrail.BusinessComponents
{
    /// <summary>
    /// Represents User Defined Type for database
    /// </summary>
    [UserDefinedTableType("UDTT_AUD_CONTAINERLIST")]
    public class BasicsAuditTrailUdttContainerList
    {
        /// <summary>
        /// 
        /// </summary>
        [UserDefinedTableTypeColumn(1)]
        public string ListItem { get; set; }
    }

    /// <summary>
    /// Represents User Defined Type for database
    /// </summary>
    [UserDefinedTableType("UDTT_AUD_COLUMNLIST")]
    public class BasicsAuditTrailUdttColumnList
    {
        /// <summary>
        /// 
        /// </summary>
        [UserDefinedTableTypeColumn(1)]
        public string ListItem { get; set; }
    }
}
