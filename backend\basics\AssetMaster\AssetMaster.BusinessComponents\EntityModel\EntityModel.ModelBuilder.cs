﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON>art Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Data.Common;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.ModelConfiguration;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Linq;
using System.Runtime.Serialization;
using System.Xml.Serialization;
using RIB.Visual.Platform.Common;
using System.Data.Entity.Core.EntityClient;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Core.Objects.DataClasses;
using RIB.Visual.Basics.AssetMaster.BusinessComponents;

namespace RIB.Visual.Basics.AssetMaster.BusinessComponents
{
    /// <summary/>
    public partial class ModelBuilder
    {
		#region Constructors
		/// <summary>
		/// Initialize a new ModelBuilder object.
		/// </summary>
		public ModelBuilder()
    {
		}
		#endregion

		private static readonly object Locking = new object();
		private static DbCompiledModel _model;

		/// <summary>Creates a compiled entity model </summary>
		public static DbCompiledModel DbModel
		{
			get
			{
				if (_model == null)
				{
					lock (Locking)
					{
						if (_model != null) return _model;
            var modelBuilder = new DbModelBuilder();

            AddMappings(modelBuilder);
            AddAdditionalMappings(modelBuilder);

            modelBuilder.Conventions.Remove<StoreGeneratedIdentityKeyConvention>();

            _model = modelBuilder.Build(RIB.Visual.Platform.BusinessComponents.DbContext.CreateConnection()).Compile();
					}
				}

				return _model;
			}
		}

		// partial method to add special/additional mappings
		static partial void AddAdditionalMappings(DbModelBuilder modelBuilder);

		/// <summary>
		/// Adds the mapping for each entity of this db context.
		/// </summary>
		/// <param name="modelBuilder"></param>
		public static void AddMappings(DbModelBuilder modelBuilder)
		{

            #region AssetMasterEntity

            modelBuilder.Entity<AssetMasterEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("MDC_ASSET_MASTER");
            // Properties:
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.AssetMasterParentFk)
                    .HasColumnName(@"MDC_ASSET_MASTER_PARENT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.MdcContextFk)
                    .HasColumnName(@"MDC_CONTEXT_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.Code)
                    .HasColumnName(@"CODE")
                    .IsRequired()
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.DescriptionInfo.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.DescriptionInfo.DescriptionTr)
                    .HasColumnName(@"DESCRIPTION_TR")
                    .HasColumnType("int");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.AddressFk)
                    .HasColumnName(@"BAS_ADDRESS_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.IsLive)
                    .HasColumnName(@"ISLIVE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.AssetMasterLevel1Fk)
                    .HasColumnName(@"MDC_ASSET_MASTER_LEVEL1_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.Remark)
                    .HasColumnName(@"REMARK")
                    .HasColumnType("nvarchar(max)");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.UserDefined1)
                    .HasColumnName(@"USERDEFINED1")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.UserDefined2)
                    .HasColumnName(@"USERDEFINED2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.UserDefined3)
                    .HasColumnName(@"USERDEFINED3")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.UserDefined4)
                    .HasColumnName(@"USERDEFINED4")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.UserDefined5)
                    .HasColumnName(@"USERDEFINED5")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.AssetMasterLevel2Fk)
                    .HasColumnName(@"MDC_ASSET_MASTER_LEVEL2_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.AssetMasterLevel3Fk)
                    .HasColumnName(@"MDC_ASSET_MASTER_LEVEL3_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.AssetMasterLevel4Fk)
                    .HasColumnName(@"MDC_ASSET_MASTER_LEVEL4_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.AssetMasterLevel5Fk)
                    .HasColumnName(@"MDC_ASSET_MASTER_LEVEL5_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<AssetMasterEntity>()
                .Property(p => p.AllowAssignment)
                    .HasColumnName(@"ALLOW_ASSIGNMENT")
                    .IsRequired()
                    .HasColumnType("bit");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<AssetMasterEntity>(modelBuilder);
            // Associations:
            modelBuilder.Entity<AssetMasterEntity>()
                .HasMany(p => p.AssetMasterChildren)
                    .WithOptional(c => c.AssetMasterParent)
                .HasForeignKey(p => new { p.AssetMasterParentFk })
                    .WillCascadeOnDelete(false);
            modelBuilder.Entity<AssetMasterEntity>()
                .HasMany(p => p.DdTempIdsEntities)
                    .WithRequired(c => c.AssetMasterEntity)
                .HasForeignKey(p => new { p.Id })
                    .WillCascadeOnDelete(false);

            #endregion

            #region DdTempIdsEntity

            modelBuilder.Entity<DdTempIdsEntity>()
                .HasKey(p => new { p.Id, p.RequestId })
                .ToTable("BAS_DDTEMPIDS");
            // Properties:
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.RequestId)
                    .HasColumnName(@"REQUESTID")
                    .IsRequired()
                    .HasMaxLength(32)
                    .HasColumnType("char");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key1)
                    .HasColumnName(@"KEY1")
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key2)
                    .HasColumnName(@"KEY2")
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key3)
                    .HasColumnName(@"KEY3")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<DdTempIdsEntity>(modelBuilder);

            #endregion

            #region TranslationEntity

            modelBuilder.Entity<TranslationEntity>()
                .HasKey(p => new { p.BasLanguageFk, p.Id })
                .ToTable("BAS_TRANSLATION");
            // Properties:
            modelBuilder.Entity<TranslationEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<TranslationEntity>()
                .Property(p => p.BasLanguageFk)
                    .HasColumnName(@"BAS_LANGUAGE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<TranslationEntity>()
                .Property(p => p.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<TranslationEntity>()
                .Property(p => p.SourceInfo)
                    .HasColumnName(@"SOURCEINFO")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<TranslationEntity>()
                .Property(p => p.Maxlength)
                    .HasColumnName(@"MAXLENGTH")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<TranslationEntity>(modelBuilder);

            #endregion

            #region ComplexTypes

            modelBuilder.ComplexType<DescriptionTranslateType>();

            #endregion

            #region Disabled conventions


            #endregion

        }

    
        /// <summary>
        /// There are no comments for AssetMasterEntity in the schema.
        /// </summary>
        public DbSet<AssetMasterEntity> AssetMasterEntities { get; set; }
    
        /// <summary>
        /// There are no comments for DdTempIdsEntity in the schema.
        /// </summary>
        public DbSet<DdTempIdsEntity> DdTempIdsEntities { get; set; }
    
        /// <summary>
        /// There are no comments for TranslationEntity in the schema.
        /// </summary>
        public DbSet<TranslationEntity> TranslationEntities { get; set; }
    }
}
