﻿Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.1.32228.430
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Api", "Api", "{BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Foundation", "Foundation", "{99DF2B87-942B-456C-8065-19D82BFD4B66}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Service Layer", "Service Layer", "{ECB189F5-611B-463C-87E5-384B127E9BB4}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Service Facades", "Service Facades", "{14439C06-8163-48A3-97E8-5541F28E465A}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Business Layer", "Business Layer", "{DC8CA2C8-CB16-4927-9D35-3207505075D9}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTests", "UnitTests", "{C35FCB33-C320-4750-8E23-C28CEF8E57F2}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{ED4B0F69-FAA0-4CF5-82FD-DA161441434A}"
	ProjectSection(SolutionItems) = preProject
		.filenesting.json = .filenesting.json
		RIB.Visual.Basics.Api.vsmdi = RIB.Visual.Basics.Api.vsmdi
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.Api.Core", "Api.Core\RIB.Visual.Basics.Api.Core.csproj", "{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.Api.BusinessComponents", "Api.BusinessComponents\RIB.Visual.Basics.Api.BusinessComponents.csproj", "{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.Api.Common", "Api.Common\RIB.Visual.Basics.Api.Common.csproj", "{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.Api.UnitTests", "Api.UnitTests\RIB.Visual.Basics.Api.UnitTests.csproj", "{C611B718-AD15-42CC-A49B-597BFC67A902}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.Api.Localization", "Api.Localization\RIB.Visual.Basics.Api.Localization.csproj", "{74E96892-0A0F-4141-A99A-B13E6F2AB479}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.Api.ServiceFacade.WebApi", "Api.ServiceFacade.WebApi\RIB.Visual.Basics.Api.ServiceFacade.WebApi.csproj", "{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Client", "Client", "{29C4EF04-83AD-4745-A787-99BD562BC750}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "WpfApp", "WpfApp", "{EC73B200-62CB-441E-8DD0-8CFC533A113F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.Api.Client.WpfApp", "Api.Client.WpfApp\RIB.Visual.Basics.Api.Client.WpfApp.csproj", "{645E6869-3AEC-4C16-919F-618FADB8FC4E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SyncDataApp", "SyncDataApp", "{B12928B3-0BBC-4ABD-8FB8-7B773A375064}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.Api.Client.SyncDataApp", "Api.Client.SyncDataApp\RIB.Visual.Basics.Api.Client.SyncDataApp.csproj", "{C4A231A4-C517-4675-B39E-6EDF7136CC9B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.Api.Client.Itwo40WebApi", "Itwo40WebApi\RIB.Visual.Basics.Api.Client.Itwo40WebApi.csproj", "{49FBB6C8-DC60-4EA7-B9FA-983E5A5C95F4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ConsoleApp", "ConsoleApp", "{85D74F50-C0B2-4BFE-9B43-2744107C9312}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Sample", "Sample", "{4D213B85-D74C-444E-99FD-1972380D2A08}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample", "Itwo40WebApi.Sample\RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample.csproj", "{C260A1BB-3890-4791-9E01-BB86CD0A2C0D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
		Description = ...
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}.Release|Any CPU.Build.0 = Release|Any CPU
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}.Release|Any CPU.Build.0 = Release|Any CPU
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}.Release|Any CPU.Build.0 = Release|Any CPU
		{C611B718-AD15-42CC-A49B-597BFC67A902}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C611B718-AD15-42CC-A49B-597BFC67A902}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C611B718-AD15-42CC-A49B-597BFC67A902}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C611B718-AD15-42CC-A49B-597BFC67A902}.Release|Any CPU.Build.0 = Release|Any CPU
		{74E96892-0A0F-4141-A99A-B13E6F2AB479}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{74E96892-0A0F-4141-A99A-B13E6F2AB479}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{74E96892-0A0F-4141-A99A-B13E6F2AB479}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{74E96892-0A0F-4141-A99A-B13E6F2AB479}.Release|Any CPU.Build.0 = Release|Any CPU
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}.Release|Any CPU.Build.0 = Release|Any CPU
		{645E6869-3AEC-4C16-919F-618FADB8FC4E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{645E6869-3AEC-4C16-919F-618FADB8FC4E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{645E6869-3AEC-4C16-919F-618FADB8FC4E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{645E6869-3AEC-4C16-919F-618FADB8FC4E}.Release|Any CPU.Build.0 = Release|Any CPU
		{C4A231A4-C517-4675-B39E-6EDF7136CC9B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4A231A4-C517-4675-B39E-6EDF7136CC9B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4A231A4-C517-4675-B39E-6EDF7136CC9B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4A231A4-C517-4675-B39E-6EDF7136CC9B}.Release|Any CPU.Build.0 = Release|Any CPU
		{49FBB6C8-DC60-4EA7-B9FA-983E5A5C95F4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{49FBB6C8-DC60-4EA7-B9FA-983E5A5C95F4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{49FBB6C8-DC60-4EA7-B9FA-983E5A5C95F4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{49FBB6C8-DC60-4EA7-B9FA-983E5A5C95F4}.Release|Any CPU.Build.0 = Release|Any CPU
		{C260A1BB-3890-4791-9E01-BB86CD0A2C0D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C260A1BB-3890-4791-9E01-BB86CD0A2C0D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C260A1BB-3890-4791-9E01-BB86CD0A2C0D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C260A1BB-3890-4791-9E01-BB86CD0A2C0D}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{99DF2B87-942B-456C-8065-19D82BFD4B66} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{ECB189F5-611B-463C-87E5-384B127E9BB4} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{14439C06-8163-48A3-97E8-5541F28E465A} = {ECB189F5-611B-463C-87E5-384B127E9BB4}
		{DC8CA2C8-CB16-4927-9D35-3207505075D9} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{C35FCB33-C320-4750-8E23-C28CEF8E57F2} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF} = {99DF2B87-942B-456C-8065-19D82BFD4B66}
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B} = {DC8CA2C8-CB16-4927-9D35-3207505075D9}
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D} = {99DF2B87-942B-456C-8065-19D82BFD4B66}
		{C611B718-AD15-42CC-A49B-597BFC67A902} = {C35FCB33-C320-4750-8E23-C28CEF8E57F2}
		{74E96892-0A0F-4141-A99A-B13E6F2AB479} = {99DF2B87-942B-456C-8065-19D82BFD4B66}
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85} = {14439C06-8163-48A3-97E8-5541F28E465A}
		{29C4EF04-83AD-4745-A787-99BD562BC750} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{EC73B200-62CB-441E-8DD0-8CFC533A113F} = {29C4EF04-83AD-4745-A787-99BD562BC750}
		{645E6869-3AEC-4C16-919F-618FADB8FC4E} = {EC73B200-62CB-441E-8DD0-8CFC533A113F}
		{B12928B3-0BBC-4ABD-8FB8-7B773A375064} = {29C4EF04-83AD-4745-A787-99BD562BC750}
		{C4A231A4-C517-4675-B39E-6EDF7136CC9B} = {B12928B3-0BBC-4ABD-8FB8-7B773A375064}
		{49FBB6C8-DC60-4EA7-B9FA-983E5A5C95F4} = {85D74F50-C0B2-4BFE-9B43-2744107C9312}
		{85D74F50-C0B2-4BFE-9B43-2744107C9312} = {29C4EF04-83AD-4745-A787-99BD562BC750}
		{4D213B85-D74C-444E-99FD-1972380D2A08} = {29C4EF04-83AD-4745-A787-99BD562BC750}
		{C260A1BB-3890-4791-9E01-BB86CD0A2C0D} = {4D213B85-D74C-444E-99FD-1972380D2A08}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9C88299E-8BC0-4C16-B6F6-40FE55F20E5C}
	EndGlobalSection
	GlobalSection(SubversionScc) = preSolution
		Svn-Managed = True
		Manager = AnkhSVN - Subversion Support for Visual Studio
	EndGlobalSection
	GlobalSection(TestCaseManagementSettings) = postSolution
		CategoryFile = RIB.Visual.Basics.Api.vsmdi
	EndGlobalSection
	GlobalSection(SolutionConfigurationApis) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|ARM = Debug|ARM
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|ARM = Release|ARM
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationApis) = postSolution
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|Any CPU.Build.0 = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|ARM.ActiveCfg = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|x64.ActiveCfg = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|x86.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|ARM.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|x64.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|x86.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|ARM.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|x64.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|x86.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|x64.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|x86.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|Any CPU.Build.0 = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|ARM.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|x64.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|x86.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|ARM.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|x64.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|x86.ActiveCfg = Release|Any CPU
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}.Release|Any CPU.Build.0 = Release|Any CPU
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}.Release|ARM.ActiveCfg = Release|Any CPU
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}.Release|x64.ActiveCfg = Release|Any CPU
		{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}.Release|x86.ActiveCfg = Release|Any CPU
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}.Release|Any CPU.Build.0 = Release|Any CPU
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}.Release|ARM.ActiveCfg = Release|Any CPU
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}.Release|x64.ActiveCfg = Release|Any CPU
		{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}.Release|x86.ActiveCfg = Release|Any CPU
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}.Release|Any CPU.Build.0 = Release|Any CPU
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}.Release|ARM.ActiveCfg = Release|Any CPU
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}.Release|x64.ActiveCfg = Release|Any CPU
		{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}.Release|x86.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|Any CPU.Build.0 = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|ARM.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|x64.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|x86.ActiveCfg = Release|Any CPU
		{C611B718-AD15-42CC-A49B-597BFC67A902}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C611B718-AD15-42CC-A49B-597BFC67A902}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C611B718-AD15-42CC-A49B-597BFC67A902}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{C611B718-AD15-42CC-A49B-597BFC67A902}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C611B718-AD15-42CC-A49B-597BFC67A902}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C611B718-AD15-42CC-A49B-597BFC67A902}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C611B718-AD15-42CC-A49B-597BFC67A902}.Release|Any CPU.Build.0 = Release|Any CPU
		{C611B718-AD15-42CC-A49B-597BFC67A902}.Release|ARM.ActiveCfg = Release|Any CPU
		{C611B718-AD15-42CC-A49B-597BFC67A902}.Release|x64.ActiveCfg = Release|Any CPU
		{C611B718-AD15-42CC-A49B-597BFC67A902}.Release|x86.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|ARM.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|x64.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|x86.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|Any CPU.Build.0 = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|ARM.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|x64.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|x86.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|ARM.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|x64.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|x86.ActiveCfg = Release|Any CPU
		{74E96892-0A0F-4141-A99A-B13E6F2AB479}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{74E96892-0A0F-4141-A99A-B13E6F2AB479}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{74E96892-0A0F-4141-A99A-B13E6F2AB479}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{74E96892-0A0F-4141-A99A-B13E6F2AB479}.Debug|x64.ActiveCfg = Debug|Any CPU
		{74E96892-0A0F-4141-A99A-B13E6F2AB479}.Debug|x86.ActiveCfg = Debug|Any CPU
		{74E96892-0A0F-4141-A99A-B13E6F2AB479}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{74E96892-0A0F-4141-A99A-B13E6F2AB479}.Release|Any CPU.Build.0 = Release|Any CPU
		{74E96892-0A0F-4141-A99A-B13E6F2AB479}.Release|ARM.ActiveCfg = Release|Any CPU
		{74E96892-0A0F-4141-A99A-B13E6F2AB479}.Release|x64.ActiveCfg = Release|Any CPU
		{74E96892-0A0F-4141-A99A-B13E6F2AB479}.Release|x86.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|Any CPU.Build.0 = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|ARM.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|x64.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|x86.ActiveCfg = Release|Any CPU
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}.Debug|x86.ActiveCfg = Debug|Any CPU
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}.Release|Any CPU.Build.0 = Release|Any CPU
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}.Release|ARM.ActiveCfg = Release|Any CPU
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}.Release|x64.ActiveCfg = Release|Any CPU
		{EB72108F-A9E4-404E-ACE7-E2B2ACD50E85}.Release|x86.ActiveCfg = Release|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Release|Any CPU.Build.0 = Release|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Release|ARM.ActiveCfg = Release|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Release|x64.ActiveCfg = Release|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Release|x86.ActiveCfg = Release|Any CPU
	EndGlobalSection
	GlobalSection(DPCodeReviewSolutionGUID) = preSolution
		DPCodeReviewSolutionGUID = {00000000-0000-0000-0000-000000000000}
	EndGlobalSection
	GlobalSection(TextTemplating) = postSolution
		TextTemplating = 1
	EndGlobalSection
EndGlobal
