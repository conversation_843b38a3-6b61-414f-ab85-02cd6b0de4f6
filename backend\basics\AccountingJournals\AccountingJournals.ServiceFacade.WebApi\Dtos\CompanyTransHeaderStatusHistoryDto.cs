﻿/*
 * $Id$
 * Copyright (c) RIB Software AG
 *
 */

namespace RIB.Visual.Basics.AccountingJournals.ServiceFacade.WebApi
{
    /// <summary/>
    public partial class CompanyTransHeaderStatusHistoryDto
    {

        ///// <summary/>
        //partial void OnCopy(CompanyTransHeaderStatusHistoryEntity entity)
        //{
        //}

        ///// <summary/>
        //partial void OnConstruct(CompanyTransHeaderStatusHistoryEntity entity)
        //{
        //}


    }
}
