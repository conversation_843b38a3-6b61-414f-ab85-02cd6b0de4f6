{"format": 1, "restore": {"D:\\application\\backend\\basics\\Api\\Itwo40WebApi.Sample\\RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample.csproj": {}}, "projects": {"D:\\application\\backend\\basics\\Api\\Itwo40WebApi.Sample\\RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\application\\backend\\basics\\Api\\Itwo40WebApi.Sample\\RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample.csproj", "projectName": "RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample", "projectPath": "D:\\application\\backend\\basics\\Api\\Itwo40WebApi.Sample\\RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\application\\backend\\basics\\Api\\Itwo40WebApi.Sample\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\application\\backend\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://pkgs.dev.azure.com/ribdev/itwo40/_packaging/itwo40-nuget/nuget/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": {"target": "Package", "version": "[7.3.1, )"}, "Microsoft.IdentityModel.Logging": {"target": "Package", "version": "[7.3.1, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[7.3.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.3.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}