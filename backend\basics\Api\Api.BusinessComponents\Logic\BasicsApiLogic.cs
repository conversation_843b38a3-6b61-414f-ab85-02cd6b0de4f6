using RIB.Visual.Platform.Server.Common;
using System;
using System.Collections.Generic;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
// ReSharper disable StringLiteralTypo

namespace RIB.Visual.Basics.Api.BusinessComponents.Logic
{
	/// <summary>
	///
	/// </summary>
	public class SsoApiKeyCallbackInfo
	{
		/// <summary>
		///
		/// </summary>
		/// <param name="key"></param>
		/// <param name="cb"></param>
		/// <param name="roleid"></param>
		public SsoApiKeyCallbackInfo(string key, string cb, int roleid = 0)
		{
			Key = key;
			Callback = cb;
			RoleId = null;
			if (roleid > 0) { RoleId = roleid; }
		}
		/// <summary/>
		public string Key { get; set; }
		/// <summary/>
		public string Callback { get; set; }
		/// <summary/>
		public int? RoleId { get; set; }
	}
	/// <summary>
	///
	/// </summary>
	public class BasicsApiLogic : RVPBizComp.LogicBase
	{
		/// <summary>
		/// Provides access to the database model.
		/// </summary>
		/// <returns></returns>
		public override System.Data.Entity.Infrastructure.DbCompiledModel GetDbModel() => ModelBuilder.DbModel;

		private IList<SsoApiKeyCallbackInfo> _keyCbList;  // holds all key from web.config entry, load once while first call
		private void ReadJwt2LogonNameAppKeys()
		{
			if (_keyCbList != null) { return; }
			const string keyTemplate = "logonname2jwt:apikey{0}";
			const string cbTemplate = "logonname2jwt:apicb{0}";
			const string roleIdTemplate = "logonname2jwt:apiroleid{0}";
			var loopItems = new[] { "", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" }; // allowed key/cbh pair iterator
			_keyCbList = new List<SsoApiKeyCallbackInfo>();
			foreach (var iterator in loopItems)
			{
				var key = AppSettingsReader.ReadString(string.Format(keyTemplate, iterator));
				var callback = AppSettingsReader.ReadString(string.Format(cbTemplate, iterator));
				var roleId = AppSettingsReader.ReadInt(string.Format(roleIdTemplate, iterator));
				if (!string.IsNullOrEmpty(key) && !string.IsNullOrEmpty(callback))
				{
					_keyCbList.Add(new SsoApiKeyCallbackInfo(key, callback,roleId));
				}
			}
		}

		/// <summary>
		/// read Application Key with Callback from web.config
		/// </summary>
		/// <returns></returns>
		public string ReadJwt2LogonNameAppKey(string appKey, int? roleId)
		{
			ReadJwt2LogonNameAppKeys();
			foreach (var keyCbPair in _keyCbList)
			{
				if (!appKey.Equals(keyCbPair.Key, StringComparison.InvariantCulture))
				{
					continue;
				}
				if (keyCbPair.RoleId.HasValue)
				{
					if (roleId.HasValue && keyCbPair.RoleId.Value == roleId.Value)
					{
						return keyCbPair.Callback;
					}
				}
				else
				{
					return keyCbPair.Callback;
				}
			}
			return null;
		}
	}
}