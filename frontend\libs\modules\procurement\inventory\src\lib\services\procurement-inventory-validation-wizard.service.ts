/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable, inject } from '@angular/core';
import { UiCommonMessageBoxService, UiCommonDialogService, ICustomDialogOptions, StandardDialogButtonId } from '@libs/ui/common';
import { PlatformTranslateService } from '@libs/platform/common';
import { ValidationResult, IEntityRuntimeDataRegistry } from '@libs/platform/data-access';
import { IPrcInventoryHeaderEntity } from '../model/entities/prc-inventory-header-entity.interface';
import { IPrcInventoryEntity } from '../model/entities/prc-inventory-entity.interface';
import { ProcurementInventoryHeaderDataService } from './procurement-inventory-header-data.service';
import { ProcurementInventoryGridDataService } from './procurement-inventory-grid-data.service';
import { ProcurementInventoryValidationWizardComponent } from '../components/validation-wizard/procurement-inventory-validation-wizard.component';

/**
 * Interface for validation error information
 */
export interface IValidationError {
	entityId: number;
	entityType: 'header' | 'inventory';
	fieldName: string;
	errorMessage: string;
	translationKey?: string;
	translationParams?: any;
	entity: IPrcInventoryHeaderEntity | IPrcInventoryEntity;
}

/**
 * Interface for validation wizard configuration
 */
export interface IValidationWizardConfig {
	title: string;
	message: string;
	errors: IValidationError[];
	showEntityDetails?: boolean;
	allowContinue?: boolean;
}

/**
 * Procurement Inventory Validation Wizard Service
 * 
 * This service handles validation errors by showing them in a wizard-like dialog
 * similar to the AngularJS implementation but adapted for Angular.
 */
@Injectable({
	providedIn: 'root'
})
export class ProcurementInventoryValidationWizardService {
	private readonly messageBoxService = inject(UiCommonMessageBoxService);
	private readonly modalDialogService = inject(UiCommonDialogService);
	private readonly translationService = inject(PlatformTranslateService);

	/**
	 * Shows validation errors in a wizard dialog
	 * @param config Configuration for the validation wizard
	 * @returns Promise that resolves when the dialog is closed
	 */
	public async showValidationWizard(config: IValidationWizardConfig): Promise<boolean> {
		if (!config.errors || config.errors.length === 0) {
			return true; // No errors, validation passed
		}

		// Create dialog configuration with the validation wizard component
		const dialogConfig: ICustomDialogOptions<IValidationWizardConfig, ProcurementInventoryValidationWizardComponent> = {
			headerText: config.title || 'Validation Errors',
			width: '900px',
			maxHeight: '700px',
			resizeable: true,
			backdrop: false,
			bodyComponent: ProcurementInventoryValidationWizardComponent,
			value: config,
			buttons: [
				{
					id: StandardDialogButtonId.Ok,
					caption: { key: 'cloud.common.ok' },
					autoClose: true,
				}
			]
		};

		// Show the validation wizard dialog
		try {
			await (this.modalDialogService as any).show(dialogConfig);
		} catch (error) {
			// Fallback to simple message box if modal dialog fails
			const errorMessages = this.formatErrorsForDisplay(config.errors);
			const fullMessage = `${config.message}\n\n${errorMessages}`;
			await this.messageBoxService.showMsgBox(fullMessage, config.title, 'error');
		}

		// Return false to indicate validation failed (user needs to fix errors)
		return false;
	}

	/**
	 * Validates inventory header and shows wizard if there are errors
	 * @param headerDataService The header data service
	 * @returns Promise<boolean> indicating if validation passed
	 */
	public async validateInventoryHeaderWithWizard(
		headerDataService: ProcurementInventoryHeaderDataService
	): Promise<boolean> {
		const errors: IValidationError[] = [];
		const selectedEntity = headerDataService.getSelectedEntity();

		if (!selectedEntity) {
			await this.messageBoxService.showMsgBox(
				'No inventory header selected for validation.',
				'Validation Error',
				'error'
			);
			return false;
		}

		// Validate required fields
		this.validateHeaderRequiredFields(selectedEntity, errors);

		// Validate business rules
		this.validateHeaderBusinessRules(selectedEntity, errors);

		if (errors.length > 0) {
			const config: IValidationWizardConfig = {
				title: 'Inventory Header Validation Errors',
				message: 'Please correct the following validation errors before proceeding:',
				errors: errors,
				showEntityDetails: true,
				allowContinue: false
			};

			return await this.showValidationWizard(config);
		}

		return true;
	}

	/**
	 * Validates inventory items and shows wizard if there are errors
	 * @param gridDataService The grid data service
	 * @returns Promise<boolean> indicating if validation passed
	 */
	public async validateInventoryItemsWithWizard(
		gridDataService: ProcurementInventoryGridDataService
	): Promise<boolean> {
		const errors: IValidationError[] = [];
		const items = gridDataService.getList();

		if (!items || items.length === 0) {
			await this.messageBoxService.showMsgBox(
				'No inventory items to validate.',
				'Validation Warning',
				'warning'
			);
			return true; // No items is not an error
		}

		// Validate each item
		items.forEach(item => {
			this.validateInventoryItemRequiredFields(item, errors);
			this.validateInventoryItemBusinessRules(item, errors);
		});

		// Check for duplicate materials
		this.validateNoDuplicateMaterials(items, errors);

		if (errors.length > 0) {
			const config: IValidationWizardConfig = {
				title: 'Inventory Items Validation Errors',
				message: 'Please correct the following validation errors in inventory items:',
				errors: errors,
				showEntityDetails: true,
				allowContinue: false
			};

			return await this.showValidationWizard(config);
		}

		return true;
	}

	/**
	 * Validates both header and items together
	 * @param headerDataService The header data service
	 * @param gridDataService The grid data service
	 * @returns Promise<boolean> indicating if validation passed
	 */
	public async validateCompleteInventoryWithWizard(
		headerDataService: ProcurementInventoryHeaderDataService,
		gridDataService: ProcurementInventoryGridDataService
	): Promise<boolean> {
		const headerValid = await this.validateInventoryHeaderWithWizard(headerDataService);
		if (!headerValid) {
			return false;
		}

		const itemsValid = await this.validateInventoryItemsWithWizard(gridDataService);
		return itemsValid;
	}

	/**
	 * Demo method to show validation wizard with sample errors
	 * This can be used for testing the validation wizard functionality
	 */
	public async showDemoValidationWizard(): Promise<void> {
		const sampleErrors: IValidationError[] = [
			{
				entityId: 1,
				entityType: 'header',
				fieldName: 'Project',
				errorMessage: 'Project is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Project' },
				entity: {} as IPrcInventoryHeaderEntity
			},
			{
				entityId: 1,
				entityType: 'header',
				fieldName: 'Stock',
				errorMessage: 'Stock is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Stock' },
				entity: {} as IPrcInventoryHeaderEntity
			},
			{
				entityId: 2,
				entityType: 'inventory',
				fieldName: 'Material',
				errorMessage: 'Material is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Material' },
				entity: {} as IPrcInventoryEntity
			},
			{
				entityId: 3,
				entityType: 'inventory',
				fieldName: 'Material',
				errorMessage: 'Duplicate material found in inventory',
				translationKey: 'procurement.inventory.duplicatematerailmsg',
				entity: {} as IPrcInventoryEntity
			}
		];

		const config: IValidationWizardConfig = {
			title: 'Demo Validation Errors',
			message: 'This is a demonstration of the validation wizard. Please correct the following errors:',
			errors: sampleErrors,
			showEntityDetails: true,
			allowContinue: false
		};

		await this.showValidationWizard(config);
	}

	/**
	 * Groups validation errors by entity for better display
	 */
	private groupErrorsByEntity(errors: IValidationError[]): Map<string, IValidationError[]> {
		const grouped = new Map<string, IValidationError[]>();

		errors.forEach(error => {
			const key = `${error.entityType}_${error.entityId}`;
			if (!grouped.has(key)) {
				grouped.set(key, []);
			}
			grouped.get(key)!.push(error);
		});

		return grouped;
	}

	/**
	 * Formats errors for display in message box
	 */
	private formatErrorsForDisplay(errors: IValidationError[]): string {
		return errors.map((error, index) => {
			const entityInfo = error.entityType === 'header' ? 'Header' : `Item ${error.entityId}`;
			return `${index + 1}. ${entityInfo} - ${error.fieldName}: ${error.errorMessage}`;
		}).join('\n');
	}

	/**
	 * Validates required fields for inventory header
	 */
	private validateHeaderRequiredFields(entity: IPrcInventoryHeaderEntity, errors: IValidationError[]): void {
		if (!entity.PrjProjectFk || entity.PrjProjectFk === 0) {
			errors.push({
				entityId: entity.Id,
				entityType: 'header',
				fieldName: 'Project',
				errorMessage: 'Project is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Project' },
				entity: entity
			});
		}

		if (!entity.PrjStockFk || entity.PrjStockFk === 0) {
			errors.push({
				entityId: entity.Id,
				entityType: 'header',
				fieldName: 'Stock',
				errorMessage: 'Stock is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Stock' },
				entity: entity
			});
		}

		if (!entity.PrcStockTransactionTypeFk || entity.PrcStockTransactionTypeFk === 0) {
			errors.push({
				entityId: entity.Id,
				entityType: 'header',
				fieldName: 'Transaction Type',
				errorMessage: 'Transaction Type is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Transaction Type' },
				entity: entity
			});
		}

		if (!entity.InventoryDate) {
			errors.push({
				entityId: entity.Id,
				entityType: 'header',
				fieldName: 'Inventory Date',
				errorMessage: 'Inventory Date is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Inventory Date' },
				entity: entity
			});
		}

		if (!entity.TransactionDate) {
			errors.push({
				entityId: entity.Id,
				entityType: 'header',
				fieldName: 'Transaction Date',
				errorMessage: 'Transaction Date is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Transaction Date' },
				entity: entity
			});
		}
	}

	/**
	 * Validates business rules for inventory header
	 */
	private validateHeaderBusinessRules(entity: IPrcInventoryHeaderEntity, errors: IValidationError[]): void {
		// Add business rule validations here
		if (entity.IsPosted) {
			errors.push({
				entityId: entity.Id,
				entityType: 'header',
				fieldName: 'Posted Status',
				errorMessage: 'Cannot modify posted inventory',
				translationKey: 'procurement.inventory.error.cannotModifyPosted',
				entity: entity
			});
		}
	}

	/**
	 * Validates required fields for inventory items
	 */
	private validateInventoryItemRequiredFields(entity: IPrcInventoryEntity, errors: IValidationError[]): void {
		if (!entity.MdcMaterialFk || entity.MdcMaterialFk === 0) {
			errors.push({
				entityId: entity.Id,
				entityType: 'inventory',
				fieldName: 'Material',
				errorMessage: 'Material is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Material' },
				entity: entity
			});
		}

		if (!entity.ActualQuantity || entity.ActualQuantity <= 0) {
			errors.push({
				entityId: entity.Id,
				entityType: 'inventory',
				fieldName: 'Actual Quantity',
				errorMessage: 'Actual Quantity must be greater than 0',
				translationKey: 'procurement.inventory.error.quantityRequired',
				entity: entity
			});
		}
	}

	/**
	 * Validates business rules for inventory items
	 */
	private validateInventoryItemBusinessRules(entity: IPrcInventoryEntity, errors: IValidationError[]): void {
		// Add business rule validations here
		if (entity.Price && entity.Price < 0) {
			errors.push({
				entityId: entity.Id,
				entityType: 'inventory',
				fieldName: 'Price',
				errorMessage: 'Price cannot be negative',
				translationKey: 'procurement.inventory.error.negativePrice',
				entity: entity
			});
		}
	}

	/**
	 * Validates that there are no duplicate materials
	 */
	private validateNoDuplicateMaterials(items: IPrcInventoryEntity[], errors: IValidationError[]): void {
		const materialMap = new Map<number, IPrcInventoryEntity[]>();

		// Group items by material
		items.forEach(item => {
			if (item.MdcMaterialFk) {
				if (!materialMap.has(item.MdcMaterialFk)) {
					materialMap.set(item.MdcMaterialFk, []);
				}
				materialMap.get(item.MdcMaterialFk)!.push(item);
			}
		});

		// Check for duplicates
		materialMap.forEach((itemsWithSameMaterial, materialFk) => {
			if (itemsWithSameMaterial.length > 1) {
				itemsWithSameMaterial.forEach(item => {
					errors.push({
						entityId: item.Id,
						entityType: 'inventory',
						fieldName: 'Material',
						errorMessage: `Duplicate material (ID: ${materialFk}) found in inventory`,
						translationKey: 'procurement.inventory.duplicatematerailmsg',
						translationParams: { fieldName: 'Material' },
						entity: item
					});
				});
			}
		});
	}
}
