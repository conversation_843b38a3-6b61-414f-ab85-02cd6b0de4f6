/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable, inject } from '@angular/core';
import { UiCommonMessageBoxService, UiCommonDialogService, ICustomDialogOptions, StandardDialogButtonId } from '@libs/ui/common';


import { IPrcInventoryHeaderEntity } from '../model/entities/prc-inventory-header-entity.interface';
import { IPrcInventoryEntity } from '../model/entities/prc-inventory-entity.interface';
import { ProcurementInventoryHeaderDataService } from './procurement-inventory-header-data.service';
import { ProcurementInventoryGridDataService } from './procurement-inventory-grid-data.service';

/**
 * Interface for validation error information
 */
export interface IValidationError {
	entityId: number;
	entityType: 'header' | 'inventory';
	fieldName: string;
	errorMessage: string;
	translationKey?: string;
	translationParams?: any;
	entity: IPrcInventoryHeaderEntity | IPrcInventoryEntity;
}

/**
 * Interface for validation wizard configuration
 */
export interface IValidationWizardConfig {
	title: string;
	message: string;
	errors: IValidationError[];
	showEntityDetails?: boolean;
	allowContinue?: boolean;
}

/**
 * Procurement Inventory Validation Wizard Service
 * 
 * This service handles validation errors by showing them in a wizard-like dialog
 * similar to the AngularJS implementation but adapted for Angular.
 */
@Injectable({
	providedIn: 'root'
})
export class ProcurementInventoryValidationWizardService {
	private readonly messageBoxService = inject(UiCommonMessageBoxService);
	private readonly modalDialogService = inject(UiCommonDialogService);

	/**
	 * Formats errors for display in message box
	 */
	private formatErrorsForDisplay(errors: IValidationError[]): string {
		return errors.map((error, index) => {
			const entityInfo = error.entityType === 'header' ? 'Header' : `Item ${error.entityId}`;
			return `${index + 1}. ${entityInfo} - ${error.fieldName}: ${error.errorMessage}`;
		}).join('\n');
	}

	/**
	 * Validates required fields for inventory header
	 */
	private validateHeaderRequiredFields(entity: IPrcInventoryHeaderEntity, errors: IValidationError[]): void {
		if (!entity.PrjProjectFk || entity.PrjProjectFk === 0) {
			errors.push({
				entityId: entity.Id,
				entityType: 'header',
				fieldName: 'Project',
				errorMessage: 'Project is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Project' },
				entity: entity
			});
		}

		if (!entity.PrjStockFk || entity.PrjStockFk === 0) {
			errors.push({
				entityId: entity.Id,
				entityType: 'header',
				fieldName: 'Stock',
				errorMessage: 'Stock is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Stock' },
				entity: entity
			});
		}

		if (!entity.PrcStockTransactionTypeFk || entity.PrcStockTransactionTypeFk === 0) {
			errors.push({
				entityId: entity.Id,
				entityType: 'header',
				fieldName: 'Transaction Type',
				errorMessage: 'Transaction Type is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Transaction Type' },
				entity: entity
			});
		}

		if (!entity.InventoryDate) {
			errors.push({
				entityId: entity.Id,
				entityType: 'header',
				fieldName: 'Inventory Date',
				errorMessage: 'Inventory Date is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Inventory Date' },
				entity: entity
			});
		}

		if (!entity.TransactionDate) {
			errors.push({
				entityId: entity.Id,
				entityType: 'header',
				fieldName: 'Transaction Date',
				errorMessage: 'Transaction Date is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Transaction Date' },
				entity: entity
			});
		}
	}

	/**
	 * Validates business rules for inventory header
	 */
	private validateHeaderBusinessRules(entity: IPrcInventoryHeaderEntity, errors: IValidationError[]): void {
		// Add business rule validations here
		if (entity.IsPosted) {
			errors.push({
				entityId: entity.Id,
				entityType: 'header',
				fieldName: 'Posted Status',
				errorMessage: 'Cannot modify posted inventory',
				translationKey: 'procurement.inventory.error.cannotModifyPosted',
				entity: entity
			});
		}
	}

	/**
	 * Validates required fields for inventory items
	 */
	private validateInventoryItemRequiredFields(entity: IPrcInventoryEntity, errors: IValidationError[]): void {
		if (!entity.MdcMaterialFk || entity.MdcMaterialFk === 0) {
			errors.push({
				entityId: entity.Id,
				entityType: 'inventory',
				fieldName: 'Material',
				errorMessage: 'Material is required',
				translationKey: 'cloud.common.emptyOrNullValueErrorMessage',
				translationParams: { fieldName: 'Material' },
				entity: entity
			});
		}

		if (!entity.ActualQuantity || entity.ActualQuantity <= 0) {
			errors.push({
				entityId: entity.Id,
				entityType: 'inventory',
				fieldName: 'Actual Quantity',
				errorMessage: 'Actual Quantity must be greater than 0',
				translationKey: 'procurement.inventory.error.quantityRequired',
				entity: entity
			});
		}
	}

	/**
	 * Validates business rules for inventory items
	 */
	private validateInventoryItemBusinessRules(entity: IPrcInventoryEntity, errors: IValidationError[]): void {
		// Add business rule validations here
		if (entity.Price && entity.Price < 0) {
			errors.push({
				entityId: entity.Id,
				entityType: 'inventory',
				fieldName: 'Price',
				errorMessage: 'Price cannot be negative',
				translationKey: 'procurement.inventory.error.negativePrice',
				entity: entity
			});
		}
	}

	/**
	 * Validates that there are no duplicate materials
	 */
	private validateNoDuplicateMaterials(items: IPrcInventoryEntity[], errors: IValidationError[]): void {
		const materialMap = new Map<number, IPrcInventoryEntity[]>();

		// Group items by material
		items.forEach(item => {
			if (item.MdcMaterialFk) {
				if (!materialMap.has(item.MdcMaterialFk)) {
					materialMap.set(item.MdcMaterialFk, []);
				}
				materialMap.get(item.MdcMaterialFk)!.push(item);
			}
		});

		// Check for duplicates
		materialMap.forEach((itemsWithSameMaterial, materialFk) => {
			if (itemsWithSameMaterial.length > 1) {
				itemsWithSameMaterial.forEach(item => {
					errors.push({
						entityId: item.Id,
						entityType: 'inventory',
						fieldName: 'Material',
						errorMessage: `Duplicate material (ID: ${materialFk}) found in inventory`,
						translationKey: 'procurement.inventory.duplicatematerailmsg',
						translationParams: { fieldName: 'Material' },
						entity: item
					});
				});
			}
		});
	}
}
