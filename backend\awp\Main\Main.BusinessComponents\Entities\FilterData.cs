using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Awp.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class FilterData
	{
		/// <summary>
		/// 
		/// </summary>
		public int EstHeaderFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? PrcPackageFk{ get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? MainItemId { get; set; }

		/// <summary>
		/// prc item id
		/// </summary>
		public int? PrcItemFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? BoqHeaderId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? BoqItemId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int ProjectId {  get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string Filter { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? packageStructureResourceFilterType {  get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<Tuple<int, int, int>> LineItemResourceIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<GroupingStructureNode> GroupingStructureNodes { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<int> LineItemIds { get; set; }
	}
}
