using System;

namespace RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample
{
	/// <summary>
	/// This class holds the root url of a smaple server plus relative urls for the services
	/// 
	/// </summary>B
	public static class Constant
	{
		public const string BaseUrl1 = "https://apps.itwo40.eu/itwo40/rel510";
		public const string BaseUrl1_local = "https://rib-w1236.rib-software.com/itwo40dev/trunk";


		// url definition for web apis 
		// see itwo.40 client url, select documentation, at the end select "Web API documentation" 
		// or direct url:   <your itwo4.0 client url>/webapihelp,
		//								i.e. https://itwo40.rib-software.com/itwo40/rel2/client/webapihelp/
		public const string GetAssignedCompaniesWithRoles = "/basics/publicapi/company/1.0/getassignedcompanieswithroles";
		public const string CheckCompaniesWithRoles = "/basics/publicapi/company/1.0/checkcompany";
		public const string CheckCompaniesbyCode = "/basics/publicapi/company/1.0/checkcompanycode";
		public const string DoLogonUrl20  = "/basics/api/2.0/logon";
		public const string SystemInfo = "/basics/api/1.0/systeminfo";
	}
}