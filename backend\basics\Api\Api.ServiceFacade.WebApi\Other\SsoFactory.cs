﻿using System.Net;
using IdentityModel.Client;
using RIB.Visual.Basics.Core.Core;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// myHome Factory handles all service and methods used for doing single sign on and 
	/// authenticate a myHome user for itwo4.0
	/// 
	/// Services avialable:
	///   read parameters from web.config file and save it locally into properties
	///   myHome Encryption/Decryption methods
	///	  		used for encrypt/decrypt mobile app passwords
	///   
	///   myHome Ticket validation service 
	///   myHome Logout service 
	///   
	/// rei@2.1.18
	/// </summary>
	public static class SsoFactory
	{
		/// <summary>
		/// 
		/// </summary>
		private static TokenResponse ResultUnauthorizedSttFailed
		{
			get
			{
				return TokenResponseStaticMethods.CreateTokenResponse(HttpStatusCode.Unauthorized, "ShortTermToken validation failed.");
			}
		}




		/// <summary>
		/// 
		/// </summary>
		private static TokenResponse ResultUnauthorizedSttInvalid
		{
			get
			{
				return TokenResponseStaticMethods.CreateTokenResponse(HttpStatusCode.Unauthorized, "ShortTermToken validation failed. Token is invalid");
			}
		}


		/// <summary>
		/// 
		/// </summary>
		/// <param name="shortTermToken"></param>
		public static TokenResponse CreateAccessTokenFromShortTermToken(string shortTermToken)
		{
			if (!string.IsNullOrWhiteSpace(shortTermToken))
			{
				var idServerLogon = new IdentityServerLogon();
				return idServerLogon.CreateAccessTokenFromShortTermToken(shortTermToken);
			}
			return ResultUnauthorizedSttFailed;
		}


		/// <summary>
		/// 
		/// </summary>
		/// <param name="logonname"></param>
		/// <param name="password"></param>
		public static TokenResponse CreateShortTermToken(string logonname, string password)
		{
			if (!string.IsNullOrWhiteSpace(logonname) && !string.IsNullOrWhiteSpace(password))
			{
				var idServerLogon = new IdentityServerLogon();
				return idServerLogon.CreateShortTermToken(logonname,password);
			}
			return ResultUnauthorizedSttFailed;
		}


		private static TokenResponse CheckLogonNameandCreateToken(string logonName)
		{
			if (string.IsNullOrWhiteSpace(logonName))
			{
				return ResultUnauthorizedSttInvalid;
			}

			var userExtProviderLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IUserExtProviderLogic>();
			if (userExtProviderLogic != null)
			{
				;
				//var userId = userExtProviderLogic.ValidateIdmUser(_idpId, logonName);
				//if (userId.HasValue)
				//{
				//	var idServerLogon = new IdentityServerLogon();
				//	var accessToken = idServerLogon.SsoRequestToken(logonName, _idpId);
				//	// var accessToken2 = idServerLogon.SsoRequestTokenCertificate(logonName, _idpId);
				//	return accessToken;
				//}
			}
			return ResultUnauthorizedSttInvalid;
		}
	}

}
