﻿using System;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// 
	/// </summary>
	public class UniqueHintAttribute : Attribute
	{
		/// <summary>
		/// Represents a composite unique hint, which could have one or more in a object.
		/// The default order is 1.
		/// </summary>
		public UniqueHintAttribute(short order = 1)
		{
			Order = order;
		}

		/// <summary>
		/// Indicates the order of the hint to print
		/// </summary>
		public short Order
		{
			get;
			set;
		}
	}
}
