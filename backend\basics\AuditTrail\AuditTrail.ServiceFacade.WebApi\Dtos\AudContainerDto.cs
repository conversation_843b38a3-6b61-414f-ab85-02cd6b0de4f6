/*
 * $Id$
 * Copyright (c) RIB Software AG
 * 
 */

using RIB.Visual.Basics.AuditTrail.BusinessComponents;
namespace RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi
{
    /// <summary/>
    public partial class AudContainerDto
    {

        /// <summary/>
        partial void OnCopy(AudContainerEntity entity) 
        {
        }

        /// <summary/>
        partial void OnConstruct(AudContainerEntity entity) 
        {
			this.Checked = entity.Checked;
        }

		/// <summary>
		/// Checked
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"boolean")]
		public bool Checked { get; set; }

    }
}
