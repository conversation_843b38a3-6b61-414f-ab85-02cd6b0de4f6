/*
 * $Id$
 * Copyright (c) RIB Software AG
 * 
 */

namespace RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi
{
    /// <summary/>
    public partial class AudContainer2AudTableDto
    {

        ///// <summary/>
        //partial void OnCopy(AudContainer2AudTableEntity entity) 
        //{
        //}

        ///// <summary/>
        //partial void OnConstruct(AudContainer2AudTableEntity entity) 
        //{
        //}


    }
}
