﻿using RIB.Visual.Platform.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;

namespace RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi
{
	/// <summary>
	/// Copy of LogicEntity class in RIB.Visual.Basics.AuditTrail.BusinessComponents (only to get schema)
	/// </summary>
	public partial class LogEntityDto
	{

		/// <summary>
		/// 
		/// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [System.ComponentModel.DataAnnotations.Key]
		public int Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"datetime")]
        public DateTime DateAndTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        public string Description { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        public string UserName { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"description")]
		public string Action { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
		public int ObjectFk { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int RecordFk { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        public string Container { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        public string Column { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"description")]
		public string OldValue { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[RIB.Visual.Platform.Common.DomainName(Name = @"description")]
		public string NewValue { get; set; }

	}
}
