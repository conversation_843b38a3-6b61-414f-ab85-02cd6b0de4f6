/*
 * Copyright (c) RIB Software AG
 */

using System;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Cors;
using IdentityModel.Client;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RIB.Visual.Basics.Api.BusinessComponents;
using RIB.Visual.Basics.Api.BusinessComponents.Logic;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Platform.Server.Common;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using RIB.Visual.Services.Platform.Common;
using RIB.Visual.Services.Platform.ServiceFacade.WebApi;

// ReSharper disable StringLiteralTypo
// ReSharper disable UseStringInterpolation
// ReSharper disable IdentifierTypo

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// This is the json response from the LognName2JWT Callback
	/// </summary>
	public class CallbackResponse
	{
		/// <summary>
		/// The logonname which is searched for in the frm_user table.
		/// If found we are able to supply a token
		/// </summary>
		public string LogonName { get; set; }

		/// <summary>
		/// User specific string object, completely transparent passed thru the service calls.
		/// </summary>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public string Payload { get; set; }

		/// <summary>
		/// Error Information can be string as code or complete message
		/// </summary>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public string Errorcode { get; set; }

		/// <summary/>
		/// <returns></returns>
		/// Remark: @rei: do not put payload into the string, it might contain {...} which force the string.format to produce un error
		public override String ToString() => $"CallbackResponse >> LogonName={LogonName} Errorcode={Errorcode}";
	}

	/// <summary>
	/// This class is returned from a service call, holding error and response(payload) 
	/// </summary>
	/// <typeparam name="T"></typeparam>
	public class ServiceCallResponse<T>
	{
		/// <summary/>
		public HttpStatusCode StatusCode { get; set; }

		/// <summary>
		/// Error Information can be string as code or complete message
		/// </summary>
		public string Errorcode { get; set; }

		/// <summary/>
		public T Response { get; set; }

		/// <summary/>
		/// <returns></returns>
		public override String ToString() => $"ServiceCallResponse >> StatusCode={StatusCode} Errorcode={Errorcode}  Response={Response}";

	}

	/// <summary>
	/// Result for getjwtfromlogonname call, containing TokenResponse if generation of a token was valid.
	/// Otherwise we report error information in Errorcode.
	/// Detail if something went wrong with token validation its reported in TokenError,TokenErrorReason
	/// </summary>
	/// <code> json result looks like following
	///	{
	///		"token": {
	///			"access_token": "eyJhbGciOiJSUzI1NiI....",
	///			"expires_in": 604800,
	///			"token_type": "Bearer",
	///			"scope": "default"
	///		},
	///		"tokenError": false,
	///		"logonname": "<EMAIL>",
	///		"payload": "{'logonname':'<EMAIL>'}"
	///	}
	/// </code>
	public class JwtReponse
	{
		/// <summary/>
		public JwtReponse() { }

		/// <summary/>
		public JwtReponse(TokenResponse tokenResponse, string logonname = null, string payload = null, string errorcode = null)
		{
			TokenResponse = tokenResponse;
			Logonname = logonname;
			Payload = payload;
			Errorcode = errorcode;
		}
		[JsonIgnore]
		private TokenResponse TokenResponse { get; set; }

		/// <summary>The token with: AccessToken, type,
		/// </summary>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public String TokenRaw
		{
			get
			{
				return (TokenResponse != null) ? TokenResponse.IsError ? TokenResponse?.Raw : null : null;
			}
		}

		/// <summary>The token with: AccessToken, type,
		/// </summary>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public JObject Token
		{
			get
			{
				if (TokenResponse == null || !TokenResponse.Json.HasValue)
				{
					return null;
				}

				var jsonStr = JObject.Parse(TokenResponse.Json.GetValueOrDefault().GetRawText() ?? String.Empty);

				return jsonStr;
			}
		}

		/// <summary/>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public bool TokenError { get { return TokenResponse?.IsError ?? false; } }

		/// <summary/>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public string TokenErrorReason { get { return TokenResponse?.HttpErrorReason; } }

		/// <summary/>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public string Logonname { get; set; }

		/// <summary/>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public string Payload { get; set; }

		/// <summary/>
		[JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
		public string Errorcode { get; set; }
	}


	/// <summary>
	/// Please add a comment here and change RoutePrefix and Name of controller to your module's and submodule's name
	/// </summary>
	[RoutePrefix("basics/api")]
	[EnableCors("*", "*", "*")]
	public class BasicsApiController : ApiControllerBase<BasicsApiInquiryLogic>
	{
		/// <summary>
		/// This method is used for login into the itwo4.0. If logon is valid it returns an accesstoken for using itwo 4.0 services.
		/// 
		/// The accesstoken is only valid a certain time. 
		/// 
		/// If logon fails method throws a System.AccessViolationException
		///  
		/// </summary>
		/// <param name="username">The logon username</param>
		/// <param name="password">The password for the logon user</param>
		/// <returns></returns>
		[Route("apilogon")]
		[HttpGet]
		[Obsolete("This method will be remove within the next versions. Please use: /2.0/apilogon")]
		[AllowAnonymous]
		public string ApiLogonObsolete(string username, string password)
		{
			return ApiLogon(username, password);
		}

		/// <summary>
		/// This method is used for login into the itwo4.0. If logon is valid it returns an accesstoken for using itwo 4.0 services.
		/// The accesstoken is only valid a certain time. 
		/// If logon fails method throws a System.AccessViolationException
		/// After Login you need to get a Client context with selected company and role ids for detail see basics/company/checkcompany api 
		/// </summary>
		/// <param name="username">The logon username</param>
		/// <param name="password">The password for the logon user</param>
		/// <returns></returns>
		/// <exception cref="System.AccessViolationException"></exception>
		[Route("1.0/apilogon")]
		[Obsolete("This method will be remove within the next versions. Please use: /2.0/apilogon")]
		[HttpGet]
		[AllowAnonymous]
		public string ApiLogon(string username, string password)
		{
			var indentityServerLogin = new IdentityServerLogon();
			var accessToken = indentityServerLogin.ApiLogon(username, password);

			return accessToken;
		}

		/// <summary>
		/// This method is used for login into the itwo 4.0. If logon is valid it returns an accesstoken for using itwo 4.0 services.
		/// The accesstoken is only valid a certain time. 
		/// If logon fails method throws a System.AccessViolationException
		/// After Login you need to get a Client context with selected company and role ids for detail see basics/company/checkcompany api 
		/// </summary>
		/// <example>
		/// The sample body of the request will look like:
		///   <code>
		///   {
		///           "username": "my user name",
		///           "password": "my secret password"
		///   }
		///   </code>
		/// </example>
		/// <param name="accountData">body of request, json object containing 'username' and 'password' properties { "username": "my user name", "passord": "my secret password" }</param>
		/// <returns>returns access token or null</returns>
		/// <exception cref="System.AccessViolationException"></exception>
		[Route("2.0/logon"), HttpPost]
		[AllowAnonymous]
		[Microsoft.AspNetCore.Mvc.Produces("application/json")]
		public string Logon([FromBody] dynamic accountData)
		{
			if (accountData == null)
			{
				throw new ArgumentException("Logon: Account Parameters missing !");
			}

			var username = accountData.username.ToString();
			var password = accountData.password.ToString();
			if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
			{
				throw new ArgumentException("Logon: Username or Password mustn't be empty!");
			}

			var identityServerLogin = new IdentityServerLogon();
			var accessToken = identityServerLogin.ApiLogon(username, password);
			return accessToken;
		}

		/// <summary>
		/// This method return the systeminfo of the 
		/// </summary>
		/// <returns></returns>
		[Route("1.0/systeminfo")]
		[Route("2.0/systeminfo")]
		[HttpGet]
		[CamelCaseJsonFilter]
		public SystemInfo SystemInfo()
		{
			return ServicesPlatformController.TheSystemInfo(HttpContext);
		}

		/// <summary>
		/// Provides user properties available in current principal (user)
		/// 
		/// </summary>
		/// <returns>user's properties as anonymus instance</returns>
		[Route("1.0/userinfo")]
		[Route("2.0/userinfo")]
		[HttpGet]
		[ClientContext(optional: true)]
		public UserInfo GetUserInfo()
		{
			var principal = HttpContext.User;
			return ServicesPlatformController.TheUserInfo(principal);
		}

		/// <summary>
		/// Provides build version of current assembly 
		/// </summary>
		/// <returns>*******</returns>
		[AllowAnonymous]
		[Route("1.0/buildversion")]
		[Route("2.0/buildversion")]
		[HttpGet]
		public string GetBuildVersion()
		{
			return GetBuildVersion(typeof(BasicsApiController));
		}

		/// <summary>
		/// This method returns the list of available languages for the system
		/// </summary>
		/// <example>
		/// </example>
		/// <returns></returns>
		[AllowAnonymous]
		[Obsolete("will be deleted in upcoming releases")]
		[Route("2.0/languages")]
		[HttpGet]
		public HttpResponseMessage GetLanguages()
		{
			var env = BusinessApplication.BusinessEnvironment;
			var languageInfo = env.CompositionContainer.GetExportedValue<ILanguageInfo>();
			string result;

			env.EnterSystemContext();
			try
			{
				result = languageInfo.GetLanguageInfoV2();
			}
			finally
			{
				env.LeaveSystemContext();
			}
			return new HttpResponseMessage()
			{
				StatusCode = string.IsNullOrEmpty(result) ? HttpStatusCode.NoContent : HttpStatusCode.OK,
				Content = new StringContent(result ?? "")
				{
					Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
				}
			};
		}

		/// <summary/>
		/// <returns></returns>
		[AllowAnonymous]
		[Route("3.0/languages")]
		[HttpGet]
		public HttpResponseMessage GetLanguages3()
		{
			var env = BusinessApplication.BusinessEnvironment;
			var languageInfo = env.CompositionContainer.GetExportedValue<ILanguageInfo>();
			string result;

			env.EnterSystemContext();
			try
			{
				result = languageInfo.GetLanguageInfoV3();
			}
			finally
			{
				env.LeaveSystemContext();
			}
			return new HttpResponseMessage
			{
				StatusCode = string.IsNullOrEmpty(result) ? HttpStatusCode.NoContent : HttpStatusCode.OK,
				Content = new StringContent(result ?? String.Empty)
				{
					Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
				}
			};
		}

		/// <summary/>
		/// <returns></returns>
		[AllowAnonymous]
		[Route("4.0/languages")]
		[HttpGet]
		public HttpResponseMessage GetLanguages4()
		{
			var env = BusinessApplication.BusinessEnvironment;
			var languageInfo = env.CompositionContainer.GetExportedValue<ILanguageInfo>();
			string result;

			env.EnterSystemContext();
			try
			{
				result = languageInfo.GetLanguageInfoV4();
			}
			finally
			{
				env.LeaveSystemContext();
			}
			return new HttpResponseMessage
			{
				StatusCode = string.IsNullOrEmpty(result) ? HttpStatusCode.NoContent : HttpStatusCode.OK,
				Content = new StringContent(result ?? String.Empty)
				{
					Headers = { ContentType = new MediaTypeHeaderValue("application/json") }
				}
			};
		}

		/// <summary>
		/// This method returns the data language as cultureCode from the available data languages,
		/// you can specific the requestedLanguage, if it's found or its fallback it will be returned
		/// In case of not specific or not available requestedLanguage, "en" language is returned,
		/// which is the default Fallback language, if this language is not there null is returned.
		/// </summary>
		/// <returns>the data languageV2Entity as json
		/// {	"id": 1,
		///		"description": "English",
		///		"descriptionTr": 3,
		///		"culture": "en",
		///		"sorting": 1,
		///		"isDefault": false,
		///		"islive": true,
		///		"insertedBy": 1,
		///		"insertedAt": "2015-05-01T00:00:00",
		///		"updatedBy": 132,
		///		"updatedAt": "2018-10-04T08:37:38.183",
		///		"version": 10
		/// }
		/// </returns>
		[Route("2.0/datalanguage"), HttpGet, AllowAnonymous]
		public HttpResponseMessage GetDataLanguage(string requestedLanguage = null)
		{
			var env = BusinessApplication.BusinessEnvironment;
			var languageLogic = env.CompositionContainer.GetExportedValue<ILanguageLogic>();
			string result;
			env.EnterSystemContext();
			try
			{
				result = languageLogic.GetDataLanguageV2(requestedLanguage);
			}
			finally
			{
				env.LeaveSystemContext();
			}
			return new HttpResponseMessage
			{
				StatusCode = string.IsNullOrEmpty(result) ? HttpStatusCode.NoContent : HttpStatusCode.OK,
				Content = new StringContent(result ?? String.Empty) { Headers = { ContentType = new MediaTypeHeaderValue("application/json") } }
			};
		}

		/// <summary>
		/// Datapine SSO callback
		/// </summary>
		/// <returns></returns>
		[AllowAnonymous]
		[Route("2.0/datapinelogin")]
		[HttpGet]
		public Microsoft.AspNetCore.Mvc.RedirectResult DataPineLoginCallback([System.Web.Http.FromUri(Name = "ssoCallback")] String callback, [System.Web.Http.FromUri(Name = "ribid")] String data = null, [System.Web.Http.FromUri(Name = "ssokey")] String ssokey = null, [System.Web.Http.FromUri(Name = "dashboard")] String dashboard = null, [System.Web.Http.FromUri(Name = "platform")] String platform = "dashboard")
		{
			var loggingEnabled = AppSettingsReader.ReadBool("datapine:sso-logging");

			if (loggingEnabled)
			{
				Logger.Write(Logger.Category.ServiceLayer, Logger.Severity.Information, "datapine-sso | ssoCallback={0} | ribid={1} | ssokey={2} | platform={3} | dashboard={4}", callback, data, ssokey, platform, dashboard);
			}

			if (data == null)
			{
				// ReSharper disable once NotResolvedInText LocalizableElement
				throw new ArgumentNullException("data", "URL parameter ribid must be provided by callback");
			}

			var token = CompositionContainerHelper.GetExportedValue<IDatapineTokenLogic>().Load(data);
			var uri = string.Format(@"{0}?token={1}&ssokey={2}&dashboard={3}&platform={4}", callback, token, ssokey ?? "itwo40", dashboard, platform ?? "dashboard");
			var client = new HttpClient();
			var result = client.GetAsync(uri).Result;

			if (loggingEnabled)
			{
				Logger.Write(Logger.Category.ServiceLayer, Logger.Severity.Information, "datapine-sso | token:{0}", token);
			}

			return Redirect(result.RequestMessage?.RequestUri?.ToString() ?? String.Empty);
		}

		/// <summary>
		/// This method is used for getting a JWT Token from an LogonName
		/// Requirement:  Call must supply a valid application key. With the application, there will be called a
		/// Callback function, Key and Callback function must be configured in iTWO4.0 (normally web.config)
		/// The Caller can optionally send a payload, containing his context for identifying the context when his callback
		/// is triggered.
		/// The result of the callback is the logon name. The logon name must be found in the user list, if all the condition fit,
		/// we establishing a jwt token.
		/// </summary>
		/// <returns></returns>
		[Route("2.0/getjwtfromlogonname"), HttpPost]
		[Permission("8b97cfc9acf3456ea4fb29051e0e267a", Permissions.Create)]
		[CamelCaseJsonFilter]
		public async Task<JwtReponse> GetJwtFromLogonName([System.Web.Http.FromUri] string appkey, [FromBody] JToken payLoad, [System.Web.Http.FromUri] int? roleid = null)
		{
			if (string.IsNullOrWhiteSpace(appkey)) { throw new BusinessLayerException("appkey missing"); }
			var logic = new BasicsApiLogic();
			var callback = logic.ReadJwt2LogonNameAppKey(appkey, roleid);
			if (callback == null) { throw new BusinessLayerException($"appkey: {appkey} roleid: {roleid} invalid or not found in configuration"); }
			try
			{
				var contextinfo = $"appkey={appkey} | roleid={(roleid.HasValue ? roleid.ToString() : "")} | callback={callback}";
				Logger.Write(Logger.Category.ServiceLayer, Logger.Severity.Information, $"GetJwtFromLogonName | {contextinfo} ");

				var callbackResult = await CallJwtCallbackAsync(callback, payLoad);

				if (callbackResult.StatusCode == HttpStatusCode.OK)
				{
					Logger.Write(Logger.Category.ServiceLayer, Logger.Severity.Information, $"GetJwtFromLogonName cb OK | {contextinfo} | Result={callbackResult}");
					if (callbackResult.Response?.LogonName != null)
					{
						var idsrvlogon = new IdentityServerLogon();
						var tokenResponse = idsrvlogon.Logonname2JwtRequestToken(callbackResult.Response.LogonName, roleid);

						return new JwtReponse(tokenResponse, callbackResult.Response.LogonName, callbackResult.Response.Payload);
					}
					// in case of exception 
					Logger.Write(Logger.Category.ServiceLayer, Logger.Severity.Information, $"GetJwtFromLogonName cb OK | {contextinfo} | Error=logonname missing");
					throw new BusinessLayerException("GetJwtFromLogonName() logonname missing, cannot create a token");
				}

				// in case of exception 
				Logger.Write(Logger.Category.ServiceLayer, Logger.Severity.Information, "GetJwtFromLogonName cb failed | " + contextinfo + " | ErrorCode=" + callbackResult.Errorcode);
				throw new BusinessLayerException(callbackResult.Errorcode);
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException(ex.Message);
			}
		}


		/// <summary>
		/// dummy function for testing purpose only. if payload is a json string we try to parse it.
		/// If there is a logonname property we return this value in the response CallbackResponse
		/// 
		/// </summary>
		/// <param name="payload">string value, we recommend to encrypt it.</param>
		/// <returns></returns>
		[Route("2.0/jwtfromlogonnamecallback"), AllowAnonymous, HttpPost]
		public async Task<CallbackResponse> JwtFromLogonNameCallbackPost([FromBody] JToken payload)
		{
			var callJwtCallback = Task.Factory.StartNew(() =>
			{
				try
				{
					var logonname = payload["logonname"];
					var response = new CallbackResponse { LogonName = logonname?.Value<string>(), Payload = payload.ToString(Formatting.None), Errorcode = "ok" };
					return response;
				}
				catch (JsonReaderException ex)
				{
					var response = new CallbackResponse { LogonName = null, Errorcode = ex.Message, Payload = payload.ToString(Formatting.None) };
					return response;
				}
			});
			return await callJwtCallback;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="callback"></param>
		/// <param name="payload"></param>
		/// <returns></returns>
		private static async Task<ServiceCallResponse<CallbackResponse>> CallJwtCallbackAsync(string callback, JToken payload)
		{
			var callJwtCallback = Task.Factory.StartNew(() => CallJwtCallback(callback, payload));
			var result = await callJwtCallback;
			return result;
		}

		/// <summary>
		/// This method call the callback function defined with key,value in appsettings.json
		/// 
		/// </summary>
		private static ServiceCallResponse<CallbackResponse> CallJwtCallback(string callback, JToken payload)
		{
			ServicePointManager.SecurityProtocol |= SecurityProtocolType.Tls12;
			using var client = new HttpClient();
			try
			{
				var content = new StringContent(payload.ToString(Formatting.None), Encoding.UTF8, "application/json");

				HttpResponseMessage response = client.PostAsync(callback, content).Result;
				if (response.StatusCode == HttpStatusCode.OK)
				{
					var resp = response.Content.ReadAsStringAsync().Result;
					var res = JsonConvert.DeserializeObject<CallbackResponse>(resp);
					return new ServiceCallResponse<CallbackResponse> { StatusCode = response.StatusCode, Response = res };
				}

				var result = response.Content.ReadAsStringAsync().Result;
				return new ServiceCallResponse<CallbackResponse> { StatusCode = response.StatusCode, Errorcode = result };
			}
			catch (Exception ex)
			{
				return new ServiceCallResponse<CallbackResponse> { StatusCode = HttpStatusCode.BadRequest, Errorcode = ex.Message };
			}
		}
	}
}
