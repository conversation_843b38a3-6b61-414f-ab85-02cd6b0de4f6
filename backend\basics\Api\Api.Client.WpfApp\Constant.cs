using System;
using System.Text.Encodings.Web;

namespace RIB.Visual.Basics.Api.Client.WpfApp
{
	/// <summary>
	/// 
	/// </summary>
	public static class Constant
	{
		public const string BaseUrl1 = "https://rib-w0918.rib-software.com/itwo40dev/v1_local/";
		public const string BaseUrl2 = "https://apps-int.itwo40.eu/itwo40/daily";
		public const string BaseUrl3 = "https://apps.itwo40.eu/ITWO40/rel320";
		public const string BaseUrl4 = "https://apps.itwo40.eu/itwo40/rel430";
		public const string BaseUrl5 = "https://apps.itwo40.eu/itwo40/rel510";
		public const string BaseUrl6 = "https://apps.itwo40.eu/itwo40/rel520";
		public const string BaseUrl7 = "https://itwo40.rib-software.com/itwo40/rel3";
		public const string BaseUrl8 = "http://localhost/cloud/v1/";
		public const string BaseUrl9 = "https://rib-cn-vm02.mtwocloud.com/itwo40dev/daily";
		//public const string BaseUrl8 = "https://localhost/trunk";

		public const string IdentityServerUrl1 = "https://itwo40-int.rib-software.com/itwo40/identityserver/core/connect/token";
		public const string IdentityServerUrl2 = "https://apps-int.itwo40.eu/itwo40/daily/identityservercore/core/connect/token";
		public const string IdentityServerUrl3 = "https://apps.itwo40.eu/itwo40/rel320/identityservercore/core/connect/token";
		public const string IdentityServerUrl4 = "https://apps.itwo40.eu/itwo40/rel430/identityservercore/core/connect/token";
		public const string IdentityServerUrl5 = "https://apps.itwo40.eu/itwo40/rel510/identityservercore/core/connect/token";
		public const string IdentityServerUrl6 = "https://apps.itwo40.eu/itwo40/rel520/identityservercore/core/connect/token";
		public const string IdentityServerUrl7 = "https://itwo40.rib-software.com/itwo40/rel3/identityserver/core/connect/token";
		public const string IdentityServerUrl8 = "https://rib-w0918.rib-software.com/itwo40dev/v1_local/core/connect/token";
		public const string IdentityServerUrl9 = "https://rib-cn-vm02.mtwocloud.com/itwo40dev/daily/identityservercore/core/connect/token";
		//public const string IdentityServerUrl8 = "https://localhost/identityservercore/core/";

		public const string OperationInquiryForBusinesspartner = "inquiryForBusinespartner";
		public const string OperationInquiryForInvoice = "inquiryForInvoice";
		public const string OperationInquiryForPackage = "inquiryForPackage";
		public const string OperationInquiryForContract = "inquiryForContract";
		public const string OperationInquiryForEstimate = "inquiryForEstimate";
		public const string OperationInquiry = "inquiry";

		public const string OperationLookup = "lookup";
		public const string OperationLookupForInvoice = "lookupForInvoice";
		public const string OperationLookupForBusinesspartner = "lookupForBusinesspartner";
		public const string OperationLookupForPackage = "lookupForPackage";
		public const string OperationLookupForContract = "lookupForContract";

		public const string OperationLookupMaterial = "Materiallookup";
		public const string OperationLookupInvoiceDocumentsMain = "InvoiceDocumentsMain";
		public const string OperationLookupBillingDocumentsMain = "BillingDocumentsMain";
		public const string OperationLookupForBpFromBaseline= "lookupForBpFromBaseline";

		public const string OperationRelUrlInquiry = "/businesspartner/publicapi/getInquiryAddresses";
		public const string OperationRelUrlLookup = "/businesspartner/publicapi/getInquiryAddresses";
		public const string OperationRelUrlMaterial = "/basics/publicapi/materiallookup/getInquiryMaterial";
		public const string OperationRelUrlDocumentProject = "/basics/publicapi/materiallookup/getInquiryMaterial";

		//public const string ClientUrl = BaseUrl + "Clients";
		//public const string ClientUrl ="https://rib-s-itwocld5d.rib-software.com/Cloud5D/Daily/client";

		//public const string AppServicesUrl = BaseUrl + "services";
		// public const string AppServicesUrl = "https://rib-w0635.rib-software.com/Cloud5D/v1/services";
		// public  string AppServicesUrl = "https://rib-s-itwocld5d.rib-software.com/Cloud5D/Daily/services";

		#region Inquiry
		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		private static string MakeInquiryRequestForBusinesspartner(Model theModel)
		{
			// {0} requestId
			// {1} 100.11
			// {2} &id=4124"
			const string requestTemplate =
			@"{0}/#/api?navigate&operation={4}&module=businesspartner.main&requestid={1}&company={2}{3}";
			//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.invoice&requestid={1}&company={2}{3}";
			//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.package&requestid={1}&company={2}{3}";
			//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.contract&requestid={1}&company={2}{3}";
			//@"{0}/#/api?navigate&operation={4}&selection=multiple&module=estimate.main&requestid={1}&company={2}{3}";
			theModel.RequestId = Guid.NewGuid();
			var urlPart = string.Format(requestTemplate,
				theModel.BaseUrl+ "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&search=" + theModel.SearchPattern,
				//string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&roleid=" + theModel.SearchPattern,
				"inquiry"
				);
			GetSelection(theModel, ref urlPart);
			return urlPart;
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		private static string MakeInquiryRequestForInvoice(Model theModel)
		{
			// {0} requestId
			// {1} 100.11
			// {2} &id=4124"
			const string requestTemplate =
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=businesspartner.main&requestid={1}&company={2}{3}";
				@"{0}/#/api?navigate&operation={4}&module=procurement.invoice&requestid={1}&company={2}{3}";
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.package&requestid={1}&company={2}{3}";
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.contract&requestid={1}&company={2}{3}";
			//@"{0}/#/api?navigate&operation={4}&selection=multiple&module=estimate.main&requestid={1}&company={2}{3}";
			theModel.RequestId = Guid.NewGuid();
			var urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&search=" + theModel.SearchPattern,
				//string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&roleid=" + theModel.SearchPattern,
				"inquiry"
				);

			GetSelection(theModel, ref urlPart);
			return urlPart;
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		private static string MakeInquiryRequestForPackage(Model theModel)
		{
			// {0} requestId
			// {1} 100.11
			// {2} &id=4124"
			const string requestTemplate =
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=businesspartner.main&requestid={1}&company={2}{3}";
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.invoice&requestid={1}&company={2}{3}";
				@"{0}/#/api?navigate&operation={4}&module=procurement.package&requestid={1}&company={2}{3}";
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.contract&requestid={1}&company={2}{3}";
			//@"{0}/#/api?navigate&operation={4}&selection=multiple&module=estimate.main&requestid={1}&company={2}{3}";
			theModel.RequestId = Guid.NewGuid();
			var urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&search=" + theModel.SearchPattern,
				//string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&roleid=" + theModel.SearchPattern,
				"inquiry"
				);
			GetSelection(theModel, ref urlPart);
			return urlPart;
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		private static string MakeInquiryRequestForContract(Model theModel)
		{
			// {0} requestId
			// {1} 100.11
			// {2} &id=4124"
			const string requestTemplate =
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=businesspartner.main&requestid={1}&company={2}{3}";
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.invoice&requestid={1}&company={2}{3}";
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.package&requestid={1}&company={2}{3}";
				@"{0}/#/api?navigate&operation={4}&module=procurement.contract&requestid={1}&company={2}{3}";
			//@"{0}/#/api?navigate&operation={4}&selection=multiple&module=estimate.main&requestid={1}&company={2}{3}";
			theModel.RequestId = Guid.NewGuid();
			var urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&search=" + theModel.SearchPattern,
				//string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&roleid=" + theModel.SearchPattern,
				"inquiry"
				);
			GetSelection(theModel, ref urlPart);
			return urlPart;
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		private static string MakeInquiryRequestForEstimate(Model theModel)
		{
			// {0} requestId
			// {1} 100.11
			// {2} &id=4124"
			const string requestTemplate =
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=businesspartner.main&requestid={1}&company={2}{3}";
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.invoice&requestid={1}&company={2}{3}";
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.package&requestid={1}&company={2}{3}";
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.contract&requestid={1}&company={2}{3}";
			@"{0}/#/api?navigate&operation={4}&module=estimate.main&requestid={1}&company={2}{3}";
			theModel.RequestId = Guid.NewGuid();
			var urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&search=" + theModel.SearchPattern,
				//string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&roleid=" + theModel.SearchPattern,
				"inquiry"
				);
			GetSelection(theModel, ref urlPart);
			return urlPart;
		}
		#endregion

		#region Lookup
		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		private static string MakeLookupRequestForInvoice(Model theModel)
		{
			// {0} requestId
			// {1} 100.11
			// {2} &id=4124"
			const string requestTemplate =
            @"{0}/#/api?navigate&operation=lookup&module=procurement.invoice&company={2}{3}";
            //"{0}/#/api?navigate&operation=lookup&module=businesspartner.main&company={2}{3}";
            //@"{0}/#/api?navigate&operation=lookup&module=procurement.package&company={2}{3}";
            //@"{0}/#/api?navigate&operation=lookup&module=procurement.contract&company={2}{3}";
			theModel.RequestId = Guid.NewGuid();
			var urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&id=" + theModel.SearchPattern
				);

			return urlPart;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		private static string MakeLookupRequestForBusinesspartner(Model theModel)
		{
			// {0} requestId
			// {1} 100.11
			// {2} &id=4124"
			const string requestTemplate =
			//@"{0}/#/api?navigate&operation=lookup&module=procurement.invoice&company={2}{3}";
			"{0}/#/api?navigate&operation=lookup&module=businesspartner.main&company={2}{3}";
			//@"{0}/#/api?navigate&operation=lookup&module=procurement.package&company={2}{3}";
			//@"{0}/#/api?navigate&operation=lookup&module=procurement.contract&company={2}{3}";
			theModel.RequestId = Guid.NewGuid();
			var urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&id=" + theModel.SearchPattern
				);

			return urlPart;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		private static string MakeLookupRequestForPackage(Model theModel)
		{
			// {0} requestId
			// {1} 100.11
			// {2} &id=4124"
			const string requestTemplate =
				//@"{0}/#/api?navigate&operation=lookup&module=procurement.invoice&company={2}{3}";
			//"{0}/#/api?navigate&operation=lookup&module=businesspartner.main&company={2}{3}";
			@"{0}/#/api?navigate&operation=lookup&module=procurement.package&company={2}{3}";
			//@"{0}/#/api?navigate&operation=lookup&module=procurement.contract&company={2}{3}";
			theModel.RequestId = Guid.NewGuid();
			var urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&id=" + theModel.SearchPattern
				);

			return urlPart;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		private static string MakeLookupRequestForContract(Model theModel)
		{
			// {0} requestId
			// {1} 100.11
			// {2} &id=4124"
			const string requestTemplate =
				//@"{0}/#/api?navigate&operation=lookup&module=procurement.invoice&company={2}{3}";
			//"{0}/#/api?navigate&operation=lookup&module=businesspartner.main&company={2}{3}";
			//@"{0}/#/api?navigate&operation=lookup&module=procurement.package&company={2}{3}";
			@"{0}/#/api?navigate&operation=lookup&module=procurement.contract&company={2}{3}";
			theModel.RequestId = Guid.NewGuid();
			var urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&id=" + theModel.SearchPattern
				);

			return urlPart;
		}

		#endregion

		#region MaterialLookup

		private static string MakeMaterialLookupRequest(Model theModel)
		{
			const string requestTemplate =
			 @"{0}/#/api?navigate&operation=inquiry&module=basics.materiallookup&requestid={1}&company={2}{3}";

			theModel.RequestId = Guid.NewGuid();

			string urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&search=" + theModel.SearchPattern
				);

			GetSelection(theModel, ref urlPart);
			return urlPart;
		}

		#endregion

		#region Invoice DocumentProject

		private static string MakeInvoiceDocumentProjectLookupRequest(Model theModel)
		{
			const string requestTemplate =
			@"{0}/#/api?navigate&operation=lookup&module=documents.main&company={2}{3}";
			string urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&invoiceid=" + theModel.SearchPattern
				);

			return urlPart;
		}

		#endregion

		#region Billing DocumentProject

		private static string MakeBillingDocumentProjectLookupRequest(Model theModel)
		{
			const string requestTemplate =
			@"{0}/#/api?navigate&operation=lookup&module=documents.main&company={2}{3}";
			string urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&billingid=" + theModel.SearchPattern
				);

			return urlPart;
		}

		#endregion
		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		public static string MakeRequestUrl(Model theModel)
		{
			if (theModel.Operation.Equals(Constant.OperationInquiry))
				return MakeInquiryRequest(theModel);
			if (theModel.Operation.Equals(Constant.OperationLookup))
				return MakeLookupRequest(theModel);
			if (theModel.Operation.Equals(Constant.OperationInquiryForInvoice))
				return MakeInquiryRequestForInvoice(theModel);
			if (theModel.Operation.Equals(Constant.OperationInquiry))
				return MakeInquiryRequestForBusinesspartner(theModel);
			if (theModel.Operation.Equals(Constant.OperationInquiryForBusinesspartner))
				return MakeInquiryRequestForBusinesspartner(theModel);
			if (theModel.Operation.Equals(Constant.OperationInquiryForPackage))
				return MakeInquiryRequestForPackage(theModel);
			if (theModel.Operation.Equals(Constant.OperationInquiryForContract))
				return MakeInquiryRequestForContract(theModel);
			if (theModel.Operation.Equals(Constant.OperationInquiryForEstimate))
				return MakeInquiryRequestForEstimate(theModel);

			if (theModel.Operation.Equals(Constant.OperationLookupForInvoice))
				return MakeLookupRequestForInvoice(theModel);
			if (theModel.Operation.Equals(Constant.OperationLookup))
				return MakeLookupRequestForBusinesspartner(theModel);
			if (theModel.Operation.Equals(Constant.OperationLookupForBusinesspartner))
				return MakeLookupRequestForBusinesspartner(theModel);
			if (theModel.Operation.Equals(Constant.OperationLookupForPackage))
				return MakeLookupRequestForPackage(theModel);
			if (theModel.Operation.Equals(Constant.OperationLookupForContract))
				return MakeLookupRequestForContract(theModel);

			if (theModel.Operation.Equals(Constant.OperationLookupMaterial))
				return MakeMaterialLookupRequest(theModel);

			if (theModel.Operation.Equals(Constant.OperationLookupInvoiceDocumentsMain))
				return MakeInvoiceDocumentProjectLookupRequest(theModel);

			if (theModel.Operation.Equals(Constant.OperationLookupBillingDocumentsMain))
				return MakeBillingDocumentProjectLookupRequest(theModel);

			if (theModel.Operation.Equals(Constant.OperationLookupForBpFromBaseline))
				return MakeLookupForBpFromBaseline(theModel);

			return string.Empty;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		private static string MakeInquiryRequest(Model theModel)
		{
			// {0} requestId
			// {1} 100.11
			// {2} &id=4124"
			const string requestTemplate =
				@"{0}/#/api?navigate&operation={4}&module=businesspartner.main&requestid={1}&company={2}{3}";
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.invoice&requestid={1}&company={2}{3}";
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.package&requestid={1}&company={2}{3}";
				//@"{0}/#/api?navigate&operation={4}&selection=single&module=procurement.contract&requestid={1}&company={2}{3}";
			// @"{0}/#/api?navigate&operation={4}&selection=multiple&module=estimate.main&requestid={1}&company={2}{3}";
			theModel.RequestId = Guid.NewGuid();
			var urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&search=" + theModel.SearchPattern,
				//string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&roleid=" + theModel.SearchPattern,
				theModel.Operation
				);
			GetSelection(theModel, ref urlPart);
			return urlPart;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		private static string MakeLookupRequest(Model theModel)
		{
			// {0} requestId
			// {1} 100.11
			// {2} &id=4124"
			const string requestTemplate =
			@"{0}/#/api?navigate&operation=lookup&module=procurement.invoice&company={2}{3}";
			//"{0}/#/api?navigate&operation=lookup&module=businesspartner.main&company={2}{3}";
			//@"{0}/#/api?navigate&operation=lookup&module=procurement.package&company={2}{3}";
			//@"{0}/#/api?navigate&operation=lookup&module=procurement.contract&company={2}{3}";
			theModel.RequestId = Guid.NewGuid();
			var urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&id=" + theModel.SearchPattern
				);

			return urlPart;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		private static string MakeLookupForBpFromBaseline(Model theModel)
		{
			const string requestTemplate =
				@"{0}/#/api?navigate&operation=inquiry&module=businesspartner.main&requestid={1}&company={2}{3}&extparams={4}";
			theModel.RequestId = Guid.NewGuid();
			var extparam = "{\"from\":\"baseline\"}";
			var extparams=UrlEncoder.Default.Encode(extparam);

			var urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&search=" + theModel.SearchPattern, extparams
				);
			GetSelection(theModel,ref urlPart);
			return urlPart;
		}

		/// <summary>
		/// GetSelection
		/// </summary>
		/// <param name="theModel"></param>
		/// <param name="urlPart"></param>
		/// <returns></returns>
		private static void GetSelection(Model theModel,ref string urlPart)
		{
			var selection = "single";
			if (!theModel.IsSelection)
			{
				selection = "multiple";
			}
			urlPart = urlPart + "&selection=" + selection;
		}

	}
}