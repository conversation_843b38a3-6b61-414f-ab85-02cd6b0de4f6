using System;
using System.Net;
using System.Web.Http;
using IdentityModel.Client;
using RIB.Visual.Basics.Api.BusinessComponents;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using System.Collections.Generic;
using System.Linq;
using RIB.Visual.Platform.Common;
using System.Web.Http.Cors;
using System.Web.Http.Results;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.Server.Common;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// Please add a comment here and change RoutePrefix and Name of controller to your module's and submodule's name
	/// </summary>
	[RoutePrefix("basics/sso")]
	// or 
	// [RoutePrefix("module/submodule[/localname]")]
	public class BasicsSsoController : ApiControllerBase<BasicsApiInquiryLogic>
	{
		/// <summary>
		/// This method is used for signle signon into the itwo4.0.
		/// method it returns an accesstoken:
		/// check HttpResult for validate login.
		/// if logon is valid you can use the access_token itwo 4.0 services.
		///  
		/// </summary>
		/// <returns></returns>
		[Route("1.0/signon"), HttpPost]
		[ClientContext(optional: true)]
		[AllowAnonymous]
		[ExtSingleSignOn]
		public SsoTokenResponse ExternalSingleSignOn()
		{
			var httpContext = this.HttpContext;

			// ReSharper disable once NotAccessedVariable
			string accessInformation;
			if (ExtSingleSignOnAttribute.BoschIdmActivated)
			{
				accessInformation = ExtSingleSignOnAttribute.SsoValue(ExtSingleSignOnAttribute.SsoType.BoschIdm, httpContext);
			}
			else if (ExtSingleSignOnAttribute.MyHomeActivated)
			{
				// for myhome we're having different login scenarios: 
				//	1.	via the myhome-token which must be validated against myhome sso server, and resolving the user
				//  2.	mobile apps which holding an encrypted password, resulting as well int a user 

				// 1. Portal Token is used

				accessInformation = ExtSingleSignOnAttribute.SsoValue(ExtSingleSignOnAttribute.SsoType.MyHomePortalToken, httpContext);
				if (accessInformation != null)
				{
					var result = new MyHomeFactory().CreateTokenFromTicket(accessInformation);
					return CheckSsoTokenResponse(result);
				}
				else
				{
					// 2. Mobile password is used
					accessInformation = ExtSingleSignOnAttribute.SsoValue(ExtSingleSignOnAttribute.SsoType.MyHomeEncryptedPassword, httpContext);
					if (accessInformation != null)
					{
						accessInformation = System.Web.HttpUtility.UrlDecode(accessInformation);
						var result = new MyHomeFactory().CreateTokenFromEncryptedUser(accessInformation);
						return CheckSsoTokenResponse(result);
					}
				}
			}
			// rei@21.9.18 web.config setting in application  "simplefair:active" "true | false"
			else if (ExtSingleSignOnAttribute.SimpleFairActivated)
			{
				// 1. Portal Token is used
				accessInformation = ExtSingleSignOnAttribute.SsoValue(ExtSingleSignOnAttribute.SsoType.SimpleFairToken, httpContext);
				if (accessInformation != null)
				{
					var result = new SimpleFairFactory().CreateTokenFromTicket(accessInformation);
					return CheckSsoTokenResponse(result);
				}
			}
			// rei@9.11.18 added/modified for supporting Chinese national super computer center (nscc) 
			else if (ExtSingleSignOnAttribute.NsccActivated)
			{
				// 1. Portal Token is used
				accessInformation = ExtSingleSignOnAttribute.SsoValue(ExtSingleSignOnAttribute.SsoType.NsccToken, httpContext);
				if (accessInformation != null)
				{
					var result = new NsccFactory().CreateTokenFromTicket(accessInformation);
					return CheckSsoTokenResponse(result);
				}
			}
			else if (ExtSingleSignOnAttribute.TokenActivated)
			{
				accessInformation = ExtSingleSignOnAttribute.SsoValue(ExtSingleSignOnAttribute.SsoType.Token, httpContext);
				if (accessInformation != null)
				{
					if (AppSettingsReader.ReadBool("catl:active"))
					{
						TokenResponse result = new CatlFactory().CreateTokenFromTicket(accessInformation);
						return CheckSsoTokenResponse(result);
					}
				}
			}

			//else
			//{
			//	accessInformation = ExtSingleSignOnAttribute.SsoValue(ExtSingleSignOnAttribute.SsoType.ShortTermSsoToken, null);
			//	if (accessInformation != null)
			//	{
			//		var result = SsoFactory.CreateAccessTokenFromShortTermToken(accessInformation);
			//		return CheckSsoTokenResponse(result);
			//	}
			//}
			//Console.WriteLine(accessInformation);
			//var accessToken = (string)null; // sample
			//return null;

			var tokenResponse = TokenResponseStaticMethods.CreateTokenResponse(HttpStatusCode.NotFound, "Single Signon Provider not found or not activated. Please check configuration!");
			return new SsoTokenResponse(tokenResponse);
		}


		///  <summary>
		///  This method returns a short term token.
		///  Method must be called from a service and returns if the logonname/password 
		///  is valid a shorttermtoken for usage via a browser url (security reason)
		///  The browser take this short term token and create an access_token from it and enable 
		///  via this mechanism a sso from a third party application without break security rules
		///  You need a specific access right for requesting an shorttermtoken. This access right must be enabled to the technical user
		///  which you have to signon first via the signon method.
		///  Important: 
		/// 		only valid from a authenticated user account, 
		/// 		i.e. with an Access_token
		///  </summary>
		/// <example>
		///  The sample body of the request will look like:
		///   <code>
		///   { "username": "my user name", "password": "my secret password" }
		///   </code>
		/// </example>
		/// <param name="accountData">body of request, json object containing 'username' and 'password' properties { "username": "my user name", "passord": "my secret password" }</param>
		/// <returns></returns>
		[Route("2.0/ssoshorttermtoken"), HttpPost]
		[EnableCors("*", "*", "*")]
		public SsoTokenResponse SsoShortTermToken([FromBody] dynamic accountData)
		{
			if (accountData==null) 
			{
				throw new ArgumentException("SsoShortTermToken: Account Parameters missing !");
			}
			var username = accountData.username.ToString();
			var password = accountData.password.ToString();
			if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
			{
				throw new ArgumentException("SsoShortTermToken: Username or Password mustn't be empty!");
			}
			var result = SsoFactory.CreateShortTermToken(username, password);
			return new SsoTokenResponse(result);
		}

		/// <summary>
		/// This method returns an accesstoken from a valid shortterm token.
		/// the short term token must be issued via the method ssoshorttermtoken
		/// 
		/// Attention:
		/// anonymous method, because if there is only a shortmtermtoken, login is not already done
		/// </summary>
		/// <param name="shortTermToken">The shorttermtoken to be converted</param>
		/// <returns></returns>
		[Route("1.0/shorttermtokentoaccesstoken"), HttpGet]
		[EnableCors("*", "*", "*")]
		[AllowAnonymous]
		public SsoTokenResponse CreateAccessTokenfromShortTermToken(string shortTermToken)
		{
			var result = SsoFactory.CreateAccessTokenFromShortTermToken(shortTermToken);
			var res = new SsoTokenResponse(result);
			return res;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="result"></param>
		/// <returns></returns>
		private static SsoTokenResponse CheckSsoTokenResponse(TokenResponse result)
		{
			if(result != null)
			{
				return new SsoTokenResponse(result);
			}
			return new SsoTokenResponse(TokenResponseStaticMethods.CreateTokenResponse(HttpStatusCode.Forbidden, "no TokenResponse returned"));
		}

	}
}