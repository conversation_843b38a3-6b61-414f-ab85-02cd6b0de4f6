using Newtonsoft.Json;
using RIB.Visual.Basics.Core.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Expressions;
using RIB.Visual.Basics.Api.Common;
using System.Runtime.Caching;
using MemoryCache = System.Runtime.Caching.MemoryCache;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{ 
	/// <summary>
	/// 
	/// </summary>
	/// <typeparam name="TUpdateRequest"></typeparam>
	public class UpdateHandlerAdapter<TUpdateRequest>
		where TUpdateRequest : class
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="updateFn"></param>
		public UpdateHandlerAdapter(Func<TUpdateRequest, IPublicApiExecutionLogger, object> updateFn)
		{
			if (updateFn == null)
			{
				throw new ArgumentNullException("updateFn");
			}

			FireUpdate = updateFn;
		}

		/// <summary>
		/// Update json data.
		/// </summary>
		/// <param name="json"></param>
		/// <param name="logger"></param>
		/// <param name="forceModelValidate">force model validate</param>
		/// <returns></returns>
		public object ProcessUpdate(string json, IPublicApiExecutionLogger logger, bool forceModelValidate = false)
		{
			object retObj = null;
			TUpdateRequest request = null;

			try
			{
				if (!string.IsNullOrEmpty(json) && !string.IsNullOrWhiteSpace(json))
				{
					request = JsonConvert.DeserializeObject<TUpdateRequest>(json);
				}
			}
			catch (Exception ex)
			{
				logger.WriteInfo(ex.Message);
				logger.WriteDebug(string.Format("ProcessUpdate(): Failed to Deserialize [{0}]. The reason is: {1}", json, ex.Message));
				throw;
			}

			if (forceModelValidate)
			{
				var isValid = PublicApiModelValidator.Validate(request, logger);
				if (!isValid)
				{
					throw new ArgumentException("Parameter is invalid.", "request");
				}

				PublicApiModelValidator.TruncateRequestDto(request, logger, null);
			}

			retObj = FireUpdate((TUpdateRequest)request, logger);

			return null;
		}

		/// <summary>
		/// Update the data.
		/// </summary>
		/// <param name="request"></param>
		/// <param name="logger"></param>
		/// <param name="forceModelValidate">force model validate</param>
		/// <returns></returns>
		public object ProcessUpdate(object request, IPublicApiExecutionLogger logger, bool forceModelValidate = false)
		{
			object retObj = null;

			if (forceModelValidate)
			{
				var isValid = PublicApiModelValidator.Validate(request, logger);
				if (!isValid)
				{
					throw new ArgumentException("Parameter is invalid.", "request");
				}

				PublicApiModelValidator.TruncateRequestDto(request, logger, null);
			}

			retObj = FireUpdate((TUpdateRequest)request, logger);

			return retObj;
		}

		/// <summary>
		/// 
		/// </summary>
		protected Func<TUpdateRequest, IPublicApiExecutionLogger, object> FireUpdate
		{
			get;
			set;
		}
	}


	/// <summary>
	/// 
	/// </summary>
	public class PublicApiRequestHandlerBase<TDto> where TDto : class
	{
		/// <summary>
		/// Get or Set logger
		/// </summary>
		protected readonly IPublicApiExecutionLogger Logger = null;
		private bool cached = false;

		/// <summary>
		/// Initialize class
		/// </summary>
		public PublicApiRequestHandlerBase()
		{
			Logger = new PublicApiValidationLogger();
		}

		/// <summary>
		/// Initialize class
		/// </summary>
		public PublicApiRequestHandlerBase(bool cached)
			: this()
		{
			this.cached = cached;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="logger"></param>
		public PublicApiRequestHandlerBase(IPublicApiExecutionLogger logger)
		{
			Logger = logger;
		}

		/// <summary>
		/// Get list main entry, set request to global.
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public virtual IEnumerable<TDto> GetSearchList<TSearchRequest>(TSearchRequest request) 
			where TSearchRequest : PublicApiGetDataRequest
		{
			try
			{
				IEnumerable<TDto> response = null;

				if (cached == true && request.GetAll == true)
				{
					var key0 = Newtonsoft.Json.JsonConvert.SerializeObject(request);
					key0 = Md5String(key0);

					var context = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
					var newContext = new
					{
						context.ClientId,
						context.DataLanguageId,
						context.UserId,
						context.PermissionClientId,
						context.PermissionRoleId
					};
					var key1 = Newtonsoft.Json.JsonConvert.SerializeObject(newContext);
					key1 = Md5String(key1);
					var key2 = typeof(TDto).FullName;

					key2 = Md5String(key2);

					var key = string.Format("{0}_{1}_{2}", key0, key1, key2);

					var responseCache = PublicApiRequestCache.GetByKey<IEnumerable<TDto>>(key);
					if (responseCache != null)
					{
						return responseCache;
					}
				}

				if (request.GetAll)
				{
					response = GetAll();
				}
				else if (request.ParentIds != null && request.PageIndex.HasValue && request.PageSize.HasValue)
				{
					response = GetItemsByParentFksAndPaging(request.ParentIds, request.PageIndex.Value, request.PageSize.Value);
				}
				else if (request.ParentIds != null)
				{
					response = GetItemsByParentFks(request.ParentIds);
				}
				//else if (request.UnionIds != null && request.PageIndex.HasValue && request.PageSize.HasValue)
				//{
				//	response = GetItemsByUnionIdsAndPaging(request.UnionIds, request.PageIndex.Value, request.PageSize.Value);
				//}
				//else if (request.UnionIds != null)
				//{
				//	response = GetItemsByUnionIds(request.UnionIds);
				//}
				else if (request.PageIndex.HasValue && request.PageSize.HasValue)
				{
					response = GetItemsByPaging(request.PageIndex.Value, request.PageSize.Value);
				}
				else
				{
					throw new ArgumentException("The parameter is incorrect.", "request");
				}

				if (response != null && response.Count() > 0)
				{
					response = MappingDtos(response.ToList());
				}

				if (cached == true && request.GetAll == true)
				{
					var key0 = Newtonsoft.Json.JsonConvert.SerializeObject(request);
					key0 = Md5String(key0);
					var context = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
					var newContext = new
					{
						context.ClientId,
						context.DataLanguageId,
						context.UserId,
						context.PermissionClientId,
						context.PermissionRoleId
					};
					var key1 = Newtonsoft.Json.JsonConvert.SerializeObject(newContext);
					key1 = Md5String(key1);
					var key2 = typeof(TDto).FullName;

					key2 = Md5String(key2);

					var key = string.Format("{0}_{1}_{2}", key0, key1, key2);

					PublicApiRequestCache.Set(key, response, PublicApiRequestCache.DefaultExpiration);
				}

				return response;
			}
			catch (Exception ex)
			{
				Logger.WriteError(ex.Message);
				throw;
			}
		}

		private string Md5String(string str)
		{
			System.Security.Cryptography.MD5 provider = System.Security.Cryptography.MD5.Create();
			byte[] bytes = provider.ComputeHash(System.Text.Encoding.UTF8.GetBytes(str));
			StringBuilder sbResult = new StringBuilder();
			foreach (var b in bytes)
			{
				sbResult.Append(b.ToString("x2"));
			}
			return sbResult.ToString();
		}

		///// <summary>
		///// Get items by unionIds and paging
		///// </summary>
		///// <param name="enumerable"></param>
		///// <param name="pageIndex"></param>
		///// <param name="pageSize"></param>
		///// <returns></returns>
		//protected virtual IEnumerable<TResponse> GetItemsByUnionIdsAndPaging(IEnumerable<Platform.Core.IdentificationData> enumerable, int pageIndex, int pageSize)
		//{
		//	throw new ArgumentException("These parameters does not supported to be used together.", "Request.UnionIds, Request.PageIndex, Request.PageSize");
		//}

		/// <summary>
		/// Get items by fks(parent table ids) and paging.
		/// </summary>
		/// <param name="parentFks"></param>
		/// <param name="pageIndex"></param>
		/// <param name="pageSize"></param>
		/// <returns></returns>
		protected virtual IEnumerable<TDto> GetItemsByParentFksAndPaging(IEnumerable<int> parentFks, int pageIndex, int pageSize)
		{
			throw new ArgumentException("These parameters does not supported to be used together.", "Request.ParentIds, Request.PageIndex, Request.PageSize");
		}

		/// <summary>
		/// Get items by fks(parent table ids).
		/// </summary>
		/// <param name="parentFks"></param>
		/// <returns></returns>
		protected virtual IEnumerable<TDto> GetItemsByParentFks(IEnumerable<int> parentFks)
		{
			throw new ArgumentException("The parameter is not supported.", "Request.ParentIds");
		}

		///// <summary>
		///// Get items by unionIds.
		///// </summary>
		///// <param name="unionIds"></param>
		///// <returns></returns>
		//protected virtual IEnumerable<TResponse> GetItemsByUnionIds(IEnumerable<RIB.Visual.Platform.Core.IdentificationData> unionIds)
		//{
		//	throw new ArgumentException("The parameter is not supported.", "Request.UnionIds");
		//}

		/// <summary>
		/// Get items by paging.
		/// </summary>
		/// <param name="pageIndex"></param>
		/// <param name="pageSize"></param>
		/// <returns></returns>
		protected virtual IEnumerable<TDto> GetItemsByPaging(int pageIndex, int pageSize)
		{
			throw new ArgumentException("The parameter is not supported.", "Request.PageIndex, Request.PageSize");
		}

		/// <summary>
		/// Get all items.
		/// </summary>
		/// <returns></returns>
		protected virtual IEnumerable<TDto> GetAll()
		{
			throw new ArgumentException("The parameter is not supported.", "Request.GetAll");
		}

		/// <summary>
		/// MappingDtos, it's called after query and before return.
		/// </summary>
		/// <param name="dtos">source dtos.</param>
		/// <returns></returns>
		protected virtual IEnumerable<TDto> MappingDtos(IEnumerable<TDto> dtos)
		{
			return dtos;
		}

		/// <summary>
		/// Make property name expression
		/// </summary>
		/// <param name="keySelector"></param>
		/// <returns></returns>
		protected string GetPropertyName<Tkey>(Expression<Func<TDto, Tkey>> keySelector)
		{
			if (keySelector == null)
			{
				return null;
			}

			string propertyName = ((MemberExpression)keySelector.Body).Member.Name;
			return propertyName;
		}
	}


	/// <summary>
	/// 
	/// </summary>
	public static class PublicApiRequestCache
	{
		private static readonly object _lock = new Object();
		private static readonly MemoryCache _cache = new MemoryCache("PublicApiRequestCache");

		/// <summary>
		/// Item will be removed from cache after 5 mins
		/// </summary>
		public static CacheItemPolicy DefaultExpiration
		{
			get
			{
				var mins = 5;
				return new CacheItemPolicy() { AbsoluteExpiration = new DateTimeOffset(DateTime.Now.AddMinutes(mins)) };
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <typeparam name="TValue"></typeparam>
		/// <param name="key"></param>
		/// <param name="entity"></param>
		/// <param name="cacheItemPolicy"></param>
		public static void Set<TValue>(string key, TValue entity, CacheItemPolicy cacheItemPolicy) where TValue : class
		{
			lock (_lock)
			{
				_cache.Set(key, entity, cacheItemPolicy);
			}
		}


		/// <summary>
		/// 
		/// </summary>
		/// <typeparam name="TValue"></typeparam>
		/// <param name="key"></param>
		/// <returns></returns>
		public static TValue GetByKey<TValue>(string key) where TValue : class
		{
			lock (_lock)
			{
				var item = _cache.Get(key);
				if (item == null)
				{
					return null;
				}
				return (TValue)item;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <typeparam name="TValue"></typeparam>
		/// <param name="key"></param>
		/// <param name="valueFactory"></param>
		/// <param name="cacheItemPolicy"></param>
		/// <returns></returns>
		public static TValue GetOrAdd<TValue>(string key, Func<TValue> valueFactory, CacheItemPolicy cacheItemPolicy) where TValue : class
		{
			var cacheItem = _cache.Get(key);
			if (cacheItem == null)
			{
				lock (_lock)
				{
					cacheItem = _cache.Get(key);
					if (cacheItem != null)
					{
						return (TValue)cacheItem;
					}
					else
					{
						var value = valueFactory();
						_cache.Set(key, value, cacheItemPolicy);
						return value;
					}
				}
			}
			return (TValue)cacheItem;
		}
	}
}
