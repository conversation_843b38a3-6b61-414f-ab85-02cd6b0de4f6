﻿/*
 * $Id: VariableExpansionContext.cs 576980 2020-02-21 11:15:08Z haagf $
 * Copyright (c) RIB Software SE
 */

using System;
using System.Reflection;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Stores some data used during variable expansion.
	/// </summary>
	public class VariableExpansionContext
	{
		/// <summary>
		/// Initializes a new instance.
		/// </summary>
		/// <param name="type">The type.</param>
		/// <param name="method">The method.</param>
		/// <exception cref="ArgumentNullException">Any of the arguments is <see langword="null"/>.</exception>
		public VariableExpansionContext(Type type, MethodInfo method)
		{
			if (type == null)
			{
				throw new ArgumentNullException("type");
			}
			if (method == null)
			{
				throw new ArgumentNullException("method");
			}

			Type = type;
			Method = method;
		}

		/// <summary>
		/// The method.
		/// </summary>
		public MethodInfo Method { get; private set; }

		/// <summary>
		/// The type.
		/// </summary>
		public Type Type { get; private set; }
	}
}
