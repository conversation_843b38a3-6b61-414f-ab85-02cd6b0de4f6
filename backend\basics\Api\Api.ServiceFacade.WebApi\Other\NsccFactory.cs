using System;
using System.IO;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using IdentityModel.Client;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.Common;
using RIB.Visual.Services.Platform.BusinessComponents;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text;
using System.Security.Cryptography;
using System.Collections.Specialized;
using RIB.Visual.Platform.Server.Common;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Collections.Generic;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// SimpleFair Factory handles all service and methods used for doing single sign on and 
	/// authenticate a SimpleFair user for itwo4.0
	/// 
	/// Services avialable:
	///   read parameters from web.config file and save it locally into properties
	///   
	///   SimpleFair Ticket validation service 
	///   SimpleFair Logout service 
	///   
	/// </summary>
	internal class NsccFactory : TokenFactoryBase
	{
		private string _clientName;
		private string _clientSsokey;

		/// <summary>
		/// reads the configuration parameters from the web.config file of WEB-Server
		/// 
		/// <example>
		/// <code>
		///		<appSettings>
		///			<add key="nscc:sso.providerurl" value="http://www.thbic.cn/api/v1/sso/getloginuser/"/>
		///			<add key="nscc:sso.serviceurl" value="https://rib-w0918.rib-software.com/myhome/ssoserver/sso/"/>
		///			<add key="nscc:sso.clientname" value="ItwoPlatform"/>
		///			<add key="nscc:sso.clientssokey" value=""/>
		///		</appSettings>
		/// </code>
		/// </example>
		/// </summary>
		protected override void ReadConfig()
		{
			_ssoServiceUrl = AppSettingsReader.ReadString("nscc:sso.providerurl");
			_ssoServiceServiceUrl = AppSettingsReader.ReadString("nscc:sso.serviceurl");
			_idpId = AppSettingsReader.ReadInt("idm:identityproviderid");
			_clientName = AppSettingsReader.ReadString("nscc:sso.clientname");
			_clientSsokey = AppSettingsReader.ReadString("nscc:sso.clientssokey");
		}

		/// <summary>
		/// This method validate the ticket received from caller against the myHome Single Sign On (SSO) service.
		/// If ticket is valid, it returns the user name.
		/// </summary>
		/// <param name="ticket"></param>
		/// <returns></returns>
		protected override ValidateResult ValidateTicket(string ticket)
		{
			ValidateResult validateResult = new ValidateResult() { StatusCode = HttpStatusCode.Unauthorized };

			var userTicketItems = ticket.Split(':');
			if (userTicketItems.Length == 2) //ticket:projectId pair e.g. aea264ce30a2b4466b561edb0ba809c4:36
			{
				long timestamp = System.Convert.ToInt64((DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds);
				string secretKey = _clientName + _clientSsokey + userTicketItems[0] + timestamp.ToString();

				HttpClient httpClient = new HttpClient();
				httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/x-www-form-urlencoded"));
				List<KeyValuePair<string, string>> formdata = new List<KeyValuePair<string, string>>
				{
						new KeyValuePair<string, string>("ticket", userTicketItems[0]),
						new KeyValuePair<string, string>("client_name", _clientName),
						new KeyValuePair<string, string>("timestamp", timestamp.ToString()),
						new KeyValuePair<string, string>("secret_key", GetMd5Hash(secretKey)),
						new KeyValuePair<string, string>("projectid", userTicketItems[1])
				};
				HttpContent content = new FormUrlEncodedContent(formdata);

				//e.g. loginUrl = "http://www.thbic.cn/api/v1/sso/getloginuser/";
				var postResult = httpClient.PostAsync(string.Format("{0}", _ssoServiceUrl), content).Result;
				if (postResult.StatusCode == HttpStatusCode.OK)
				{
					var response = postResult.Content.ReadAsByteArrayAsync().Result;
					var retJObj = JsonConvert.DeserializeObject<JObject>(Encoding.UTF8.GetString(response));
					if (retJObj != null && string.Compare((string)retJObj["success"], "yes", StringComparison.InvariantCultureIgnoreCase) == 0)
					{
						validateResult.LogonName = (string)retJObj["userinfo"]["username"];
						validateResult.StatusCode = HttpStatusCode.OK;
					}
					else
					{
						//return sample when error: {{  "error_code": "20006",  "success": "no",  "error_desc": "fail to read cache, user is not login or logout"}}
						validateResult.ErrorCode = string.Format("{0}:{1}", retJObj["error_code"], retJObj["error_desc"]);
						validateResult.StatusCode = HttpStatusCode.Unauthorized;
					}
				}
			}

			return validateResult;
		}

		private static string GetMd5Hash(string input)
		{
			var md5Hash = MD5.Create();

			// Convert the input string to a byte array and compute the hash.
			byte[] data = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(input));

			// Create a new Stringbuilder to collect the bytes
			// and create a string.
			StringBuilder sBuilder = new StringBuilder();

			// Loop through each byte of the hashed data 
			// and format each one as a hexadecimal string.
			for (int i = 0; i < data.Length; i++)
			{
				sBuilder.Append(data[i].ToString("x2"));
			}

			// Return the hexadecimal string.
			return sBuilder.ToString();
		}
	}
}
