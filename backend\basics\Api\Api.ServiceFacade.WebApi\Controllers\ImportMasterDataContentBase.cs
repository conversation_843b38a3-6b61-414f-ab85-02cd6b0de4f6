using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using RIB.Visual.Basics.Api.Common;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Platform.Server.Common;
using System.Linq.Dynamic;
using System.Threading;
using System.Diagnostics;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public abstract class ImportMasterDataContentBase : IImportMasterDataContent
	{

		#region private fields

		IImportContentStatusProvider statusProvider = null;
		IImportContentLoggerProvider loggerProvider = null;
		IImportContentLogger _logger = null;

		private bool isSubTask = false;

		private int progressStart = 0;
		private int percentOfAll = 100;

		private const string providerName = "DataBase";

		#endregion

		#region protected fields

		/// <summary>
		/// 
		/// </summary>
		protected IPublicApiExecutionLogger Logger
		{
			get { return _logger; }
		}

		#endregion

		/// <summary>
		/// 
		/// </summary>
		public ImportMasterDataContentBase()
		{
			this.statusProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<IImportContentStatusProvider>(providerName);
			this.loggerProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<IImportContentLoggerProvider>(providerName);
			var systemOptionLogicProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<ISystemOptionLogic>();
			var enableLog2Temp = systemOptionLogicProvider.GetImportContentDiagnosisEnableLogDownloadValue();

			if (enableLog2Temp)
			{
				this._logger = new ImportContentFileLogger();
			}
			else
			{
				this._logger = new ImportContentLogger();
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="moduleName"></param>
		public ImportMasterDataContentBase(string moduleName)
			: this()
		{
			this.ModuleName = moduleName;
		}

		#region IImportMasterDataContent

		/// <summary>
		/// 
		/// </summary>
		public string ModuleName { get; set; }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="mainTask"></param>
		/// <param name="progressStart"></param>
		/// <param name="percentOfAll"></param>
		public void SetAsSubtask(IImportMasterDataContent mainTask, int progressStart = 0, int percentOfAll = 100)
		{
			if (mainTask == null)
				throw new ArgumentNullException("mainTask");
			isSubTask = true;
			this._logger = (ImportContentLogger)((ImportMasterDataContentBase)mainTask).Logger;
			this.progressStart = progressStart;
			this.percentOfAll = percentOfAll;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="context"></param>
		/// <param name="logOptions"></param>
		/// <returns></returns>
		public virtual string GetLogMessage(ImportMasterDataContext context, LogOutputInfoFlag logOptions)
		{
			return loggerProvider.GetLog(this.ModuleName, context, logOptions);
		}


		/// <summary>
		/// 
		/// </summary>
		/// <param name="context"></param>
		/// <returns></returns>
		public virtual EMasterDataImportStatus GetStatus(ImportMasterDataContext context)
		{
			return statusProvider.GetStatus(this.ModuleName, context);
		}


		/// <summary>
		/// 
		/// </summary>
		/// <param name="context"></param>
		/// <param name="status"></param>
		/// <returns></returns>
		public virtual bool SetStatus(ImportMasterDataContext context, EMasterDataImportStatus status)
		{
			return statusProvider.SaveStatus(this.ModuleName, context, status);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="context"></param>
		/// <param name="progress"></param>
		/// <returns></returns>
		public virtual bool SetProgress(ImportMasterDataContext context, int progress)
		{
			var totalProgress = (int)((decimal)progress / 100 * percentOfAll) + progressStart;
			if (totalProgress == 0 && progress > 0)
			{
				totalProgress = progress;
			}
			return statusProvider.SetProgress(this.ModuleName, context, totalProgress);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="context"></param>
		/// <returns></returns>
		public virtual bool IsChecked(ImportMasterDataContext context)
		{
			return true;
		}


		/// <summary>
		/// 
		/// </summary>
		public virtual void StartTask(ImportMasterDataContext context)
		{
			if (!IsChecked(context))
			{
				return;
			}

			this._logger.StartWrite(context);

			Stopwatch sw = new Stopwatch();
			sw.Start();
			string startMessage = null;
			if (isSubTask)
			{
				startMessage = string.Format("Subtask Start Time: {0}", DateTime.UtcNow);
			}
			else
			{
				this.Logger.WriteDebug(string.Format("Import Context: {0}", JsonConvert.SerializeObject(context.ImportSettings)));
				this.Logger.WriteDebug(GetCurrentContextInfo());

				startMessage = string.Format("Start Time: {0}", DateTime.UtcNow);
				this.Logger.WriteInfo(startMessage);
			}
			this.Logger.WriteDebug(startMessage);

			try
			{
				if (!isSubTask)
				{
					SetStatus(context, EMasterDataImportStatus.InProgress);
				}
				SetProgress(context, 1);
				PrepareTask(context);
				bool success = DoTask(context);
				SetProgress(context, 99);
				if (success && !HasError())
				{
					if (!isSubTask)
					{
						SetStatus(context, EMasterDataImportStatus.Succeed);
					}
				}
				else
				{
					SetStatus(context, EMasterDataImportStatus.Failed);
				}

				sw.Stop();
				string endMessage = null;
				string costTimeMessage = null;
				if (isSubTask)
				{
					endMessage = string.Format("Subtask End Time: {0}", DateTime.UtcNow);
					costTimeMessage = string.Format("Subtask Cost Time: {0}(ms)", sw.ElapsedMilliseconds);
				}
				else
				{
					endMessage = string.Format("End Time: {0}", DateTime.UtcNow);
					costTimeMessage = string.Format("Cost Time: {0}(ms)", sw.ElapsedMilliseconds);
					this.Logger.WriteInfo(endMessage);
					this.Logger.WriteInfo(costTimeMessage);
				}

				this.Logger.WriteDebug(endMessage);
				this.Logger.WriteDebug(costTimeMessage);
				if (!isSubTask)
				{
					SaveLog(context);
				}
				SetProgress(context, 100);
			}
			catch (Exception ex)
			{
				SetStatus(context, EMasterDataImportStatus.Failed);
				var exStackTrace = string.Empty;
				var exErrorMessage = string.Empty;
				try
				{
					exStackTrace = CollectAllStackTrace(ex);
					exErrorMessage = CollectAllError(ex);
				}
				catch (Exception)
				{
					exStackTrace = ex.Message;
					exErrorMessage = ex.Message;
				}

				this.Logger.WriteDebug(exStackTrace);
				this.Logger.WriteError(exErrorMessage);
				sw.Stop();
				string endMessage = null;
				string costTimeMessage = null;
				if (isSubTask)
				{
					endMessage = string.Format("Subtask End Time: {0}", DateTime.UtcNow);
					costTimeMessage = string.Format("Subtask Cost Time: {0}(ms)", sw.ElapsedMilliseconds);
				}
				else
				{
					endMessage = string.Format("End Time: {0}", DateTime.UtcNow);
					costTimeMessage = string.Format("Cost Time: {0}(ms)", sw.ElapsedMilliseconds);
					this.Logger.WriteInfo(endMessage);
					this.Logger.WriteInfo(costTimeMessage);
				}

				this.Logger.WriteDebug(endMessage);
				this.Logger.WriteDebug(costTimeMessage);
				if (!isSubTask)
				{
					SaveLog(context);
				}
				SetProgress(context, 100);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="context"></param>
		/// <returns></returns>
		public virtual bool Abort(ImportMasterDataContext context)
		{
			this.Logger.WriteInfo("Task was aborted.");
			SaveLog(context);

			return SetStatus(context, EMasterDataImportStatus.Abort);
		}

		#endregion


		#region protected methods

		/// <summary>
		/// 
		/// </summary>
		protected bool ImportAll { get; private set; }
		/// <summary>
		/// 
		/// </summary>
		protected IEnumerable<ContentSetting.ImportItem> SelectedImportItems { get; private set; }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="context"></param>
		protected virtual void PrepareTask(ImportMasterDataContext context)
		{
			var selections = context.ImportSettings.ItemSelections;

			if (selections != null)
			{
				var items = selections.Where(p => p.RuntimeCode == this.ModuleName && p.Level != 0).ToList();
				if (items != null && items.Any())
				{
					ImportAll = false;
					SelectedImportItems = items;
				}
				else
				{
					ImportAll = true;
				}
			}
		}

		/// <summary>
		/// Do task logic
		/// </summary>
		/// <param name="context"></param>
		/// <returns></returns>
		public abstract bool DoTask(ImportMasterDataContext context);

		private string LogInfoProcessStartTemplate = "Start to process ({0});.";
		private string LogInfoProcessCompletedTemplate = "Finish processing ({0}).";
		private string LogInfoGetDataStartTemplate = "Start to get ({0}).";
		private string LogInfoGetDataCompletedTemplate = "Finish getting ({0}).";
		private string LogInfoUpdateDataStartTemplate = "Start to put ({0}{1}).";
		private string LogInfoUpdateDataCompletedTemplate = "Finish putting ({0}{1}).";

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entityName"></param>
		protected void LogProcesStart4Info(string entityName)
		{
			Logger.WriteInfo(string.Format(this.LogInfoProcessStartTemplate, entityName));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entityName"></param>
		protected void LogProcesCompleted4Info(string entityName)
		{
			Logger.WriteInfo(string.Format(this.LogInfoProcessCompletedTemplate, entityName));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entityName"></param>
		protected void LogGetDataStart4Info(string entityName)
		{
			Logger.WriteInfo(string.Format(this.LogInfoGetDataStartTemplate, entityName));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entityName"></param>
		protected void LogGetDataCompleted4Info(string entityName)
		{
			Logger.WriteInfo(string.Format(this.LogInfoGetDataCompletedTemplate, entityName));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entityName"></param>
		/// <param name="codeOrDescription"></param>
		protected void LogUpdateDataStart4Info(string entityName, string codeOrDescription = null)
		{
			codeOrDescription = FormatCodeOrDescription(codeOrDescription);
			Logger.WriteInfo(string.Format(this.LogInfoUpdateDataStartTemplate, entityName, codeOrDescription));
		}

		private static string FormatCodeOrDescription(string codeOrDescription)
		{
			return string.IsNullOrEmpty(codeOrDescription) ? string.Empty : string.Format("[0]", codeOrDescription);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entityName"></param>
		/// <param name="codeOrDescription"></param>
		protected void LogUpdateDataCompleted4Info(string entityName, string codeOrDescription = null)
		{
			codeOrDescription = FormatCodeOrDescription(codeOrDescription);
			Logger.WriteInfo(string.Format(this.LogInfoUpdateDataCompletedTemplate, entityName, codeOrDescription));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected bool HasError()
		{
			try
			{
				return this._logger.HasError();
			}
			catch (Exception)
			{
				return false;
			}

		}

		/// <summary>
		/// 
		/// </summary>
		/// <typeparam name="TParameter"></typeparam>
		/// <param name="parameters"></param>
		/// <param name="AsyncData"></param>
		/// <param name="context"></param>
		/// <param name="multiThread"></param>
		/// <param name="threadCount">set the count of threads. if set to 0, means auto adjust.</param>
		protected void DoImportAsync<TParameter>(IEnumerable<TParameter> parameters, Action<TParameter> AsyncData, IContext context = null, bool multiThread = true, int threadCount = 0)
		{
			try
			{
				context ??= Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;

				if (multiThread)
				{
					//init parallel options , set MaxDegreeOfParallelism as current processor count/2.
					var parallelOptions = new ParallelOptions();
					var processorCount = Environment.ProcessorCount;

					Logger.WriteDebug(string.Format("ProcessorCount: {0}", processorCount));

					var count = 1;
					if (threadCount > 0 && threadCount < processorCount)
					{
						count = threadCount;
					}
					else
					{
						count = Environment.ProcessorCount > 1 ? Environment.ProcessorCount / 2 : 1;
					}

					Logger.WriteDebug(string.Format("Execute thread count: {0}", count));

					parallelOptions.MaxDegreeOfParallelism = count;
					Parallel.ForEach(parameters, parallelOptions, (parameter, loopState) =>
					{
						GetAction(() => { AsyncData(parameter); }, loopState, context).Invoke();
					});
				}
				else
				{
					foreach (var parameter in parameters)
					{
						AsyncData(parameter);
					}
				}

			}
			catch (AggregateException /* ex */)
			{
				throw;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="ex"></param>
		/// <param name="lastError"></param>
		/// <param name="exCount"></param>
		/// <returns></returns>
		protected string CollectAllError(Exception ex, string lastError = null, int exCount = 1)
		{
			const string entryFormat = "#{0}: {1} ";
			lastError = lastError ?? string.Empty;
			if (exCount > 1)
			{
				lastError += Environment.NewLine;
			}
			if (ex is ServiceLayerException)
			{
				var serviceException = ex as ServiceLayerException;
				lastError += string.Format(entryFormat, exCount, serviceException.ErrorMessage);
			}
			else if (ex is ServiceLayerException)
			{
				var serviceException = ex as ServiceLayerException;
				lastError += string.Format(entryFormat, exCount, serviceException.ErrorMessage);
			}
			else
			{
				lastError += string.Format(entryFormat, exCount, ex.Message);
			}

			if (ex.InnerException != null)
			{
				return CollectAllError(ex.InnerException, lastError, ++exCount);
			}
			return lastError;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="ex"></param>
		/// <param name="lastStackTrace"></param>
		/// <param name="exCount"></param>
		/// <returns></returns>
		protected string CollectAllStackTrace(Exception ex, string lastStackTrace = null, int exCount = 1)
		{
			const string entryFormat = "#{0}: {1}\r\n{2}";
			lastStackTrace = lastStackTrace ?? string.Empty;
			if (exCount > 1)
			{
				lastStackTrace += Environment.NewLine;
			}
			if (ex is ServiceLayerException)
			{
				var serviceException = ex as ServiceLayerException;
				lastStackTrace += string.Format(entryFormat, exCount, serviceException.ErrorMessage, ex.StackTrace);
			}
			else
			{
				lastStackTrace += string.Format(entryFormat, exCount, ex.Message, ex.StackTrace);
			}

			if (ex.InnerException != null)
			{
				return CollectAllStackTrace(ex.InnerException, lastStackTrace, ++exCount);
			}
			return lastStackTrace;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="ex"></param>
		/// <returns></returns>
		protected string GetReallyErrorMessage(Exception ex)
		{
			var rootException = GetRootException(ex);
			if (ex is ServiceLayerException)
			{
				var serviceException = ex as ServiceLayerException;
				return serviceException.ErrorMessage;
			}
			else
			{
				return ex.Message;
			}
		}

		private Exception GetRootException(Exception ex)
		{
			var header = ex;
			while (header.InnerException != null)
			{
				header = header.InnerException;
			}

			return header;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="context"></param>
		/// <returns></returns>
		protected T CreateDefaultRequest<T>(ImportMasterDataContext context)
			where T : PublicApiGetDataRequest, new()
		{
			var request = new T()
			{
				LogOptions = LogOutputInfoFlag.Error | LogOutputInfoFlag.Debug
			};
			return request;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="context"></param>
		/// <returns></returns>
		protected T CreateGetAllRequest<T>(ImportMasterDataContext context)
			where T : PublicApiGetDataRequest, new()
		{
			var request = CreateDefaultRequest<T>(context);
			request.GetAll = true;
			return request;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="context"></param>
		/// <param name="pageIndex"></param>
		/// <param name="pageSize"></param>
		/// <returns></returns>
		protected T CreatePagingRequest<T>(ImportMasterDataContext context, int pageIndex, int pageSize)
			where T : PublicApiGetDataRequest, new()
		{
			var request = CreateDefaultRequest<T>(context);
			request.PageIndex = pageIndex;
			request.PageSize = pageSize;
			return request;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="context"></param>
		/// <param name="pageSize"></param>
		/// <param name="recordCount"></param>
		/// <returns></returns>
		protected List<T> CreateTaskParameters<T>(ImportMasterDataContext context, int pageSize, int recordCount)
			where T : PublicApiGetDataRequest, new()
		{
			int prePageCount = recordCount / pageSize;
			int pageCount = recordCount % pageSize == 0 ? prePageCount : prePageCount + 1;
			var requestParameters = new List<T>();
			for (var pageIndex = 0; pageIndex < pageCount; pageIndex++)
			{
				var requestParameter = CreatePagingRequest<T>(context, pageIndex, pageSize);
				requestParameters.Add(requestParameter);
			}
			return requestParameters;
		}

		/// <summary>
		/// force execute model validation before update data.
		/// </summary>
		/// <typeparam name="TUpdateRequest"></typeparam>
		/// <param name="action"></param>
		/// <param name="request"></param>
		/// <param name="forceModelValidate"></param>
		/// <returns></returns>
		protected object SafeUpdate<TUpdateRequest>(Func<TUpdateRequest, IPublicApiExecutionLogger, object> action,
			TUpdateRequest request, bool forceModelValidate = true) where TUpdateRequest : class
		{
			var adapter = new UpdateHandlerAdapter<TUpdateRequest>(action);
			return adapter.ProcessUpdate(request, this.Logger, forceModelValidate);
		}

		/// <summary>
		/// force execute model validation before update data.
		/// </summary>
		/// <typeparam name="TUpdateRequest"></typeparam>
		/// <param name="action"></param>
		/// <param name="request"></param>
		/// <param name="forceModelValidate"></param>
		protected void SafeUpdate<TUpdateRequest>(Action<TUpdateRequest, IPublicApiExecutionLogger> action,
			TUpdateRequest request, bool forceModelValidate = true) where TUpdateRequest : class
		{
			var func = new Func<TUpdateRequest, IPublicApiExecutionLogger, object>(
				(requestParam, loggerParam) =>
				{

					action(requestParam, loggerParam);
					return null;
				}
			);

			var adapter = new UpdateHandlerAdapter<TUpdateRequest>(func);
			adapter.ProcessUpdate(request, this.Logger, forceModelValidate);
		}

		#endregion

		#region private methods

		// build action for import task.
		private Action GetAction(Action asyncData, ParallelLoopState loopState = null, IContext context = null)
		{
			context ??= Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
			Action action = () =>
			{
				//setting the context
				Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext = context;
				try
				{
					Logger.WriteDebug(string.Format("started thread id: {0}, name: {1}", Thread.CurrentThread.ManagedThreadId, Thread.CurrentThread.Name));
					asyncData();
					Logger.WriteDebug(string.Format("finished thread id: {0}, name: {1}", Thread.CurrentThread.ManagedThreadId, Thread.CurrentThread.Name));
				}
				catch (System.Exception)
				{
					if (loopState != null)
					{
						loopState.Break();
					}

					throw;
				}
			};

			return action;
		}

		// get an json format of current context info.
		private string GetCurrentContextInfo()
		{
			string format = "Context: {0}";
			var context = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
			var log = new { context.UserId, context.ClientId, context.SignedInClientId, context.DataLanguageId };
			return string.Format(format, Newtonsoft.Json.JsonConvert.SerializeObject(log));
		}

		// save all log to database.
		private bool SaveLog(ImportMasterDataContext context)
		{
			this._logger.FinishAndWaitCompleted();

			var log = JsonConvert.SerializeObject(this._logger.ValidationResults);
			return loggerProvider.SaveLog(this.ModuleName, context, log);
		}


		#endregion

	}
}
