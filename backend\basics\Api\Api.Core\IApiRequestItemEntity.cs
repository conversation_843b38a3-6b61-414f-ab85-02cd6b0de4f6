using System;

namespace RIB.Visual.Basics.Api.Core
{

	/// <summary>
	/// 
	/// </summary>
	public interface IApiRequestItemEntity
	{
		/// <summary>
		/// There are no comments for Id in the schema.
		/// </summary>
		int Id { get; set; }

		/// <summary>
		/// There are no comments for RequestId in the schema.
		/// </summary>
		string RequestId { get; set; }
		
		/// <summary>
		/// There are no comments for Module<PERSON><PERSON> in the schema.
		/// </summary>
		string ModuleName { get; set; }

		/// <summary>
		/// There are no comments for ItemData in the schema.
		/// </summary>
		string ItemData { get; set; }

		/// <summary>
		/// There are no comments for Status in the schema.
		/// </summary>
		int Status { get; set; }

		/// <summary>
		/// There are no comments for <PERSON>id<PERSON>nti<PERSON> in the schema.
		/// </summary>
		System.DateTime? ValidUntil { get; set; }

	}
}