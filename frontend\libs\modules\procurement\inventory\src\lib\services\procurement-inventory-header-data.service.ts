/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';

import {
	DataServiceFlatRoot,
	ServiceRole,
	IDataServiceOptions,
	IDataServiceEndPointOptions,
	IDataServiceRoleOptions
} from '@libs/platform/data-access';
import { MainDataDto } from '@libs/basics/shared';

import { ProcurementInventoryHeaderGridComplete } from '../model/procurement-inventory-header-grid-complete.class';
import { IPrcInventoryHeaderEntity } from '../model/entities/prc-inventory-header-entity.interface';
import { ProcurementInventoryHeaderReadonlyProcessor } from './processors/procurement-inventory-header-readonly-processor.class';

/**
 * Procurement Inventory Header Grid Data Service.
 */
@Injectable({
	providedIn: 'root'
})

export class ProcurementInventoryHeaderDataService extends DataServiceFlatRoot<IPrcInventoryHeaderEntity, ProcurementInventoryHeaderGridComplete> {
	// public readonly readonlyProcessor: ProcurementInventoryHeaderReadonlyProcessor;

	public constructor() {
		const options: IDataServiceOptions<IPrcInventoryHeaderEntity> = {
			apiUrl: 'procurement/inventory/header',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: true,
			},
			createInfo: <IDataServiceEndPointOptions>{
				endPoint: 'create',
				usePost: true,
			},
			updateInfo: <IDataServiceEndPointOptions>{
				endPoint: 'update',
				usePost: true,
			},
			deleteInfo: <IDataServiceEndPointOptions>{
				endPoint: 'delete'
			},
			roleInfo: <IDataServiceRoleOptions<IPrcInventoryHeaderEntity>>{
				role: ServiceRole.Root,
				itemName: 'InventoryHeader',
			}
		};

		super(options);

		console.log('ProcurementInventoryHeaderDataService initialized with options:', options);

		// this.readonlyProcessor = new ProcurementInventoryHeaderReadonlyProcessor(this);

		// readonly processor to entity processor
		// this.processor.addProcessor([this.readonlyProcessor]);
	}
	public override createUpdateEntity(modified: IPrcInventoryHeaderEntity | null): ProcurementInventoryHeaderGridComplete {
		console.log('ProcurementInventoryHeaderDataService.createUpdateEntity called with:', modified);

		const complete = new ProcurementInventoryHeaderGridComplete();
		if (modified !== null) {
			complete.Id = modified.Id;

			// Clean the entity before sending to backend - remove invalid foreign key values
			const cleanedEntity = this.cleanEntityForSave(modified);
			complete.InventoryHeader = cleanedEntity;

			console.log('Created complete object:', complete);
		}

		return complete;
	}

	public override getModificationsFromUpdate(complete: ProcurementInventoryHeaderGridComplete): IPrcInventoryHeaderEntity[] {
		console.log('ProcurementInventoryHeaderDataService.getModificationsFromUpdate called with:', complete);
		return complete.InventoryHeader ? [complete.InventoryHeader] : [];
	}

	/**
	 * Override save method to add debugging
	 */
	public override save(): Promise<void> {
		console.log('ProcurementInventoryHeaderDataService.save() called');
		console.log('Current selection:', this.getSelection());
		console.log('Selected entity:', this.getSelectedEntity());

		return super.save().then(() => {
			console.log('Save completed successfully');
		}).catch((error) => {
			console.error('Save failed:', error);
			throw error;
		});
	}

	/**
	 * Cleans the entity by removing invalid foreign key values that would cause constraint violations
	 */
	private cleanEntityForSave(entity: IPrcInventoryHeaderEntity): IPrcInventoryHeaderEntity {
		const cleaned = { ...entity };

		// Remove foreign key fields that are 0, undefined, or null - let the backend handle defaults
		if (cleaned.CompanyFk === 0 || cleaned.CompanyFk === undefined || cleaned.CompanyFk === null) {
			delete (cleaned as any).CompanyFk;
		}
		if (cleaned.PrjProjectFk === 0 || cleaned.PrjProjectFk === undefined || cleaned.PrjProjectFk === null) {
			delete (cleaned as any).PrjProjectFk;
		}
		if (cleaned.PrjStockFk === 0 || cleaned.PrjStockFk === undefined || cleaned.PrjStockFk === null) {
			delete (cleaned as any).PrjStockFk;
		}
		if (cleaned.PrcStockTransactionTypeFk === 0 || cleaned.PrcStockTransactionTypeFk === undefined || cleaned.PrcStockTransactionTypeFk === null) {
			delete (cleaned as any).PrcStockTransactionTypeFk;
		}

		return cleaned;
	}

	protected override onLoadSucceeded(loaded: object): IPrcInventoryHeaderEntity[] {
		const dataDto = new MainDataDto<IPrcInventoryHeaderEntity>(loaded);

		// Process the loaded data
		const headers = dataDto.main;
		console.log('Loaded inventory headers:', headers);

		if (headers && headers.length > 0) {
			// Go to first item after load
			this.goToFirst();
			console.log('Selected first header:', this.getSelectedEntity());
		}

		return headers;
	}

	/**
	 * Updates readonly state for specific field
	 */
	public updateReadOnly(entity: IPrcInventoryHeaderEntity, fieldToBeSet: string, value: boolean): void {
		// Use the data service's setEntityReadOnlyFields method
		this.setEntityReadOnlyFields(entity, [
			{
				field: fieldToBeSet as keyof IPrcInventoryHeaderEntity,
				readOnly: !value
			}
		]);
	}

	/**
	 * Navigation helper method
	 */
	public doNavigate(item: any, triggerField: string): void {
		if (item && triggerField) {
			const keys: number[] = [];
			if (typeof item === 'object') {
				keys.push(item[triggerField]);
			}
			if (typeof item === 'string') {
				keys.push(parseInt(item, 10));
			}
			// Note: In the Angular version, we would need to inject the appropriate navigation service
			// For now, this is a placeholder for the navigation functionality
			console.log('Navigate to keys:', keys);
		}
	}

}







