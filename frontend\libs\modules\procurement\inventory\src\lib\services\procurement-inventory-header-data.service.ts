/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';

import {
	DataServiceFlatRoot,
	ServiceRole,
	IDataServiceOptions,
	IDataServiceEndPointOptions,
	IDataServiceRoleOptions
} from '@libs/platform/data-access';
import { MainDataDto } from '@libs/basics/shared';

import { ProcurementInventoryHeaderGridComplete } from '../model/procurement-inventory-header-grid-complete.class';
import { IPrcInventoryHeaderEntity } from '../model/entities/prc-inventory-header-entity.interface';
import { ProcurementInventoryHeaderReadonlyProcessor } from './processors/procurement-inventory-header-readonly-processor.class';

/**
 * Procurement Inventory Header Grid Data Service.
 */
@Injectable({
	providedIn: 'root'
})

export class ProcurementInventoryHeaderDataService extends DataServiceFlatRoot<IPrcInventoryHeaderEntity, ProcurementInventoryHeaderGridComplete> {
	public readonly readonlyProcessor: ProcurementInventoryHeaderReadonlyProcessor;

	public constructor() {
		const options: IDataServiceOptions<IPrcInventoryHeaderEntity> = {
			apiUrl: 'procurement/inventory/header',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: true,
			},
			createInfo: <IDataServiceEndPointOptions>{
				endPoint: 'create',
				usePost: true,
			},
			updateInfo: <IDataServiceEndPointOptions>{
				endPoint: 'update',
				usePost: true,
			},
			deleteInfo: <IDataServiceEndPointOptions>{
				endPoint: 'delete'
			},
			roleInfo: <IDataServiceRoleOptions<IPrcInventoryHeaderEntity>>{
				role: ServiceRole.Root,
				itemName: 'InventoryHeader',
			}
		};

		super(options);

		this.readonlyProcessor = new ProcurementInventoryHeaderReadonlyProcessor(this);

		// readonly processor to entity processor
		this.processor.addProcessor([this.readonlyProcessor]);
	}
	public override createUpdateEntity(modified: IPrcInventoryHeaderEntity | null): ProcurementInventoryHeaderGridComplete {
		const complete = new ProcurementInventoryHeaderGridComplete();
		if (modified !== null) {
			complete.Id = modified.Id;

			// Clean the entity before sending to backend - remove invalid foreign key values
			const cleanedEntity = this.cleanEntityForSave(modified);
			complete.InventoryHeader = cleanedEntity;
		}

		return complete;
	}

	/**
	 * Cleans the entity by removing invalid foreign key values that would cause constraint violations
	 */
	private cleanEntityForSave(entity: IPrcInventoryHeaderEntity): IPrcInventoryHeaderEntity {
		const cleaned = { ...entity };

		// Remove foreign key fields that are 0 or invalid - let the backend handle defaults
		if (cleaned.CompanyFk === 0) {
			delete (cleaned as any).CompanyFk;
		}
		if (cleaned.PrjProjectFk === 0) {
			delete (cleaned as any).PrjProjectFk;
		}
		if (cleaned.PrjStockFk === 0) {
			delete (cleaned as any).PrjStockFk;
		}
		if (cleaned.PrcStockTransactionTypeFk === 0) {
			delete (cleaned as any).PrcStockTransactionTypeFk;
		}

		return cleaned;
	}

	public override getModificationsFromUpdate(complete: ProcurementInventoryHeaderGridComplete): IPrcInventoryHeaderEntity[] {
		return complete.InventoryHeader ? [complete.InventoryHeader] : [];
	}

	protected override onLoadSucceeded(loaded: object): IPrcInventoryHeaderEntity[] {
		const dataDto = new MainDataDto<IPrcInventoryHeaderEntity>(loaded);

		// Process the loaded data
		const headers = dataDto.main;
		if (headers && headers.length > 0) {
			// Go to first item after load
			this.goToFirst();
		}

		return headers;
	}

	/**
	 * Updates readonly state for specific field
	 */
	public updateReadOnly(entity: IPrcInventoryHeaderEntity, fieldToBeSet: string, value: boolean): void {
		// Use the data service's setEntityReadOnlyFields method
		this.setEntityReadOnlyFields(entity, [
			{
				field: fieldToBeSet as keyof IPrcInventoryHeaderEntity,
				readOnly: !value
			}
		]);
	}

	/**
	 * Navigation helper method
	 */
	public doNavigate(item: any, triggerField: string): void {
		if (item && triggerField) {
			const keys: number[] = [];
			if (typeof item === 'object') {
				keys.push(item[triggerField]);
			}
			if (typeof item === 'string') {
				keys.push(parseInt(item, 10));
			}
			// Note: In the Angular version, we would need to inject the appropriate navigation service
			// For now, this is a placeholder for the navigation functionality
			console.log('Navigate to keys:', keys);
		}
	}

	/**
	 * Creates a new entity with default values
	 */
	public createNewEntityWithDefaults(): IPrcInventoryHeaderEntity {
		const newEntity = {} as IPrcInventoryHeaderEntity;

		// Set default values - only set non-foreign key fields to avoid constraint violations
		newEntity.Id = 0;
		newEntity.IsPosted = false;
		newEntity.HasInventory = false;
		newEntity.InventoryDate = new Date().toISOString().split('T')[0]; // Today's date
		newEntity.TransactionDate = new Date().toISOString().split('T')[0]; // Today's date

		// Foreign keys should be set by the user or through lookups, not defaulted to 0
		// newEntity.CompanyFk = 0;  // Will be set by the system
		// newEntity.PrjProjectFk = 0;  // Must be selected by user
		// newEntity.PrjStockFk = 0;  // Must be selected by user
		// newEntity.PrcStockTransactionTypeFk = 0;  // Must be selected by user

		return newEntity;
	}
}







