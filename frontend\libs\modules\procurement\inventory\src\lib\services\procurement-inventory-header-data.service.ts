/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';

import { 
	DataServiceFlatRoot,
	ServiceRole,
	IDataServiceOptions, 
	IDataServiceEndPointOptions,
	IDataServiceRoleOptions
} from '@libs/platform/data-access';

import { ProcurementInventoryHeaderGridComplete } from '../model/procurement-inventory-header-grid-complete.class';
import { IPrcInventoryHeaderEntity } from '../model/entities/prc-inventory-header-entity.interface';
import { ProcurementInventoryHeaderReadonlyProcessor } from './processors/procurement-inventory-header-readonly-processor.class';

/**
 * Procurement Inventory Header Grid Data Service.
 */
@Injectable({
	providedIn: 'root'
})

export class ProcurementInventoryHeaderDataService extends DataServiceFlatRoot<IPrcInventoryHeaderEntity, ProcurementInventoryHeaderGridComplete> {	
	public readonly readonlyProcessor: ProcurementInventoryHeaderReadonlyProcessor;

	public constructor() {
		const options: IDataServiceOptions<IPrcInventoryHeaderEntity> = {
			apiUrl: 'procurement/inventory/header',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: true,
			},
			updateInfo: <IDataServiceEndPointOptions>{
				endPoint: 'update',
				usePost: true,
			},
			deleteInfo: <IDataServiceEndPointOptions>{
				endPoint: 'delete'
			},
			roleInfo: <IDataServiceRoleOptions<IPrcInventoryHeaderEntity>>{
				role: ServiceRole.Root,
				itemName: 'InventoryHeader',
			}
		};

		super(options);
		
		this.readonlyProcessor = new ProcurementInventoryHeaderReadonlyProcessor(this);
		
		// readonly processor to entity processor
		this.processor.addProcessor([this.readonlyProcessor]);
	}
	public override createUpdateEntity(modified: IPrcInventoryHeaderEntity | null): ProcurementInventoryHeaderGridComplete {
		const complete = new ProcurementInventoryHeaderGridComplete();
		if (modified !== null) {
			complete.Id = modified.Id;
			complete.InventoryHeader = [modified];
		}

		return complete;
	}
}







