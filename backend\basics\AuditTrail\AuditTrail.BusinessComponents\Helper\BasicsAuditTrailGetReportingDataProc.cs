﻿using EntityFrameworkExtras.EF6;
using System;
using System.Collections.Generic;
using System.Data;

namespace RIB.Visual.Basics.AuditTrail.BusinessComponents
{
    /// <summary>
    /// Represents input types for the Stored Procedure
    /// </summary>
    [StoredProcedure("AUD_REPORT_PROC")]
    public class BasicsAuditTrailGetReportingDataProc
    {
        /// <summary>
        /// Record FK for the search
        /// </summary>
        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "Object_FK")]
        public int? ObjectFK { get; set; }

        /// <summary>
        /// Record FK for the search
        /// </summary>
        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "Record_FK")]
        public int? RecordFK { get; set; }

        /// <summary>
        /// From Date for the search. Mandatory Param
        /// </summary>
        [StoredProcedureParameter(SqlDbType.Date, ParameterName = "DateFrom", Options = StoredProcedureParameterOptions.Mandatory)]
        public DateTime DateFrom { get; set; }

        /// <summary>
        /// To Date for the search. Mandatory Param
        /// </summary>
        [StoredProcedureParameter(SqlDbType.Date, ParameterName = "DateTo", Options = StoredProcedureParameterOptions.Mandatory)]
        public DateTime DateTo { get; set; }

        /// <summary>
        /// Container UUID List for the search. Need atlease one container UUID for the search to provide results
        /// </summary>
        [StoredProcedureParameter(SqlDbType.Udt, ParameterName = "udtt_ContainerList", Options = StoredProcedureParameterOptions.Mandatory)]
        public List<BasicsAuditTrailUdttContainerList> UdttContainerList { get; set; }

        /// <summary>
        /// Column name List for the search. Should be empth list for all columns
        /// </summary>
        [StoredProcedureParameter(SqlDbType.Udt, ParameterName = "udtt_ColumnList")]
        public List<BasicsAuditTrailUdttColumnList> UdttColumnList { get; set; }

        /// <summary>
        /// Action for current search (A/U/D)
        /// </summary>
        [StoredProcedureParameter(SqlDbType.Char, ParameterName = "Action", Size=1, Options=StoredProcedureParameterOptions.Mandatory)]
        public string Action { get; set; }

        /// <summary>
        /// LogOnNameContains for current search
        /// </summary>
        [StoredProcedureParameter(SqlDbType.NVarChar, ParameterName = "LogOnNameContains", Size=255)]
        public string LogOnNameContains { get; set; }

        /// <summary>
        /// Page size for current search 
        /// </summary>
        [StoredProcedureParameter(SqlDbType.Int, Direction = ParameterDirection.Output, ParameterName = "PageSize")]
        public int PageSize { get; set; }

        /// <summary>
        /// Page Number for current search 
        /// </summary>
        [StoredProcedureParameter(SqlDbType.Int, ParameterName = "PageNumber", Options = StoredProcedureParameterOptions.Mandatory)]
        public int PageNumber { get; set; }

        /// <summary>
        /// Last Page Number for current search 
        /// </summary>
        [StoredProcedureParameter(SqlDbType.Int, Direction = ParameterDirection.Output, ParameterName = "LastPageNo")]
        public int LastPageNo { get; set; }

        /// <summary>
        /// Total No. of records for current search 
        /// </summary>
        [StoredProcedureParameter(SqlDbType.Int, Direction = ParameterDirection.Output, ParameterName = "TotalRecords")]
        public int TotalRecords { get; set; }
    }
}
