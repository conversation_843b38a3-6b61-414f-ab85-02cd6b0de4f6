/*
 * Copyright(c) RIB Software GmbH
 */

import { TestBed } from '@angular/core/testing';
import { ProcurementInventoryHeaderDataService } from './procurement-inventory-header-data.service';
import { ProcurementInventoryHeaderValidationService } from './validations/procurement-inventory-header-validation.service';

describe('ProcurementInventoryHeaderDataService', () => {
	let service: ProcurementInventoryHeaderDataService;
	let validationService: ProcurementInventoryHeaderValidationService;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [
				ProcurementInventoryHeaderDataService,
				ProcurementInventoryHeaderValidationService
			]
		});
		service = TestBed.inject(ProcurementInventoryHeaderDataService);
		validationService = TestBed.inject(ProcurementInventoryHeaderValidationService);
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should have validation service', () => {
		expect(validationService).toBeTruthy();
	});

	it('should support create operations', () => {
		expect(service.supportsCreate()).toBeTruthy();
	});

	it('should support delete operations', () => {
		expect(service.supportsDelete()).toBeTruthy();
	});

	it('should create update entity correctly', () => {
		const mockEntity = {
			Id: 1,
			Description: 'Test Header',
			InventoryDate: '2023-01-01',
			TransactionDate: '2023-01-01',
			PrjProjectFk: 1,
			PrjStockFk: 1,
			PrcStockTransactionTypeFk: 1,
			IsPosted: false,
			HasInventory: false
		} as any;

		const complete = service.createUpdateEntity(mockEntity);
		
		expect(complete).toBeTruthy();
		expect(complete.Id).toBe(1);
		expect(complete.InventoryHeader).toEqual([mockEntity]);
	});
});
