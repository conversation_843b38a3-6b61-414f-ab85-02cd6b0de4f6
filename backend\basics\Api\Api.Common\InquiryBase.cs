﻿using System;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// 
	/// </summary>
	/// <typeparam name="TInt"></typeparam>
	public interface IInquiryBase<TInt>
	{
		/// <summary>
		/// unique identifier
		/// </summary>
		TInt Id { get; set; }

		/// <summary>
		/// Name of the inquiry item
		/// </summary>
		String Name { get; set; }

		/// <summary>
		/// more info description of the inquiry item
		/// </summary>
		String Description { get; set; }
	}


	/// <summary>
	/// Base class for API Inquiry Calls holding all general required porperties
	/// </summary>
	/// <typeparam name="TInt"></typeparam>
	public class InquiryBase<TInt> : IInquiryBase<TInt>
	{
		/// <summary>
		/// unique identifier
		/// </summary>
		[JsonProperty ("id")]
		public TInt Id { get; set; }
	
		/// <summary>
		/// Name of the inquiry item
		/// </summary>
		[JsonProperty ("name")]
		public String Name { get; set; }
		/// <summary>
		/// more info description of the inquiry item
		/// </summary>
		[JsonProperty ("description")]
		public String Description { get; set; }
	}
}
