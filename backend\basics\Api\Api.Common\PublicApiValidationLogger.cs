using RIB.Visual.Basics.Core.Core;
using System;
using System.Collections.Generic;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Represents a logger for validation.
	/// </summary>
	public class PublicApiValidationLogger : IPublicApiExecutionLogger
	{
		private List<PublicApiValidationResult> _validationResults = new List<PublicApiValidationResult>();

		/// <summary>
		/// Constructor.
		/// </summary>
		public PublicApiValidationLogger()
		{
			IsInfoEnabled = false;
			IsWarningEnabled = true;
			IsErrorEnabled = true;
		}

		#region Properties

		/// <summary>
		/// A flag to indicate if the info message is recorded.
		/// </summary>
		public bool IsInfoEnabled
		{
			get;
			set;
		}

		/// <summary>
		/// A flag to indicate if the debug message is recorded.
		/// </summary>
		public bool IsDebugEnabled
		{
			get;
			set;
		}

		/// <summary>
		/// A flag to indicate if the warning message is recorded.
		/// </summary>
		public bool IsWarningEnabled
		{
			get;
			set;
		}

		/// <summary>
		/// A flag to indicate if the error message is recorded.
		/// </summary>
		public bool IsErrorEnabled
		{
			get;
			set;
		}

		/// <summary>
		/// The validation result.
		/// </summary>
		public IEnumerable<PublicApiValidationResult> ValidationResults
		{
			get { return _validationResults; }
		}

		#endregion

		#region IPublicApiExecutionLogger members

		/// <summary>
		/// Writes warning message.
		/// </summary>
		/// <param name="message"></param>
		public void WriteWarning(string message)
		{
			if (IsWarningEnabled)
			{
				_validationResults.Add(new PublicApiValidationResult() { IsValid = true, MessageType = ValidationMessageType.Warning, ErrorContent = message });
			}
		}

		/// <summary>
		/// Writes info message.
		/// </summary>
		/// <param name="message"></param>
		public void WriteInfo(string message)
		{
			if (IsInfoEnabled)
			{
				_validationResults.Add(new PublicApiValidationResult() { IsValid = true, MessageType = ValidationMessageType.Information, ErrorContent = message });
			}
		}

		/// <summary>
		/// Writes error message.
		/// </summary>
		/// <param name="message"></param>
		public void WriteError(string message)
		{
			if (IsErrorEnabled)
			{
				_validationResults.Add(new PublicApiValidationResult() { IsValid = false, MessageType = ValidationMessageType.Error, ErrorContent = message });
			}
		}

		/// <summary>
		/// Writes debug message.
		/// </summary>
		/// <param name="message"></param>
		public void WriteDebug(string message)
		{
			if (IsDebugEnabled)
			{
				_validationResults.Add(new PublicApiValidationResult() { IsValid = true, MessageType = ValidationMessageType.Debug, ErrorContent = message });
			}
		}

		/// <summary>
		/// Write Exception message
		/// </summary>
		/// <param name="ex"></param>
		public void WriteException(Exception ex)
		{
			if (IsErrorEnabled)
			{
				if (ex != null)
				{
					_validationResults.Add(new PublicApiValidationResult() { IsValid = true, MessageType = ValidationMessageType.Error, ErrorContent = ex.Message });
				}
			}

			if (IsDebugEnabled)
			{
				if (ex != null)
				{
					var eNext = ex;
					while (eNext.InnerException != null)
					{
						eNext = eNext.InnerException;
					}

					_validationResults.Add(new PublicApiValidationResult() { IsValid = true, MessageType = ValidationMessageType.Debug, ErrorContent = eNext.Message });
				}
			}
		}

		#endregion
	}
}
