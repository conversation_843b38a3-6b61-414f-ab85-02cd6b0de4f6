﻿/*
 * $Id: ApiDocVariableAttribute.cs 576980 2020-02-21 11:15:08Z haagf $
 * Copyright (c) RIB Software SE
 */

using System;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Declares a variable with a specified value for use in API documentation comments.
	/// </summary>
	[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true, Inherited = false)]
	public sealed class ApiDocVariableAttribute : ApiDocVariableAttributeBase
	{
		/// <summary>
		/// Initializes a new instance.
		/// </summary>
		/// <param name="name">The identifying name of the variable.</param>
		/// <param name="value">The value of the variable.</param>
		/// <exception cref="ArgumentNullException">Any of the arguments is <see langword="null"/>.</exception>
		public ApiDocVariableAttribute(String name, String value) : base(name)
		{
			if (value == null)
			{
				throw new ArgumentNullException("value");
			}

			Value = value;
		}

		/// <summary>
		/// The value of the variable.
		/// </summary>
		public String Value { get; private set; }

		/// <summary>
		/// Expands the variable based on the given context object.
		/// </summary>
		/// <param name="context">The context object.</param>
		/// <returns>The expanded value of the variable.</returns>
		/// <exception cref="ArgumentNullException"><paramref name="context"/> is <see langword="null"/>.</exception>
		public override String Expand(VariableExpansionContext context)
		{
			return Value;
		}
	}
}
