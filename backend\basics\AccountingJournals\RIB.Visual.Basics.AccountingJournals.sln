﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2019
VisualStudioVersion = 12.0.40629.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "AccountingJournals", "AccountingJournals", "{BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Foundation", "Foundation", "{99DF2B87-942B-456C-8065-19D82BFD4B66}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Presentation Layer", "Presentation Layer", "{5A68D1F9-923B-420E-B05A-192EAEFCBEED}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Client", "Client", "{7910B591-73C9-4033-8431-70E5D3527B76}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Service Layer", "Service Layer", "{ECB189F5-611B-463C-87E5-384B127E9BB4}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Service Facades", "Service Facades", "{14439C06-8163-48A3-97E8-5541F28E465A}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Business Layer", "Business Layer", "{DC8CA2C8-CB16-4927-9D35-3207505075D9}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTests", "UnitTests", "{C35FCB33-C320-4750-8E23-C28CEF8E57F2}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{ED4B0F69-FAA0-4CF5-82FD-DA161441434A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.AccountingJournals.Core", "AccountingJournals.Core\RIB.Visual.Basics.AccountingJournals.Core.csproj", "{BC408BF6-A751-4E17-87F7-706A8A06CF44}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.AccountingJournals.BusinessComponents", "AccountingJournals.BusinessComponents\RIB.Visual.Basics.AccountingJournals.BusinessComponents.csproj", "{82E922C9-0BA9-4603-A332-08E73541434B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.AccountingJournals.Common", "AccountingJournals.Common\RIB.Visual.Basics.AccountingJournals.Common.csproj", "{8E7DAD1C-868F-4936-B235-7873223F7FDF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.AccountingJournals.UnitTests", "AccountingJournals.UnitTests\RIB.Visual.Basics.AccountingJournals.UnitTests.csproj", "{48F3256A-AD06-47B9-9AE7-F0FF923F683C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.AccountingJournals.Localization", "AccountingJournals.Localization\RIB.Visual.Basics.AccountingJournals.Localization.csproj", "{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.AccountingJournals.ServiceFacade.WebApi", "AccountingJournals.ServiceFacade.WebApi\RIB.Visual.Basics.AccountingJournals.ServiceFacade.WebApi.csproj", "{E51CDDC2-4293-4083-B453-69C51C4F91B2}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{BC408BF6-A751-4E17-87F7-706A8A06CF44}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BC408BF6-A751-4E17-87F7-706A8A06CF44}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BC408BF6-A751-4E17-87F7-706A8A06CF44}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BC408BF6-A751-4E17-87F7-706A8A06CF44}.Release|Any CPU.Build.0 = Release|Any CPU
		{82E922C9-0BA9-4603-A332-08E73541434B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{82E922C9-0BA9-4603-A332-08E73541434B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{82E922C9-0BA9-4603-A332-08E73541434B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{82E922C9-0BA9-4603-A332-08E73541434B}.Release|Any CPU.Build.0 = Release|Any CPU
		{8E7DAD1C-868F-4936-B235-7873223F7FDF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8E7DAD1C-868F-4936-B235-7873223F7FDF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8E7DAD1C-868F-4936-B235-7873223F7FDF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8E7DAD1C-868F-4936-B235-7873223F7FDF}.Release|Any CPU.Build.0 = Release|Any CPU
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C}.Release|Any CPU.Build.0 = Release|Any CPU
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}.Release|Any CPU.Build.0 = Release|Any CPU
		{E51CDDC2-4293-4083-B453-69C51C4F91B2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E51CDDC2-4293-4083-B453-69C51C4F91B2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E51CDDC2-4293-4083-B453-69C51C4F91B2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E51CDDC2-4293-4083-B453-69C51C4F91B2}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{99DF2B87-942B-456C-8065-19D82BFD4B66} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{5A68D1F9-923B-420E-B05A-192EAEFCBEED} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{7910B591-73C9-4033-8431-70E5D3527B76} = {5A68D1F9-923B-420E-B05A-192EAEFCBEED}
		{ECB189F5-611B-463C-87E5-384B127E9BB4} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{14439C06-8163-48A3-97E8-5541F28E465A} = {ECB189F5-611B-463C-87E5-384B127E9BB4}
		{DC8CA2C8-CB16-4927-9D35-3207505075D9} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{C35FCB33-C320-4750-8E23-C28CEF8E57F2} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{BC408BF6-A751-4E17-87F7-706A8A06CF44} = {99DF2B87-942B-456C-8065-19D82BFD4B66}
		{82E922C9-0BA9-4603-A332-08E73541434B} = {DC8CA2C8-CB16-4927-9D35-3207505075D9}
		{8E7DAD1C-868F-4936-B235-7873223F7FDF} = {99DF2B87-942B-456C-8065-19D82BFD4B66}
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C} = {C35FCB33-C320-4750-8E23-C28CEF8E57F2}
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F} = {99DF2B87-942B-456C-8065-19D82BFD4B66}
		{E51CDDC2-4293-4083-B453-69C51C4F91B2} = {14439C06-8163-48A3-97E8-5541F28E465A}
	EndGlobalSection
	GlobalSection(SolutionConfigurationAccountingJournalss) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|ARM = Debug|ARM
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|ARM = Release|ARM
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationAccountingJournalss) = postSolution
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|Any CPU.Build.0 = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|ARM.ActiveCfg = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|x64.ActiveCfg = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|x86.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|ARM.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|x64.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|x86.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|ARM.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|x64.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|x86.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|x64.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|x86.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|Any CPU.Build.0 = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|ARM.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|x64.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|x86.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|ARM.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|x64.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|x86.ActiveCfg = Release|Any CPU
		{BC408BF6-A751-4E17-87F7-706A8A06CF44}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BC408BF6-A751-4E17-87F7-706A8A06CF44}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BC408BF6-A751-4E17-87F7-706A8A06CF44}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{BC408BF6-A751-4E17-87F7-706A8A06CF44}.Debug|x64.ActiveCfg = Debug|Any CPU
		{BC408BF6-A751-4E17-87F7-706A8A06CF44}.Debug|x86.ActiveCfg = Debug|Any CPU
		{BC408BF6-A751-4E17-87F7-706A8A06CF44}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BC408BF6-A751-4E17-87F7-706A8A06CF44}.Release|Any CPU.Build.0 = Release|Any CPU
		{BC408BF6-A751-4E17-87F7-706A8A06CF44}.Release|ARM.ActiveCfg = Release|Any CPU
		{BC408BF6-A751-4E17-87F7-706A8A06CF44}.Release|x64.ActiveCfg = Release|Any CPU
		{BC408BF6-A751-4E17-87F7-706A8A06CF44}.Release|x86.ActiveCfg = Release|Any CPU
		{82E922C9-0BA9-4603-A332-08E73541434B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{82E922C9-0BA9-4603-A332-08E73541434B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{82E922C9-0BA9-4603-A332-08E73541434B}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{82E922C9-0BA9-4603-A332-08E73541434B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{82E922C9-0BA9-4603-A332-08E73541434B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{82E922C9-0BA9-4603-A332-08E73541434B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{82E922C9-0BA9-4603-A332-08E73541434B}.Release|Any CPU.Build.0 = Release|Any CPU
		{82E922C9-0BA9-4603-A332-08E73541434B}.Release|ARM.ActiveCfg = Release|Any CPU
		{82E922C9-0BA9-4603-A332-08E73541434B}.Release|x64.ActiveCfg = Release|Any CPU
		{82E922C9-0BA9-4603-A332-08E73541434B}.Release|x86.ActiveCfg = Release|Any CPU
		{8E7DAD1C-868F-4936-B235-7873223F7FDF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8E7DAD1C-868F-4936-B235-7873223F7FDF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8E7DAD1C-868F-4936-B235-7873223F7FDF}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{8E7DAD1C-868F-4936-B235-7873223F7FDF}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8E7DAD1C-868F-4936-B235-7873223F7FDF}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8E7DAD1C-868F-4936-B235-7873223F7FDF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8E7DAD1C-868F-4936-B235-7873223F7FDF}.Release|Any CPU.Build.0 = Release|Any CPU
		{8E7DAD1C-868F-4936-B235-7873223F7FDF}.Release|ARM.ActiveCfg = Release|Any CPU
		{8E7DAD1C-868F-4936-B235-7873223F7FDF}.Release|x64.ActiveCfg = Release|Any CPU
		{8E7DAD1C-868F-4936-B235-7873223F7FDF}.Release|x86.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|Any CPU.Build.0 = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|ARM.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|x64.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|x86.ActiveCfg = Release|Any CPU
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C}.Release|Any CPU.Build.0 = Release|Any CPU
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C}.Release|ARM.ActiveCfg = Release|Any CPU
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C}.Release|x64.ActiveCfg = Release|Any CPU
		{48F3256A-AD06-47B9-9AE7-F0FF923F683C}.Release|x86.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|ARM.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|x64.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|x86.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|Any CPU.Build.0 = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|ARM.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|x64.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|x86.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|ARM.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|x64.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|x86.ActiveCfg = Release|Any CPU
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}.Release|Any CPU.Build.0 = Release|Any CPU
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}.Release|ARM.ActiveCfg = Release|Any CPU
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}.Release|x64.ActiveCfg = Release|Any CPU
		{7F8DA4C8-6029-4E13-B744-7BEB76AF2E4F}.Release|x86.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|Any CPU.Build.0 = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|ARM.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|x64.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|x86.ActiveCfg = Release|Any CPU
		{E51CDDC2-4293-4083-B453-69C51C4F91B2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E51CDDC2-4293-4083-B453-69C51C4F91B2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E51CDDC2-4293-4083-B453-69C51C4F91B2}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{E51CDDC2-4293-4083-B453-69C51C4F91B2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E51CDDC2-4293-4083-B453-69C51C4F91B2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E51CDDC2-4293-4083-B453-69C51C4F91B2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E51CDDC2-4293-4083-B453-69C51C4F91B2}.Release|Any CPU.Build.0 = Release|Any CPU
		{E51CDDC2-4293-4083-B453-69C51C4F91B2}.Release|ARM.ActiveCfg = Release|Any CPU
		{E51CDDC2-4293-4083-B453-69C51C4F91B2}.Release|x64.ActiveCfg = Release|Any CPU
		{E51CDDC2-4293-4083-B453-69C51C4F91B2}.Release|x86.ActiveCfg = Release|Any CPU
	EndGlobalSection
	GlobalSection(TestCaseManagementSettings) = postSolution
		CategoryFile = RIB.Visual.Basics.AccountingJournals.vsmdi
	EndGlobalSection
	GlobalSection(DPCodeReviewSolutionGUID) = preSolution
		DPCodeReviewSolutionGUID = {********-0000-0000-0000-********0000}
	EndGlobalSection
	GlobalSection(TextTemplating) = postSolution
		TextTemplating = 1
	EndGlobalSection
EndGlobal
