using RIB.Visual.Basics.AuditTrailConfig.BusinessComponents;
using System;
using System.Collections.Generic;

namespace RIB.Visual.Basic.AuditTrailConfig.AdminConsole
{
    public class Program
    {
        /// <summary>
        /// Console method for Audit Trail Admin purpose
        /// </summary>
        /// <param name="args">Input option. 1 to Create, 2 to Enable, 3 to Disable and 4 to Delete Triggers</param>
        /// <returns>0 as Success and -1 on Failure</returns>
        static int Main(string[] args)
        {
            // Test if input arguments were supplied:
            if (args.Length == 0)
            {
                System.Console.WriteLine("Please enter Audit Trial option to execute.");
                System.Console.WriteLine("Usage: value <num>");
                throw new Exception("Please enter Audit Trial option to execute.");
            }

            // Try to convert the input arguments to numbers. This will throw
            // an exception if the argument is not a number.
            // num = int.Parse(args[0]);
            int num;
            bool TestNumber = int.TryParse(args[0], out num);
            if (TestNumber == false)
            {
                System.Console.WriteLine("Please enter a numeric argument.");
                System.Console.WriteLine("Usage: value <num>");
                throw new Exception("Please enter a numeric argument.");
            }

            var logic = new AuditTrailConfigBusinessComponentsContainerLogic();
            bool result = false;

            try
            {
                Environment.SetEnvironmentVariable("RIBVISUAL_SERVICESSELFHOSTING", "1");

                var test = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.SelfHosting;

                var context = new Platform.BusinessComponents.DbContext(ModelBuilder.DbModel);
            }
            catch
            {
                // kris to correct the behaviour for the exception in the Platform module 
            }


            try
            {
                Console.WriteLine();
                Console.WriteLine("Inputed Param for Audit Trail options is " + num.ToString());
                Console.WriteLine();
                Console.WriteLine("Audit Trial option to execute");
                Console.WriteLine("1 - Create/re-create all (use post configuration changes)");
                Console.WriteLine("2 - Enable all (use to enable all as per configuration, use in combination with Disable all)");
                Console.WriteLine("3 - Disable all (use to disable all, use in combination with Enable all)");
                Console.WriteLine("4 - Delete all (use to delete all)");
                Console.WriteLine("0 - Exit");

                Console.WriteLine();
                Console.WriteLine("---------------------------------------------------------------------");

                if (num.ToString().CompareTo("1") == 0)
                {
                    result = logic.AuditMainMethod(new List<AudTableEntity>(), new List<AudColumnEntity>(), AuditTrailConfigBusinessComponentsContainerLogic.WizardOptions.CreateAll, false);
                    if (result)
                    {
                        Console.WriteLine("Create/re-create all (use post configuration changes) executed succesfully");
                    }
                    else
                    {
                        Console.WriteLine("Create/re-create all (use post configuration changes) execution failed");
                    }
                }
                else if (num.ToString().CompareTo("2") == 0)
                {
                    result = logic.AuditMainMethod(new List<AudTableEntity>(), new List<AudColumnEntity>(), AuditTrailConfigBusinessComponentsContainerLogic.WizardOptions.EnableAll, false);
                    if (result)
                    {
                        Console.WriteLine("Enable all (use to enable all as per configuration, use in combination with Disable all) executed succesfully");
                    }
                    else
                    {
                        Console.WriteLine("Enable all (use to enable all as per configuration, use in combination with Disable all) execution failed");
                    }
                }
                else if (num.ToString().CompareTo("3") == 0)
                {
                    result = logic.AuditMainMethod(new List<AudTableEntity>(), new List<AudColumnEntity>(), AuditTrailConfigBusinessComponentsContainerLogic.WizardOptions.DisableAll, false);
                    if (result)
                    {
                        Console.WriteLine("Disable all (use to disable all, use in combination with Enable all) executed succesfully");
                    }
                    else
                    {
                        Console.WriteLine("Disable all (use to disable all, use in combination with Enable all) execution failed");
                    }
                }
                else if (num.ToString().CompareTo("4") == 0)
                {
                    result = logic.AuditMainMethod(new List<AudTableEntity>(), new List<AudColumnEntity>(), AuditTrailConfigBusinessComponentsContainerLogic.WizardOptions.DeleteAll, false);
                    if (result)
                    {
                        Console.WriteLine("Delete all (use to delete all) executed succesfully");
                    }
                    else
                    {
                        Console.WriteLine("Delete all (use to delete all) execution failed");
                    }
                }
                Console.WriteLine();
                Console.WriteLine("---------------------------------------------------------------------");

                if (result)
                {
                    return 0;
                }
                else
                {
                    return -1;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("---------------------------------------------------------------------");
                Console.WriteLine("Exception " + ex.Message);
                Console.WriteLine("---------------------------------------------------------------------");
                if (string.IsNullOrEmpty(ex.InnerException.Message))
                {
                    Console.WriteLine("Inner Exception " + ex.InnerException.Message);
                    Console.WriteLine("---------------------------------------------------------------------");
                }
                Console.WriteLine();
                Console.WriteLine("Press any key to close the application");

                throw;
            }

        }
    }
}
