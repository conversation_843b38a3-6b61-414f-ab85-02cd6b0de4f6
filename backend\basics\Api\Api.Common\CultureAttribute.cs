﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Use to validate culture string such as en, en-us, zh, etc.
	/// </summary>
	public class CultureAttribute : ValidationAttribute
	{
		/// <summary>
		/// Determines whether a specified object is valid.
		/// </summary>
		/// <param name="value">The object to validate.</param>
		/// <param name="validationContext">The validation context</param>
		/// <returns></returns>
		protected override ValidationResult IsValid(object value, ValidationContext validationContext)
		{
			ValidationResult result = ValidationResult.Success;

			if (value != null)
			{
				//test the culture if it's valid or not.
				try
				{
					new System.Globalization.CultureInfo(value.ToString());
				}
				catch (Exception /*ex*/)
				{
					string errorMsg = string.Format("[{0}] is an invalid culture identifier", validationContext.MemberName);
					result = new ValidationResult(FormatErrorMessage(validationContext.MemberName), new List<string>() { validationContext.MemberName });
				}
			}

			return result;
		}
	}
}
