﻿/*
 * $Id: ApiDocVariableAttributeBase.cs 576980 2020-02-21 11:15:08Z haagf $
 * Copyright (c) RIB Software SE
 */

using System;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// The base class for variable-related API doc attributes.
	/// </summary>
	public abstract class ApiDocVariableAttributeBase : Attribute
	{
		/// <summary>
		/// Initializes a new instance.
		/// </summary>
		/// <param name="name">The identifying name of the variable.</param>
		/// <exception cref="ArgumentNullException"><paramref name="name"/> is <see langword="null"/>.</exception>
		protected ApiDocVariableAttributeBase(String name)
		{
			if (name == null)
			{
				throw new ArgumentNullException("name");
			}

			Name = name;
		}

		/// <summary>
		/// The identifying name of the variable.
		/// </summary>
		public String Name { get; private set; }

		/// <summary>
		/// Expands the variable based on the given context object.
		/// </summary>
		/// <param name="context">The context object.</param>
		/// <returns>The expanded value of the variable.</returns>
		/// <exception cref="ArgumentNullException"><paramref name="context"/> is <see langword="null"/>.</exception>
		public abstract String Expand(VariableExpansionContext context);
	}
}
