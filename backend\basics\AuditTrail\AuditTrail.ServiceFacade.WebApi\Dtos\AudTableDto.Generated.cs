//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Basics.AuditTrail.BusinessComponents;

namespace RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi
{

    /// <summary>
    /// Represents a Dto class.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("AUD_TABLE")]
    public partial class AudTableDto : RIB.Visual.Platform.Core.ITypedDto<AudTableEntity>
    {
        #region Constructors
       
        /// <summary>
        /// Initializes an instance of class AudTableDto.
        /// </summary>
        public AudTableDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class AudTableDto.
        /// </summary>
        /// <param name="entity">the instance of class AudTableEntity</param>
        public AudTableDto(AudTableEntity entity)
        {
            Id = entity.Id;
            Tablename = entity.Tablename;
            Logtablename = entity.Logtablename;
            DescriptionTr = entity.DescriptionTr;
            Description = entity.Description;
            Isenabletracking = entity.Isenabletracking;
            Archivedays = entity.Archivedays;
            Purgedays = entity.Purgedays;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;
            
            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion 

        #region Properties

        /// <summary>
        /// Gets or Sets Id.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }

        /// <summary>
        /// Gets or Sets Tablename.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TABLENAME", TypeName = "varchar(30)", Order = 1)]
        [System.ComponentModel.DataAnnotations.StringLength(30)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string Tablename { get; set; }

        /// <summary>
        /// Gets or Sets Logtablename.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LOGTABLENAME", TypeName = "varchar(30)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(30)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string Logtablename { get; set; }

        /// <summary>
        /// Gets or Sets DescriptionTr.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION_TR", TypeName = "int", Order = 3)]
        public int? DescriptionTr { get; set; }

        /// <summary>
        /// Gets or Sets Description.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Description { get; set; }

        /// <summary>
        /// Gets or Sets Isenabletracking.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISENABLETRACKING", TypeName = "bit", Order = 5)]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool Isenabletracking { get; set; }

        /// <summary>
        /// Gets or Sets Archivedays.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ARCHIVEDAYS", TypeName = "smallint", Order = 6)]
        [System.ComponentModel.DataAnnotations.Required()]
        public short Archivedays { get; set; }

        /// <summary>
        /// Gets or Sets Purgedays.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PURGEDAYS", TypeName = "smallint", Order = 7)]
        [System.ComponentModel.DataAnnotations.Required()]
        public short Purgedays { get; set; }

        /// <summary>
        /// Gets or Sets InsertedAt.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 8)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public global::System.DateTime InsertedAt { get; set; }

        /// <summary>
        /// Gets or Sets InsertedBy.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 9)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }

        /// <summary>
        /// Gets or Sets UpdatedAt.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 10)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Gets or Sets UpdatedBy.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 11)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// Gets or Sets Version.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 12)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }

        #endregion

        #region Navigation Properties

        /// <summary />
        public List<AudColumnDto> AudColumnEntities { get; set; }

        /// <summary />
        public List<AudContainer2AudTableDto> AudContainer2AudTableEntities { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(AudTableEntity); }
        }

        /// <summary>
        /// Copy the current AudTableDto instance to a new AudTableEntity instance.
        /// </summary>
        /// <returns>a new instance of class AudTableEntity</returns>
        public AudTableEntity Copy() 
        {
          var entity = new AudTableEntity();

          entity.Id = this.Id;
          entity.Tablename = this.Tablename;
          entity.Logtablename = this.Logtablename;
          entity.DescriptionTr = this.DescriptionTr;
          entity.Description = this.Description;
          entity.Isenabletracking = this.Isenabletracking;
          entity.Archivedays = this.Archivedays;
          entity.Purgedays = this.Purgedays;
          entity.InsertedAt = this.InsertedAt;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedAt = this.UpdatedAt;
          entity.UpdatedBy = this.UpdatedBy;
          entity.Version = this.Version;

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(AudTableEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(AudTableEntity entity);
    }

}
