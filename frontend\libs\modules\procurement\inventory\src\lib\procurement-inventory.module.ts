/*
 * Copyright(c) RIB Software GmbH
 */

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { BusinessModuleRoute } from '@libs/ui/business-base';
import { UiCommonModule } from '@libs/ui/common';
import { ProcurementInventoryModuleInfo } from './model/procurement-inventory-module-info.class';
import { ProcurementInventoryHeaderDataService } from './services/procurement-inventory-header-data.service';
import { ProcurementInventoryGridDataService } from './services/procurement-inventory-grid-data.service';
import { ProcurementInventoryHeaderValidationService } from './services/validations/procurement-inventory-header-validation.service';
import { ProcurementInventoryValidationService } from './services/validations/procurement-inventory-validation.service';

const routes: Routes = [new BusinessModuleRoute(ProcurementInventoryModuleInfo.instance)];
@NgModule({
	imports: [CommonModule, RouterModule.forChild(routes), UiCommonModule],
	providers: [
		ProcurementInventoryHeaderDataService,
		ProcurementInventoryGridDataService,
		ProcurementInventoryHeaderValidationService,
		ProcurementInventoryValidationService,
	],
})
export class ProcurementInventoryModule {}
