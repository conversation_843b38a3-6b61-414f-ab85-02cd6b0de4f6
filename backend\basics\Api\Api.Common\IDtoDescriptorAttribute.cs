﻿/*
 * $Id: IDtoDescriptorAttribute.cs 577880 2020-03-02 06:09:59Z haagf $
 * Copyright (c) RIB Software SE
 */

using System;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// A common interface for attributes that describe a DTO.
	/// </summary>
	public interface IDtoDescriptorAttribute
	{
		/// <summary>
		/// Specify the DTO type.
		///   If <see langword="null"/>, the declared type from the underlying method will be used.
		/// </summary>
		Type Type
		{
			get;
			set;
		}

		/// <summary>
		/// Specify the special DTO properties not shown in webAPI docs.
		/// </summary>
		String[] ExcludingProperties
		{
			get;
			set;
		}

		/// <summary>
		/// Gets or sets a value that indicates whether properties that belong to the primary key should be excluded.
		/// </summary>
		Boolean ExcludingKeyProperties
		{
			get;
			set;
		}

		/// <summary>
		/// If set, the type supplied for the specified type parameter will be used.
		/// </summary>
		String TypeParamName { get; set; }

		/// <summary>
		/// Indicates whether an array of elements of the indicated type is used.
		/// </summary>
		Boolean AsArray { get; set; }
	}
}
