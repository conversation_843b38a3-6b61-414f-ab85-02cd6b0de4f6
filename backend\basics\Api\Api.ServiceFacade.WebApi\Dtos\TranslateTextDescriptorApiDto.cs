/*
 * $Id: TranslateTextDescriptorApiDto.cs 559799 2019-09-20 03:15:06Z lst $
 * Copyright (c) RIB Software SE
 */

using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.Common;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	///
	/// </summary>
	public static class TranslateTextDescriptorApiDtoExtension
	{
		/// <summary>
		///
		/// </summary>
		/// <typeparam name="TDto"></typeparam>
		/// <param name="dtos"></param>
		/// <param name="dictCulture"></param>
		/// <param name="descriptionInfoGetter"></param>
		public static void PopulateDtoOtherLanguages<TDto>(this IEnumerable<TDto> dtos, IDictionary<int, string> dictCulture, params Func<TDto, TranslateTextDescriptorApiDto>[] descriptionInfoGetter) where TDto : class
		{
			//todo: check parameter to ensure it is not null
			foreach (var item in dtos)
			{
				foreach (var descItemFn in descriptionInfoGetter)
				{
					var descriptionInfo = descItemFn(item);
					descriptionInfo.PupulateOtherLanguages(dictCulture);
				}
			}
		}
	}

	/// <summary>
	/// Represents a translate text for public api dto.
	/// </summary>
	public class TranslateTextDescriptorApiDto
	{

		/// <summary>
		/// Constructor.
		/// </summary>
		public TranslateTextDescriptorApiDto()
		{
		}

		/// <summary>
		/// Constructor.
		/// </summary>
		/// <param name="descriptionInfo"></param>
		public TranslateTextDescriptorApiDto(DescriptionTranslateType descriptionInfo)
		{
			Description = descriptionInfo.Translated;
			//todo-mike: check if need to copy OtherLanguages
			InternalDescriptionInfo = descriptionInfo;
		}

		private DescriptionTranslateType InternalDescriptionInfo
		{
			get;
			set;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="dictCulture"></param>
		public void PupulateOtherLanguages(IDictionary<int, string> dictCulture)
		{
			if (dictCulture != null && dictCulture.Any() && InternalDescriptionInfo != null && InternalDescriptionInfo.OtherLanguages != null)
			{
				var otherLanguageList = new List<TranslateText>();
				foreach (var other in InternalDescriptionInfo.OtherLanguages)
				{
					if (dictCulture.ContainsKey(other.LanguageId))
					{
						var text = new TranslateText();
						text.Culture = dictCulture[other.LanguageId];
						text.Description = other.Description;
						otherLanguageList.Add(text);
					}
				}

				var baseLanguageId = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.DatabaseDefaultLanguageId;
				var userLanguageId = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext != null ? Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.DataLanguageId : baseLanguageId;
				if (baseLanguageId != userLanguageId && dictCulture.ContainsKey(baseLanguageId))
				{
					var baseText = new TranslateText();
					baseText.Culture = dictCulture[baseLanguageId];
					var items = otherLanguageList.Where(e => e.Culture == baseText.Culture);
					if (items.Count() == 0)
					{
						baseText.Description = InternalDescriptionInfo.Description;
						otherLanguageList.Add(baseText);
					}
				}
				OtherLanguages = otherLanguageList.ToArray();
			}
		}

		/// <summary>
		/// Constructor with OtherLanguages coping
		/// </summary>
		/// <param name="descriptionInfo"></param>
		/// <param name="dictCulture"></param>
		public TranslateTextDescriptorApiDto(DescriptionTranslateType descriptionInfo, IDictionary<int, string> dictCulture)
			: this(descriptionInfo)
		{
			this.PupulateOtherLanguages(dictCulture);
		}

		/// <summary>
		/// Description.
		/// </summary>
		[System.ComponentModel.DataAnnotations.StringLength(2000)]
		public string Description
		{
			get;
			set;
		}

		/// <summary>
		/// Other Languages.
		/// </summary>
		public TranslateText[] OtherLanguages
		{
			get;
			set;
		}

		/// <summary>
		/// Copies the current data into a new <see cref="DescriptionTranslateType"/> instance.
		/// </summary>
		/// <returns>The <see cref="DescriptionTranslateType"/> instance.</returns>
		public DescriptionTranslateType Copy()
		{
			var result = new DescriptionTranslateType();

			var languageLogic =
				Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ILanguageLogic>();
			CopyTo(result, languageLogic.GetCultureAndLanguageIdMap());

			return result;
		}

		/// <summary>
		/// Copy the current data to the incoming instance of class DescriptionTranslateType.
		/// </summary>
		/// <param name="descriptionInfo"></param>
		/// <param name="culture2LangIdMap"></param>
		/// <returns></returns>
		public DescriptionTranslateType CopyTo(DescriptionTranslateType descriptionInfo, IDictionary<string, int> culture2LangIdMap)
		{
			if (descriptionInfo == null)
			{
				throw new ArgumentNullException("descriptionInfo");
			}

			if (culture2LangIdMap == null)
			{
				throw new ArgumentNullException("culture2LangIdMap");
			}

			int languageId =
			RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.DataLanguageId;

			var databaseDefaultLanguageId = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.DatabaseDefaultLanguageId;

			if (languageId == databaseDefaultLanguageId)
			{
				if (descriptionInfo.Description != Description)
				{
					descriptionInfo.DescriptionModified = true;
				}

				descriptionInfo.Description = Description;
			}

			if (descriptionInfo.Translated != Description)
			{
				descriptionInfo.Modified = true;
			}

			descriptionInfo.Translated = Description;



			if (OtherLanguages != null)
			{
				List<TranslateOtherLanguage> newTranslateItems = new List<TranslateOtherLanguage>();

				foreach (var item in OtherLanguages)
				{
					if (culture2LangIdMap.ContainsKey(item.Culture))
					{
						var langId = culture2LangIdMap[item.Culture];

						if (descriptionInfo.OtherLanguages != null)
						{
							var otherItem = descriptionInfo.OtherLanguages.Where(e => e.LanguageId == langId).FirstOrDefault();
							if (otherItem != null)
							{
								otherItem.Description = item.Description;
							}
							else
							{
								newTranslateItems.Add(new TranslateOtherLanguage() { LanguageId = langId, Description = item.Description });
							}
						}
						else
						{
							newTranslateItems.Add(new TranslateOtherLanguage() { LanguageId = langId, Description = item.Description });
						}
					}
				}

				if (newTranslateItems.Any())
				{
					if (descriptionInfo.OtherLanguages != null && descriptionInfo.OtherLanguages.Any())
					{
						newTranslateItems.AddRange(descriptionInfo.OtherLanguages);
					}

					descriptionInfo.OtherLanguages = newTranslateItems.ToList();
				}
			}

			return descriptionInfo;
		}
	}
}
