using Newtonsoft.Json;
using RIB.Visual.Platform.Common;
using System.Threading.Tasks;
using RIB.Visual.Platform.UnitTests.Common;
using Xunit;
using Xunit.Abstractions;
using RIB.Visual.Basics.Api.ServiceFacade.WebApi;
using Newtonsoft.Json.Linq;
using System.Linq;
using System;

namespace RIB.Visual.Basics.Api.UnitTests
{
	/// <summary>
	/// Unit tests for the BasicsApiSchemaController.
	/// </summary>
	/// <remarks>
	/// This class contains tests to verify the API schema and endpoint functionality.
	/// </remarks>
	/// <param name="output">The test output helper.</param>
	/// <param name="webApiHelper">The web API helper.</param>
	[AutoTest]
	[TestClassDescription("Api Schema Test")]
	public class BasicsApiSchemaControllerTest(ITestOutputHelper output, WebApiHelper webApiHelper) : WebApiEnabledTestClassBase(output, webApiHelper)
	{
		/// <summary>
		/// Tests the retrieval of API schemas for different versions.
		/// </summary>
		/// <param name="version">The API version to test.</param>
		/// <returns>A task representing the asynchronous operation.</returns>
		[Theory]
		[InlineData("")]
		[InlineData("1.0")]
		[InlineData("2.0")]
		public async Task Test_GetApiSchemas(string version)
		{
			var routes = new string[]
			{
				"model/publicapi/object",
				"basics/publicapi/unit"
			};

			var client = base.WebApi.Connect();
			var url = base.WebApi.BuildUrl($"basics/api/schema/1.0/list?version={version}");

			var response = client.PostJsonAndWait(url, JsonConvert.SerializeObject(routes));

			Assert.True(response.IsSuccessStatusCode);
			Assert.NotNull(response.Content);

			var content = await response.Content.ReadAsStringAsync();
			Assert.NotNull(content);
			Assert.NotEmpty(content);

			var routeInfos = JArray.Parse(content);

			Assert.NotNull(routeInfos);
			Assert.NotEmpty(routeInfos);
			Assert.Contains(routeInfos, x => routes.Any(r => x["base"].ToString().Contains(r, StringComparison.InvariantCultureIgnoreCase)));
		}

		/// <summary>
		/// Tests the checking of endpoints for different versions and deprecated status.
		/// </summary>
		/// <param name="version">The API version to test.</param>
		/// <param name="includeDeprecated">Whether to include deprecated endpoints in the check.</param>
		/// <returns>A task representing the asynchronous operation.</returns>
		[Theory]
		[InlineData("1.0", true)]
		[InlineData("1.0", false)]
		[InlineData("2.0", true)]
		[InlineData("2.0", false)]
		public async Task Test_CheckEndpoints(string version, bool includeDeprecated)
		{
			var routes = new CheckVersionRequest[]
			{
				new(){ Base = "basics/publicapi/clerk", Versions = [version] },
				new(){ Base = "model/publicapi/object", Versions = [version] },
				new(){ Base = "basics/publicapi/unit", Versions = [version] }
			};

			var client = base.WebApi.Connect();
			var url = base.WebApi.BuildUrl($"basics/api/schema/1.0/check?includeDeprecated={includeDeprecated}");

			var response = client.PostJsonAndWait(url, JsonConvert.SerializeObject(routes));

			Assert.True(response.IsSuccessStatusCode);
			Assert.NotNull(response.Content);

			var content = await response.Content.ReadAsStringAsync();
			Assert.NotNull(content);
			Assert.NotEmpty(content);

			var checkResponse = JsonConvert.DeserializeObject<CheckVersionResponse>(content, new JsonSerializerSettings()
			{
				MetadataPropertyHandling = MetadataPropertyHandling.Ignore
			});

			Assert.NotNull(checkResponse);
			Assert.True(checkResponse.Available);
		}
	}
}
