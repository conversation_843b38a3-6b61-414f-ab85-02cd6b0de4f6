
//using Microsoft.VisualStudio.TestTools.UnitTesting;

//namespace RIB.Visual.Basics.AuditTrail.UnitTests
//{
//	/// <summary>
//	///This is a test class for SequenceLogicTest and is intended
//	///to contain all SequenceLogicTest Unit Tests
//	///</summary>
//	[TestClass()]
//	public class TranslationTest
//	{

//		private TestContext _testContextInstance;

//		/// <summary>
//		///Gets or sets the test context which provides
//		///information about and functionality for the current test run.
//		///</summary>
//		public TestContext TestContext
//		{
//			get
//			{
//				return _testContextInstance;
//			}
//			set
//			{
//				_testContextInstance = value;
//			}
//		}

//		[TestMethod]
//		public void Name_Test()
//		{
//			TestCommon.InitializeForServerConnection();
//		}

//	}
//}
