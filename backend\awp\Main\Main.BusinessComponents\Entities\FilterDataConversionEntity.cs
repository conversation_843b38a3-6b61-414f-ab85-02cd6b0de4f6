using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Awp.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class FilterDataConversionEntity
	{
		/// <summary>
		/// 
		/// </summary>
		public List<RIB.Visual.Platform.Core.IdentificationData> LineItemIdData { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public List<RIB.Visual.Platform.Core.IdentificationData> ResourceIdData { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<Tuple<int, int>> ServicePackBoqHeaderIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<Tuple<int, int>> MaterialPackageBoqHeaderIds { get; set; }
	}
}
