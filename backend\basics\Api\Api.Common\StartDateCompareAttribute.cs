﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// 
	/// </summary>
	[AttributeUsage(AttributeTargets.Property)]
	public class StartDateCompareAttribute : ValidationAttribute
	{
		/// <summary>
		/// Contructor.
		/// </summary>
		public StartDateCompareAttribute(string endDateProperty)
		{
			if (string.IsNullOrEmpty(endDateProperty))
			{
				throw new ArgumentNullException("endDateProperty");
			}

			EndDateProperty = endDateProperty;
		}

		/// <summary>
		/// Gets or sets EndDateProperty.
		/// </summary>
		public string EndDateProperty
		{
			get;
			set;
		}

		/// <summary>
		/// Determines whether a specified object is valid.
		/// </summary>
		/// <param name="value">The object to validate.</param>
		/// <param name="validationContext">The validation context</param>
		/// <returns></returns>
		protected override ValidationResult IsValid(object value, ValidationContext validationContext)
		{
			ValidationResult result = ValidationResult.Success;

			if (value != null)
			{
				var endDateProperty = validationContext.ObjectType.GetProperty(EndDateProperty);
				if (endDateProperty != null)
				{
					if (validationContext.ObjectInstance != null)
					{
						var endDate = endDateProperty.GetValue(validationContext.ObjectInstance);
						if (endDate != null && endDate is DateTime)
						{
							var startDate = (DateTime)value;
							if ((startDate - (DateTime)endDate).Ticks > 0)
							{
								string errorMsg = string.Format("The [{0}(1)] must not be greater than [{2}(3)]", validationContext.MemberName, startDate, EndDateProperty, endDate);
								result = new ValidationResult(FormatErrorMessage(validationContext.MemberName), new List<string>() { validationContext.MemberName });
							}
						}
					}
				}
			}

			return result;
		}
	}
}
