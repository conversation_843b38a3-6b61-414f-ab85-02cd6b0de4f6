<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="RIB.Visual.Platform.AppServer.Console.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <appSettings>
    <clear />
  </appSettings>


	<connectionStrings>
		<add connectionString="Server=sql12-dev\dev;Database=iTWOCloud;Integrated Security=true" name="Default" />
		<add connectionString="Server=.\;Database=iTWOCloud;Integrated Security=true" name="Default.local" />
	</connectionStrings>

	<system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="Default" sendTimeout="00:20:00" bypassProxyOnLocal="true" maxBufferSize="10000000" maxBufferPoolSize="50000000" maxReceivedMessageSize="10000000">
          <readerQuotas maxStringContentLength="10000000" maxArrayLength="10000000" />
        </binding>
        <binding name="Default-Large" sendTimeout="00:30:00" bypassProxyOnLocal="true" maxBufferSize="50000000" maxBufferPoolSize="50000000" maxReceivedMessageSize="50000000">
          <readerQuotas maxStringContentLength="50000000" maxArrayLength="50000000" />
        </binding>
        <binding name="Streaming" sendTimeout="12:00:00" bypassProxyOnLocal="true" maxBufferSize="10000000" maxBufferPoolSize="50000000" maxReceivedMessageSize="2147483648" transferMode="Streamed">
          <readerQuotas maxStringContentLength="10000000" maxArrayLength="10000000" />
        </binding>
      </basicHttpBinding>
      <netTcpBinding>
        <binding name="Default" sendTimeout="00:20:00" maxBufferPoolSize="50000000" maxBufferSize="10000000" maxConnections="1000" maxReceivedMessageSize="10000000" portSharingEnabled="true">
          <readerQuotas maxStringContentLength="10000000" maxArrayLength="10000000" />
          <security mode="None" />
        </binding>
        <binding name="Default-Large" sendTimeout="00:30:00" maxBufferPoolSize="50000000" maxBufferSize="50000000" maxConnections="1000" maxReceivedMessageSize="50000000" portSharingEnabled="true">
          <readerQuotas maxStringContentLength="50000000" maxArrayLength="50000000" />
          <security mode="None" />
        </binding>
        <binding name="Streaming" sendTimeout="12:00:00" maxBufferPoolSize="50000000" maxBufferSize="10000000" maxConnections="1000" maxReceivedMessageSize="2147483648" transferMode="Streamed" portSharingEnabled="true">
          <readerQuotas maxStringContentLength="10000000" maxArrayLength="10000000" />
          <security mode="None" />
        </binding>
      </netTcpBinding>
    </bindings>

    <behaviors>
      <endpointBehaviors>
        <behavior name="Default-Large">
          <dataContractSerializer maxItemsInObjectGraph="1000000" />
        </behavior>
        <behavior name="Default">
          <dataContractSerializer maxItemsInObjectGraph="200000" />
        </behavior>
      </endpointBehaviors>
    </behaviors>

    <client>
      <endpoint address="net.tcp://localhost:4410/Services.Login" binding="netTcpBinding" bindingConfiguration="Default" behaviorConfiguration="Default" contract="RIB.Visual.Services.Login.ServiceDomain.IServiceDomain" name="Services.Login" />
      <endpoint address="net.tcp://localhost:4410/Services.Platform" binding="netTcpBinding" bindingConfiguration="Default" behaviorConfiguration="Default" contract="RIB.Visual.Services.Platform.ServiceDomain.IServiceDomain" name="Services.Platform" />
      <endpoint address="net.tcp://localhost:4410/Act.UserManagement" binding="netTcpBinding" bindingConfiguration="Default" behaviorConfiguration="Default" contract="RIB.Visual.Act.UserManagement.ServiceDomain.IServiceDomain" name="Act.UserManagement" />
      <endpoint address="net.tcp://localhost:4410/Services.Infrastructure" binding="netTcpBinding" bindingConfiguration="Default" behaviorConfiguration="Default" contract="RIB.Visual.Services.Infrastructure.ServiceDomain.IServiceDomain" name="Services.Infrastructure" />
      <endpoint address="net.tcp://localhost:4410/Services.Scheduler" binding="netTcpBinding" bindingConfiguration="Default" behaviorConfiguration="Default" contract="RIB.Visual.Services.Scheduler.ServiceDomain.IServiceDomain" name="Services.Scheduler" />
      <endpoint address="net.tcp://localhost:4410/Services.Notification" binding="netTcpBinding" bindingConfiguration="Default" behaviorConfiguration="Default" contract="RIB.Visual.Services.Notification.ServiceDomain.IServiceDomain" name="Services.Notification" />
    </client>

  </system.serviceModel>
</configuration>