﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Represent a complex type property which can be truncated. e.g. Object, [], Enumerable, etc.
	/// </summary>
	[AttributeUsage(AttributeTargets.Property)]
	public class TruncatableObjectAttribute : Attribute
	{
		/// <summary>
		/// Constructor
		/// </summary>
		public TruncatableObjectAttribute()
		{
		}
	}
}
