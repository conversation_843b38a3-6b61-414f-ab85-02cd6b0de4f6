using EntityFramework.Functions;
using EntityFrameworkExtras.EF6;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Platform.OperationalManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;

namespace RIB.Visual.Basics.AuditTrail.BusinessComponents.Logic
{
    /// <summary>
    /// 
    /// </summary>
    public class BasicsAuditTrailLogic : RVPBizComp.LogicBase
    {
        /// <summary>
        /// Get List of Audit Report Entity as per provided Filter
        /// </summary>
        /// <param name="filter">Filter params from UI</param>
        /// <returns></returns>
        public IDictionary<string, object> GetList(AudFilterEntity filter)
        {
            var result = new List<AudReportEntity>();
            var udttContainerList = new List<BasicsAuditTrailUdttContainerList>();
            var udttColumnList = new List<BasicsAuditTrailUdttColumnList>();
            IDictionary<string, object> returnResult = new Dictionary<string, object>();

            if (filter.ContainerList != null && filter.ContainerList.Any())
            {
                foreach (var container in filter.ContainerList)
                {
                    if (string.IsNullOrEmpty(container) == false)
                    {
                        udttContainerList.Add(new BasicsAuditTrailUdttContainerList() { ListItem = container });
                    }
                }
            }

            if (filter.ColumnList != null && filter.ColumnList.Any())
            {
                foreach (var column in filter.ColumnList)
                {
                    if (string.IsNullOrEmpty(column) == false)
                    {
                        udttColumnList.Add(new BasicsAuditTrailUdttColumnList() { ListItem = column });
                    }
                }
            }

            using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
            {
                var getAuditTrailReportDataSP = new BasicsAuditTrailGetReportingDataProc()
                {
                    ObjectFK = filter.ObjectFk,
                    RecordFK = filter.RecordFk,
                    DateFrom = filter.DateFrom,
                    DateTo = filter.DateTo,
                    UdttContainerList = udttContainerList,
                    UdttColumnList = udttColumnList,
                    Action = filter.Action,
                    PageNumber = filter.PageNumber,
                    LogOnNameContains = (string.IsNullOrEmpty(filter.LogOnNameContains) ? null : filter.LogOnNameContains)
                };

                try
                {
                    result = (List<AudReportEntity>)dbcontext.ObjectContext().ExecuteStoredProcedure<AudReportEntity>(getAuditTrailReportDataSP);
                }
                catch (Exception ex)
                {
                    if (ex.Message.Contains("Could not find stored procedure"))
                    {
                        throw new BusinessLayerException("Please refer Audit Trail Deployment Document and perform all Steps for deployment. Exception Details : " + ex.Message);
                    }
                    else
                    {
                        throw new BusinessLayerException(ex.Message);
                    }
                }

                returnResult.Add("AudReportEntity", result);
                returnResult.Add("CurrentPageNo", filter.PageNumber);
                returnResult.Add("PageSize", getAuditTrailReportDataSP.PageSize);
                returnResult.Add("LastPageNo", getAuditTrailReportDataSP.LastPageNo);
                returnResult.Add("TotalRecords", getAuditTrailReportDataSP.TotalRecords);
            }

            return returnResult;
        }

        #region helper

        private string GetUsersCulture()
        {
            string defaultCulture;
            LanguageLogic languageLogic = new LanguageLogic();
            string culture = languageLogic.GetCultureByLanguageId(this.UserLanguageId, out defaultCulture);
            return culture != String.Empty ? culture : defaultCulture;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logTableName"></param>
        /// <param name="tableName"></param>
        /// <param name="objectFk"></param>
        /// <returns></returns>
        private string BuildSqlQuery(string logTableName, string tableName, int objectFk)
        {

            string select = String.Format(
                                @"SELECT a.[ID] AS [Id]
									,a.[DB_ACTION_TYPE] AS [DbActionType]
									,a.[OBJECT_FK] AS [ObjectFk] 
									,[ID_UPDATED] AS [IdUpdated] 
									,[OLD_VALUE] AS [OldValue] 
									,[NEW_VALUE] AS [NewValue] 
									,a.[UPDATED] AS [UpdatedAt] 
									,a.[WHOUPD] AS [UpdatedBy] 
									,usr.[NAME] as [UserName]
									,ISNULL(b.[UILABELTEXT],a.[COLUMN_NAME]) as UiColumnName
									,ISNULL(c.[UILABELTEXT],a.[TABLE_NAME]) as UiTableName
								FROM {0} a LEFT OUTER JOIN BAS_DDCOLUMNTRANSLATION b ON a.[TABLE_NAME] +'.' + a.[COLUMN_NAME] = b.[TABLECOLUMNINFO] AND b.[CULTURE] = '{1}'
								LEFT OUTER JOIN BAS_DDTABLETRANSLATION c ON a.[TABLE_NAME] = c.[TABLE_NAME] AND c.[CULTURE] = '{1}'
								LEFT OUTER JOIN FRM_USER usr ON usr.[ID]=a.[WHOUPD]", logTableName, GetUsersCulture());

            string where = "WHERE a.[TABLE_NAME]='" + tableName + "' and [OBJECT_FK]=" + objectFk;
            string orderby = "order by a.[UPDATED] desc";

            string sql = String.Format("{0} {1} {2}", select, where, orderby);
            return sql;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="internalName"></param>
        /// <returns></returns>
        private ModuleEntity GetModuleByName(string internalName)
        {
            ModuleLogic logic = new ModuleLogic();
            return logic.GetByInternalName(internalName);
        }


        #endregion

        #region CRUD opertations for Containers

        /// <summary>
        /// Get all Containers
        /// </summary>
        /// <returns></returns>
        public List<AudContainerEntity> GetAllContainers()
        {
            //to do : set permission for the method
            //RVPBizComp.Permission.Ensure("50593feea9fe4280b36f72e27c8dfda1", Permissions.Read);

            //do we need pagination?
            List<AudContainerEntity> entities;

            using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
            {
                entities = dbcontext.Entities<AudContainerEntity>().ToList();
            }

            return entities;
        }

        /// <summary>
        /// Retrive all container by Container description
        /// </summary>
        /// <param name="DescriptionContains">List of all Audit container by description</param>
        /// <returns></returns>
        public List<AudContainerEntity> GetAllContainersByContainerDescription(string DescriptionContains)
        {
            //to do : set permission for the method
            //RVPBizComp.Permission.Ensure("50593feea9fe4280b36f72e27c8dfda1", Permissions.Read);

            List<AudContainerEntity> entities;

            using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
            {
                entities = dbcontext.Entities<AudContainerEntity>().Where(e => e.DescriptionInfo.Description.Contains(DescriptionContains)).ToList();
            }

            return entities;
        }

        /// <summary>
        /// Deletes a container
        /// </summary>
        /// <param name="container">The element to be deleted</param>
        public void DeleteContainer(AudContainerEntity container)
        {
            //to do : set permission for the method
            //RVPBizComp.Permission.Ensure("50593feea9fe4280b36f72e27c8dfda1", Permissions.Delete);

            using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
            {
                dbcontext.Delete(container);
            }
        }

        #endregion
    }
}
