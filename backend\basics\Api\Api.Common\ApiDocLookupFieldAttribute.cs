﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Provides meta info from the webapi method to help webapihelp to generate docs automatically.
	/// </summary>
	[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
	public class ApiDocLookupFieldAttribute : Attribute
	{
		/// <summary>
		/// Constructor.
		/// </summary>
		public ApiDocLookupFieldAttribute(string lookupType, int order = 1)
		{
			this.LookupType = lookupType;
			this.Order = order;
		}

		/// <summary>
		/// Specify the LookupType
		/// </summary>
		public string LookupType { get; set; }

		/// <summary>
		/// Specify the order
		/// </summary>
		public int Order { get; set; }

		/// <summary>
		/// Specify the HasDefault
		/// </summary>
		public bool HasDefault { get; set; }

		/// <summary>
		/// Specify the ThrowExceptionWhenNotFound
		/// </summary>
		public bool ThrowExceptionWhenNotFound { get; set; }

	}
}
