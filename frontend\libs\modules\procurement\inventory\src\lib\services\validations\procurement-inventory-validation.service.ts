/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable, inject } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import {
	ValidationInfo,
	ValidationResult,
	IValidationFunctions,
	IEntityRuntimeDataRegistry
} from '@libs/platform/data-access';
import { ProcurementBaseValidationService } from '@libs/procurement/shared';
import { BasicsSharedMaterialLookupService } from '@libs/basics/shared';
import { IPrcInventoryEntity } from '../../model/entities/prc-inventory-entity.interface';
import { ProcurementInventoryGridDataService } from '../procurement-inventory-grid-data.service';
import { ProcurementInventoryHeaderDataService } from '../procurement-inventory-header-data.service';

/**
 * Procurement Inventory validation service
 */
@Injectable({
	providedIn: 'root'
})
export class ProcurementInventoryValidationService extends ProcurementBaseValidationService<IPrcInventoryEntity> {
	
	private readonly materialLookupService = inject(BasicsSharedMaterialLookupService);
	
	/**
	 * Constructor
	 * @param dataService The inventory data service
	 * @param headerDataService The inventory header data service
	 */
	public constructor(
		private readonly dataService: ProcurementInventoryGridDataService,
		private readonly headerDataService: ProcurementInventoryHeaderDataService
	) {
		super();
	}

	protected generateValidationFunctions(): IValidationFunctions<IPrcInventoryEntity> {
		return {
			MdcMaterialFk: this.validateMdcMaterialFk,
			ActualQuantity: this.validateActualQuantity,
			Price: this.validatePrice,
			RecordedQuantity: this.validateRecordedQuantity,
			RecordedUomFk: this.validateRecordedUomFk,
			BasUomFk: this.validateBasUomFk,
			ClerkFk1: this.validateClerkFk1,
			ClerkFk2: this.validateClerkFk2,
			Quantity1: this.validateQuantity1,
			Quantity2: this.validateQuantity2
		};
	}

	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IPrcInventoryEntity> {
		return this.dataService;
	}

	/**
	 * Validates material selection and handles duplicate materials
	 */
	private async validateMdcMaterialFk(info: ValidationInfo<IPrcInventoryEntity>): Promise<ValidationResult> {
		const { entity, value, field } = info;
		const fieldName = field === 'MdcMaterialFk'
			? this.translationService.instant('procurement.inventory.mdcmaterialfk').toString()
			: 'Material';

		// Check if mandatory
		const mandatoryResult = this.validationUtils.isMandatory(info);
		if (!mandatoryResult.valid) {
			return mandatoryResult;
		}

		// Check for zero value
		if (value === 0) {
			(entity as any).Material2Uoms = null;
			return new ValidationResult(
				this.translationService.instant('cloud.common.emptyOrNullValueErrorMessage', { fieldName }).toString()
			);
		}

		// Check for duplicate materials
		const items = this.dataService.getList().filter(item => item.Id !== entity.Id);
		const existingItem = items.find(item => item.MdcMaterialFk === value);
		if (existingItem) {
			return new ValidationResult(
				this.translationService.instant('procurement.inventory.duplicatematerailmsg', { fieldName }).toString()
			);
		}

		// Load material data and update entity
		try {
			const material = await firstValueFrom(
				this.materialLookupService.getItemByKey({ id: value as number })
			);
			
			if (material) {
				await this.updateEntityWithMaterialData(entity, material, value as number);
			}
		} catch (error) {
			console.error('Error loading material data:', error);
		}

		return new ValidationResult();
	}

	/**
	 * Updates entity with material data and calculates related values
	 */
	private async updateEntityWithMaterialData(
		entity: IPrcInventoryEntity, 
		material: any, 
		materialFk: number
	): Promise<void> {
		const selectedHeader = this.headerDataService.getSelectedEntity();
		if (!selectedHeader) {
			throw new Error('Should have selected parent entity');
		}

		// Update entity with material data
		entity.MdcMaterialFk = materialFk;
		entity.CatalogFk = material.MdcMaterialCatalogFk;
		entity.BasUomFk = entity.RecordedUomFk = material.BasUomFk;
		(entity as any).Material2Uoms = material.Material2Uoms;

		// Validate UOM fields
		this.validateBasUomFk({ entity, value: entity.BasUomFk, field: 'BasUomFk' });
		this.validateRecordedUomFk({ entity, value: entity.RecordedUomFk, field: 'RecordedUomFk' });

		// Generate inventory data by material
		try {
			const response = await this.http.get(
				`procurement/inventory/generatebymaterial?prjStockFk=${selectedHeader.PrjStockFk}&materialFk=${materialFk}`
			);

			if (response) {
				this.updateEntityWithGeneratedData(entity, response, material, selectedHeader);
			}
		} catch (error) {
			console.error('Error generating inventory data:', error);
		}
	}

	/**
	 * Updates entity with generated inventory data
	 */
	private updateEntityWithGeneratedData(
		entity: IPrcInventoryEntity, 
		data: any, 
		material: any, 
		selectedHeader: any
	): void {
		if (data) {
			// Update with existing stock data
			entity.PrjStockLocationFk = data.PrjStockLocationFk;
			entity.LotNo = data.LotNo;
			entity.ExpirationDate = data.ExpirationDate;
			entity.PpsProductFk = data.PpsProductFk;
			entity.StockQuantity = data.StockQuantity;
			entity.StockTotal = data.StockTotal;
			entity.StockProvisionTotal = data.StockProvisionTotal;
			entity.RecordedQuantity = entity.ActualQuantity = data.ActualQuantity;
			entity.ActualTotal = data.ActualTotal;
			entity.ActualProvisionTotal = data.ActualProvisionTotal;
		} else {
			// Calculate price using exchange rate
			this.calculatePriceWithExchangeRate(entity, material, selectedHeader);
		}

		this.dataService.setModified(entity);
	}

	/**
	 * Calculates price with exchange rate
	 */
	private async calculatePriceWithExchangeRate(
		entity: IPrcInventoryEntity, 
		material: any, 
		selectedHeader: any
	): Promise<void> {
		try {
			const rate = await this.getForeignToDocExchangeRate(
				selectedHeader.StockCurrencyFk,
				material.BasCurrencyFk,
				selectedHeader.StockProjectFk
			);

			if (material && rate) {
				const price = material.Cost / (material.PriceUnit * material.FactorPriceUnit) / rate;
				entity.Price = price;
				entity.ActualTotal = price * (entity.ActualQuantity || 0);
				
				this.dataService.setModified(entity);
			}
		} catch (error) {
			console.error('Error calculating price with exchange rate:', error);
		}
	}

	/**
	 * Gets foreign to document exchange rate
	 */
	private async getForeignToDocExchangeRate(
		documentCurrencyFk: number,
		currencyForeignFk: number,
		projectFk: number
	): Promise<number> {
		if (currencyForeignFk === documentCurrencyFk) {
			return 1;
		}

		try {
			const response = await this.http.get('procurement/common/exchangerate/ocrate', {
				params: {
					CurrencyForeignFk: currencyForeignFk.toString(),
					DocumentCurrencyFk: documentCurrencyFk.toString(),
					ProjectFk: projectFk.toString()
				}
			});
			return (response as { data: number }).data;
		} catch (error) {
			console.error('Error getting exchange rate:', error);
			return 1;
		}
	}

	/**
	 * Validates base UOM
	 */
	private validateBasUomFk(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value, field } = info;

		if (value === undefined || value === null || value === -1) {
			const uom = this.translationService.instant('cloud.common.entityUoM');
			const errorMsg = this.translationService.instant('cloud.common.emptyOrNullValueErrorMessage', { fieldName: uom });
			return new ValidationResult(errorMsg.toString());
		}

		// Set readonly state based on material selection
		const readOnly = !!entity.MdcMaterialFk;
		this.dataService.setEntityReadOnlyFields(entity, [{ field, readOnly }]);

		return new ValidationResult();
	}

	/**
	 * Validates actual quantity
	 */
	private validateActualQuantity(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value } = info;

		if ((value as number) < 0) {
			const errorMsg = this.translationService.instant('procurement.inventory.quantityNoNegative');
			return new ValidationResult(errorMsg.toString());
		}

		// Handle stock update warning
		if (entity.IsFromExistStock) {
			const errorMsg = this.translationService.instant('procurement.inventory.updateactualwarnning');
			const warningResult = new ValidationResult(errorMsg.toString());
			// Apply validation result to ActualTotal field
			this.dataService.addInvalid(entity, {
				field: 'ActualTotal',
				validationResult: warningResult
			} as any);
		} else {
			entity.ActualTotal = (entity.Price || 0) * (value as number);
			if (value === 0) {
				entity.ActualProvisionTotal = 0;
			}
			this.dataService.setModified(entity);
		}

		// Convert quantity
		this.conversionQuantity(entity, entity.RecordedUomFk, value as number, null);
		
		return new ValidationResult();
	}

	/**
	 * Validates price
	 */
	private validatePrice(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value } = info;

		if (!entity.IsFromExistStock) {
			entity.ActualTotal = (value as number) * (entity.ActualQuantity || 0);
			this.dataService.setModified(entity);
		}

		return new ValidationResult();
	}

	/**
	 * Validates recorded quantity
	 */
	private validateRecordedQuantity(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value } = info;
		this.conversionQuantity(entity, entity.RecordedUomFk, null, value as number);
		return new ValidationResult();
	}

	/**
	 * Validates recorded UOM
	 */
	private validateRecordedUomFk(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value, field } = info;

		const readOnly = (!!entity.MdcMaterialFk && !(entity as any).Material2Uoms);
		this.dataService.setEntityReadOnlyFields(entity, [
			{ field, readOnly },
			{ field: 'RecordedQuantity', readOnly }
		]);

		this.conversionQuantity(entity, value as number, entity.ActualQuantity, null);
		return new ValidationResult();
	}

	/**
	 * Validates clerk 1
	 */
	private validateClerkFk1(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value } = info;

		const readonly = value === null;
		if (readonly) {
			entity.Quantity1 = null;
			entity.ClerkFk1 = null;
			this.validateQuantity1({ entity, value: entity.Quantity1, field: 'Quantity1' });
		}

		this.dataService.setEntityReadOnlyFields(entity, [{ field: 'Quantity1', readOnly: readonly }]);
		return new ValidationResult();
	}

	/**
	 * Validates clerk 2
	 */
	private validateClerkFk2(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value } = info;

		const readonly = value === null;
		if (readonly) {
			entity.Quantity2 = null;
			entity.ClerkFk2 = null;
			this.validateQuantity2({ entity, value: entity.Quantity2, field: 'Quantity2' });
		}

		this.dataService.setEntityReadOnlyFields(entity, [{ field: 'Quantity2', readOnly: readonly }]);
		return new ValidationResult();
	}

	/**
	 * Validates quantity 1
	 */
	private validateQuantity1(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value } = info;

		if (entity.ClerkFk2 === null) {
			if (entity.ClerkFk1 !== null) {
				entity.ActualQuantity = value as number;
			}
		} else {
			if (value !== null) {
				if (entity.Quantity2 !== null && value !== entity.Quantity2) {
					entity.ActualQuantity = null;
				} else {
					entity.ActualQuantity = value as number;
				}
			} else {
				if (value !== entity.Quantity2) {
					entity.ActualQuantity = entity.Quantity2;
				}
			}
		}

		entity.Quantity1 = value as number;
		this.checkDifferenceClerkQuantity(entity);
		this.dataService.setModified(entity);

		return new ValidationResult();
	}

	/**
	 * Validates quantity 2
	 */
	private validateQuantity2(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value } = info;

		if (entity.ClerkFk1 === null) {
			if (entity.ClerkFk2 !== null) {
				entity.ActualQuantity = value as number;
			}
		} else {
			if (value !== null) {
				if (entity.Quantity1 !== null && value !== entity.Quantity1) {
					entity.ActualQuantity = null;
				} else {
					entity.ActualQuantity = value as number;
				}
			} else {
				if (value !== entity.Quantity1) {
					entity.ActualQuantity = entity.Quantity1;
				}
			}
		}

		entity.Quantity2 = value as number;
		this.checkDifferenceClerkQuantity(entity);
		this.dataService.setModified(entity);

		return new ValidationResult();
	}

	/**
	 * Checks difference between clerk quantities
	 */
	private checkDifferenceClerkQuantity(entity: IPrcInventoryEntity): void {
		if (entity.Quantity1 !== null || entity.Quantity2 !== null) {
			if (entity.Quantity1 !== null && entity.Quantity2 !== null) {
				entity.DifferenceClerkQuantity = (entity.Quantity1 || 0) - (entity.Quantity2 || 0);
			} else {
				if (entity.Quantity1 !== null) {
					entity.DifferenceClerkQuantity = entity.Quantity1;
				} else {
					entity.DifferenceClerkQuantity = entity.Quantity2;
				}
			}
		} else {
			entity.DifferenceClerkQuantity = null;
		}
	}

	/**
	 * Converts quantity between different UOMs
	 */
	private conversionQuantity(
		entity: IPrcInventoryEntity,
		uom: number | null | undefined,
		quantity: number | null,
		recordedQuantity: number | null
	): void {
		let value = 1;

		if ((entity as any).Material2Uoms && uom) {
			const uomItem = (entity as any).Material2Uoms.find((item: any) => item.UomFk === uom);
			if (uomItem) {
				value = uomItem.Quantity;
			}
		}

		if (quantity !== null && quantity !== undefined) {
			entity.RecordedQuantity = quantity * value;
		}

		if (recordedQuantity !== null && recordedQuantity !== undefined) {
			entity.ActualQuantity = value === 0 ? 0 : recordedQuantity / value;
		}
	}
}
