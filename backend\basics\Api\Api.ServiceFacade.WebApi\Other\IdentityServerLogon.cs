using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using IdentityModel.Client;
using Newtonsoft.Json;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Platform.Server.Common;
using RIB.Visual.Platform.ServerCommon;
using Convert = System.Convert;
// ReSharper disable IdentifierTypo

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{

	/// <summary/>
	public class IdentityServerLogon
	{
		
		/// <summary>
		/// read identity Server url from web.config
		/// </summary>
		/// <returns></returns>
		private string ReadIdentityServerUrl()
		{
			// rei@4.7.19 make sure authority and issue are lower case
			CertificateFactory.ReadAuthorityFromConfig(out var authority, out _);
			
			var completeUrl = authority + (authority.EndsWith("/") ? "" : "/") + "connect/token";

			//AppDomain.CurrentDomain.
			//		<add key="identity:authority" value="https://rib-s-itwocld5d.rib-software.com/identity/core/"/>
			return completeUrl;

		}


		/// <summary>
		/// This method requests a token from iTWO Cloud identity server via username/password
		/// 
		/// clientId and clientSecret are const values and required for communication with identity server.
		/// 
		/// </summary>
		/// <param name="username"></param>
		/// <param name="password"></param>
		/// <param name="idsrv"></param>
		/// <returns></returns>
		private TokenResponse RequestToken(string username, string password, string idsrv)
		{
			const string clientId = "itwo40.wwebapi";
			var clientSecret = AppSettingsReader.ReadString("aes:key");
			const string scope = "default";

			var client = new HttpClient();

			try
			{
				var ret = client.RequestPasswordTokenAsync(new PasswordTokenRequest
				{
					Address = idsrv,
					ClientId = clientId,
					ClientSecret = clientSecret,
					UserName = username,
					Password = password,
					Scope = scope
				}).Result;
				return ret;
			}
			catch (Exception ex)
			{
				var msg = $"Request Token from IdentityServer4 failed {idsrv}";
				throw new BusinessLayerException(msg, ex);
			}
		}

		private string StringToBase64(string theString)
		{
			var encodedByte = Encoding.ASCII.GetBytes(theString);
			var base64Encoded = Convert.ToBase64String(encodedByte);
			return base64Encoded;
		}

		/// <summary>
		/// This method requests a token from iTWO Cloud identity server via username/password
		/// 
		/// clientId and clientSecret are const values and required for communication with identity server.
		/// 
		/// </summary>
		/// <param name="logonName"></param>
		/// <param name="identityProvider"></param>
		/// <returns></returns>
		public TokenResponse SsoRequestToken(string logonName, int identityProvider)
		{
			var identityServerUrl = ReadIdentityServerUrl();
			var clientSecret = AppSettingsReader.ReadString("aes:key");

			const string clientId = "itwo40.sso";
			const string scope = "default";

			var arcValue = "logonname:" + StringToBase64(logonName) + " idpid:" + StringToBase64(identityProvider.ToString()); // rei@15.3.19 changed "," delimiter for idsrv4 compatibility
			var extraData = new Parameters() { { "acr_values", arcValue } };

			//throw new Exception("rei@10.12.21 extraData not implemented yet, must be investigated how to pass parameter like acr_values to client");

			var client = new HttpClient();
			var ret = client.RequestClientCredentialsTokenAsync(new ClientCredentialsTokenRequest
			{
				Address = identityServerUrl,
				ClientId = clientId,
				ClientSecret = clientSecret,
				Scope = scope,
				Parameters = extraData
			}).Result;
			return ret;
		}

		/// <summary>
		/// </summary>
		/// <param name="logonName"></param>
		/// <param name="roleid">the permission role id, required if configured</param>
		/// <returns></returns>
		public TokenResponse Logonname2JwtRequestToken(string logonName,int? roleid)
		{
			var identityServerUrl = ReadIdentityServerUrl();
			var clientSecret = AppSettingsReader.ReadString("aes:key");

			const string clientId = "itwo40.webapi";
			const string scope = "default";

			var theRoleid = roleid.HasValue ? roleid.Value.ToString() : "";
			var arcValue = "logonname:" + StringToBase64(logonName) + " logonname2jwt:true" + " roleid:"+ theRoleid;
			var extraData = new Parameters() { { "acr_values", arcValue } };
			//throw new Exception("rei@10.12.21 extraData not implemented yet, must be investigated how to pass parameter like acr_values to client");

			var client = new HttpClient();
			var ret = client.RequestClientCredentialsTokenAsync(new ClientCredentialsTokenRequest
			{
				Address = identityServerUrl,
				ClientId = clientId,
				ClientSecret = clientSecret,
				Scope = scope,
				Parameters = extraData
			}).Result;
			return ret;
		}

		

		/// <summary>
		/// This method requests a token from iTWO Cloud identity server via username/password
		/// 
		/// clientId and clientSecret are const values and required for communication with identity server.
		/// 
		/// </summary>
		/// <param name="logonName"></param>
		/// <param name="password"></param>
		/// <returns></returns>
		public TokenResponse CreateShortTermToken(string logonName, string password)
		{
			var identityServerUrl = ReadIdentityServerUrl();
			var clientSecret = AppSettingsReader.ReadString("aes:key");// read from web.config
			const string clientId = "itwo40.sso";
			const string scope = "default";

			var arcValue = "logonname:" + StringToBase64(logonName) + " password:" + StringToBase64(password) + " shorttermtoken:request"; // rei@15.3.19 changed "," delimiter for idsrv4 compatibility
			var extraData = new Parameters() { { "acr_values", arcValue } };
			// throw new Exception("rei@10.12.21 extraData not implemented yet, must be investigated how to pass parameter like acr_values to client");

			var client = new HttpClient();
			var ret = client.RequestClientCredentialsTokenAsync(new ClientCredentialsTokenRequest
			{
				Address = identityServerUrl,
				ClientId = clientId,
				ClientSecret = clientSecret,
				Scope = scope,
				Parameters = extraData
			}).Result;
			return ret;
		}

		/// <summary>
		/// This method requests a token from iTWO Cloud identity server via username/password
		/// 
		/// clientId and clientSecret are const values and required for communication with identity server.
		/// 
		/// </summary>
		/// <param name="shortTermToken"></param>
		/// <returns></returns>
		public TokenResponse CreateAccessTokenFromShortTermToken(string shortTermToken)
		{
			var identityServerUrl = ReadIdentityServerUrl();
			var clientSecret = AppSettingsReader.ReadString("aes:key");// read from web.config

			const string clientId = "itwo40.sso";
			const string scope = "default";

			var arcValue = "fromshorttermtoken:" + shortTermToken;
			var extraData = new Parameters() { {"acr_values",arcValue } };
			//throw new Exception("rei@10.12.21 extraData not implemented yet, must be investigated how to pass parameter like acr_values to client");
			var client = new HttpClient();
			var ret = client.RequestClientCredentialsTokenAsync(new ClientCredentialsTokenRequest
			{
				Address = identityServerUrl,
				ClientId = clientId,
				ClientSecret = clientSecret,
				Scope = scope,
				Parameters = extraData
			}).Result;
			return ret;
		}

		private X509Certificate GetCertificate(string thumbPrint)
		{
			X509Store store = new X509Store(StoreName.My, StoreLocation.LocalMachine);
			store.Open(OpenFlags.ReadOnly);
			X509Certificate2Collection certs = store.Certificates.Find(X509FindType.FindByThumbprint, thumbPrint, false);
			store.Close();
			return certs[0];
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="logonName"></param>
		/// <param name="identityProvider"></param>
		/// <returns></returns>
		public TokenResponse SsoRequestTokenCertificate(string logonName, int identityProvider)
		{
			var identityServerUrl = ReadIdentityServerUrl();
			var certThumbPrint = AppSettingsReader.ReadString("identity:x509Thumbprint");// read from web.config

			const string clientId = "itwo40.sso";
			const string scope = "default";

			var arcValue = "logonname:" + StringToBase64(logonName) + " idpid:" + StringToBase64(identityProvider.ToString()); // rei@15.3.19 changed "," delimiter for idsrv4 compatibility
			var extraData = new Parameters() { {"acr_values",arcValue } };

			var handler = new HttpClientHandler();
			handler.ClientCertificateOptions = ClientCertificateOption.Manual;
			handler.SslProtocols = SslProtocols.None | SslProtocols.Tls12 | SslProtocols.Tls13;
			var cert = GetCertificate(certThumbPrint);
			handler.ClientCertificates.Add(cert);
			var client = new HttpClient(handler);

			//throw new Exception("rei@10.12.21 extraData not implemented yet, must be investigated how to pass parameter like acr_values to client");

			var ret = client.RequestClientCredentialsTokenAsync(new ClientCredentialsTokenRequest
			{
				Address = identityServerUrl,
				ClientId = clientId,
				Scope = scope,
				Parameters = extraData  
			}).Result;
			return ret;
		}


		/// <summary>
		/// 
		/// </summary>
		/// <param name="username"></param>
		/// <param name="password"></param>
		/// <returns></returns>
		public TokenResponse Login(string username, string password)
		{
			var identityServerUrl = ReadIdentityServerUrl();
			var token = RequestToken(username, password, identityServerUrl);

			if (token != null && token.IsError)
			{
				throw new AccessViolationException(string.Format("API Logon failed to Error: {1}; Reason: {2};IdentityServer Url: {0};",
					identityServerUrl, token.Error, token.Json.ToString()));
					//(token.Json)?token.Json.ToString() : token.HttpErrorReason));
			}
			return token;
		}


		/// <summary>
		/// This method is used for logon into itwo4.0 system via Username Password combination
		/// If successful you will receive a valid token.
		/// </summary>
		/// <param name="username"></param>
		/// <param name="password"></param>
		/// <returns></returns>
		public string ApiLogon(string username, string password)
		{
			var token = Login(username, password);
			return token.AccessToken;
		}

	}
}