using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.Server.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.IO;
using System.Threading;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// 
	/// </summary>
	public class ImportContentFileLogger : IImportContentLogger
	{
		private int Capacity = 1000;

		// config for logger.
		private ImportMasterDataContext _importMasterDataContext;

		private BlockingCollection<PublicApiValidationResult> _validationResults = null;
		private Task _processTask = null;
		private Task _flushTask = null;
		private bool _isStarted = false;
		private bool _hasError = false;
		private int _newCount = 0;

		private int _flushInterval = 5;

		private StreamWriter _streamWriter = null;

		/// <summary>
		/// Constructor.
		/// </summary>
		public ImportContentFileLogger()
		{
			_flushInterval = AppSettingsReader.ReadInt("importcontent:flushInSecond", _flushInterval);
			this._validationResults = new BlockingCollection<PublicApiValidationResult>();
			this.ValidationResults = new List<PublicApiValidationResult>();
		}

		private void SetLoggerWriter(ImportMasterDataContext context)
		{
			var downloadPath = AppSettingsReader.ReadString("fileserver:downloadsPath");
			var importContentLogPath = Path.Combine(downloadPath, "importcontent");
			if (!Directory.Exists(importContentLogPath))
			{
				Directory.CreateDirectory(importContentLogPath);
			}

			var logFileName = string.Format("bas_import_content_log_{0}_{1}.log", context.TaskId, Guid.NewGuid().ToString("N"));

			var logFullPath = Path.Combine(importContentLogPath, logFileName);

			this._streamWriter = new StreamWriter(logFullPath, false, Encoding.UTF8, 65536);
		}

		private void WriteLog2Buffer(PublicApiValidationResult log)
		{
			var formatedLog = FormatLog(log);
			this._streamWriter.WriteLine(formatedLog);
		}

		private string FormatLog(PublicApiValidationResult log)
		{
			return $"{log.LoggedTime.ToString("O")} [{log.MessageType}] {log.ErrorContent}";
		}

		private void FlushLog2Disk()
		{
			try
			{
				if (_newCount > 0 && this._streamWriter != null)
				{
					this._streamWriter.Flush();
					this._newCount = 0;
				}

			}
			catch (Exception e)
			{
				Console.WriteLine(e);
			}

		}

		private void CloseLogFileStream()
		{
			try
			{
				if (this._streamWriter != null)
					this._streamWriter.Close();
			}
			catch (Exception e)
			{
				Console.WriteLine(e);
			}

		}

		/// <summary>
		/// 
		/// </summary>
		public void StartWrite(ImportMasterDataContext context)
		{
			if (_isStarted)
			{
				return;
			}
			this._importMasterDataContext = context;
			this.SetLoggerWriter(context);
			this._isStarted = true;
			this._processTask = Task.Factory.StartNew(() =>
			{
				try
				{
					var logList = new List<PublicApiValidationResult>();
					foreach (var log in _validationResults.GetConsumingEnumerable())
					{
						if (logList.Count >= Capacity)
						{
							_validationResults.CompleteAdding();
							break;
						}

						logList.Add(log);

						WriteLog2Buffer(log);
					}
					this.ValidationResults = logList;
				}
				catch (Exception e)
				{
					WriteLog2Buffer(new PublicApiValidationResult() { IsValid = false, MessageType = ValidationMessageType.Error, ErrorContent = e.Message });
					FlushLog2Disk();
				}
			});


			this._flushTask = Task.Factory.StartNew(() =>
			{
				while (!this._validationResults.IsCompleted)
				{
					try
					{
						Thread.Sleep(_flushInterval * 1000);
						FlushLog2Disk();
					}
					catch (Exception e)
					{
						WriteLog2Buffer(new PublicApiValidationResult() { IsValid = false, MessageType = ValidationMessageType.Error, ErrorContent = e.Message });
						FlushLog2Disk();
					}
				}
			});
		}

		/// <summary>
		/// 
		/// </summary>
		public void FinishAndWaitCompleted()
		{
			if (!this._validationResults.IsAddingCompleted)
			{
				this._validationResults.CompleteAdding();
			}

			this._processTask.Wait();

			this._flushTask.Wait();

			FlushLog2Disk();
			CloseLogFileStream();

			Console.WriteLine("IsCompleted: {0}.", this._validationResults.IsCompleted);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public bool HasError()
		{
			return this._hasError;
		}

		#region Properties

		/// <summary>
		/// The validation result.
		/// </summary>
		public IEnumerable<PublicApiValidationResult> ValidationResults { get; set; }


		#endregion

		#region IPublicApiExecutionLogger members

		/// <summary>
		/// Writes warning message.
		/// </summary>
		/// <param name="message"></param>
		public void WriteWarning(string message)
		{
			WriteLog(true, ValidationMessageType.Warning, message);
		}

		/// <summary>
		/// Writes info message.
		/// </summary>
		/// <param name="message"></param>
		public void WriteInfo(string message)
		{
			WriteLog(true, ValidationMessageType.Information, message);
		}

		/// <summary>
		/// Writes error message.
		/// </summary>
		/// <param name="message"></param>
		public void WriteError(string message)
		{
			WriteLog(false, ValidationMessageType.Error, message);
		}

		/// <summary>
		/// Writes debug message.
		/// </summary>
		/// <param name="message"></param>
		public void WriteDebug(string message)
		{
			WriteLog(true, ValidationMessageType.Debug, message);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="ex"></param>
		public void WriteException(Exception ex)
		{
			WriteLog(false, ValidationMessageType.Error, ex.Message + ex.StackTrace);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="isValid"></param>
		/// <param name="messageType"></param>
		/// <param name="message"></param>
		public void WriteLog(bool isValid, ValidationMessageType messageType, string message)
		{
			if (_processTask == null)
			{
				throw new Exception("Please call StartWrite first, and then call WriteLog, finally call FinishAndWaitCompleted. ");
			}

			_validationResults.Add(new PublicApiValidationResult() { IsValid = isValid, MessageType = messageType, ErrorContent = message });

			if (!isValid)
			{
				_hasError = true;
			}

			this._newCount++;
		}

		#endregion

	}
}
