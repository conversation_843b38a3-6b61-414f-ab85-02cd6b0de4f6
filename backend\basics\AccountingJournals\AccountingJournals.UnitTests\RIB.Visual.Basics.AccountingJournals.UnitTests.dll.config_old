<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="policyInjection" type="Microsoft.Practices.EnterpriseLibrary.PolicyInjection.Configuration.PolicyInjectionSettings, Microsoft.Practices.EnterpriseLibrary.PolicyInjection, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
    <section name="unity" type="Microsoft.Practices.Unity.Configuration.UnityConfigurationSection, Microsoft.Practices.Unity.Configuration, Version=2.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
    <section name="loggingConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.LoggingSettings, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
    <section name="exceptionHandling" type="Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlingSettings, Microsoft.Practices.EnterpriseLibrary.ExceptionHandling, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
    <section name="dataConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings, Microsoft.Practices.EnterpriseLibrary.Data, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
    <section name="cachingConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings, Microsoft.Practices.EnterpriseLibrary.Caching, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
    <sectionGroup name="persistence">
      <section name="persistence-mapping" type="RIB.Visual.Platform.Persistence.Cfg.PersistenceMapping, RIB.Visual.Platform.Persistence" />
      <section name="persistence-nhibernate" type="RIB.Visual.Platform.Persistence.NHibernate.Cfg.Configuration, RIB.Visual.Platform.Persistence.NHibernate" />
    </sectionGroup>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="RIB.Visual.Platform.AppServer.Console.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <unity>
    <typeAliases>
      <!--Lifetime manager types-->
      <typeAlias alias="singleton" type="Microsoft.Practices.Unity.ContainerControlledLifetimeManager,Microsoft.Practices.Unity" />
      <!--User-defined type aliases-->
      <typeAlias alias="ISequenceGenerator" type="RIB.Visual.Platform.Core.ISequenceGenerator,RIB.Visual.Platform.Core"/>
      <typeAlias alias="Server.SequenceGenerator" type="RIB.Visual.Services.Platform.BusinessComponents.Sequence.SequenceGenerator,RIB.Visual.Services.Platform.BusinessComponents"/>
      <typeAlias alias="IProfileManager" type="RIB.Visual.Platform.Core.IProfileManager,RIB.Visual.Platform.Core"/>
      <typeAlias alias="Server.ProfileManager" type="RIB.Visual.Services.Platform.BusinessComponents.ProfileManager,RIB.Visual.Services.Platform.BusinessComponents"/>
      <typeAlias alias="ILockManager" type="RIB.Visual.Platform.Core.ILockManager,RIB.Visual.Platform.Core"/>
      <typeAlias alias="Server.LockManager" type="RIB.Visual.Services.Platform.BusinessComponents.LockManager,RIB.Visual.Services.Platform.BusinessComponents"/>
    </typeAliases>
    <containers>
      <!--Sequence Manager Container-->
      <container name="Sequence">
        <types>
          <type name="Server" type="ISequenceGenerator" mapTo="Server.SequenceGenerator">
          </type>
        </types>
      </container>
      <!--Profile Manager Container-->
      <container name="ProfileManager">
        <types>
          <type type="IProfileManager" mapTo="Server.ProfileManager"/>
          <type name="Server" type="IProfileManager" mapTo="Server.ProfileManager" />
        </types>
      </container>
      <!--Lock Manager Container-->
      <container name="LockManager">
        <types>
          <type name="Server" type="ILockManager" mapTo="Server.LockManager" >
            <lifetime type="singleton"/>
          </type>
        </types>
      </container>
      <!--Platform Services Container-->
      <container name="Services.Platform">
        <types>
          <type type="RIB.Visual.Services.Platform.Core.IUserGroupHelper,RIB.Visual.Services.Platform.Core"
                mapTo="RIB.Visual.Act.UserManagement.BusinessComponents.Logic.UserLogic,RIB.Visual.Act.UserManagement.BusinessComponents">
            <lifetime type="singleton"/>
          </type>
        </types>
      </container>
    </containers>
  </unity>
  <policyInjection>
    <policies>
      <add name="Business Layer Policies">
        <matchingRules>
          <add match="RIB\.Visual\..+\..+\.BusinessComponents(\..*)*" ignoreCase="false"
						type="RIB.Visual.Platform.OperationalManagement.NamespaceRegexMatchingRule, RIB.Visual.Platform.OperationalManagement"
						name="Namespace Regex Matching Rule" />
        </matchingRules>
        <handlers>
          <add logBehavior="BeforeAndAfter" beforeMessage="Before" afterMessage="After"
						eventId="100" includeParameterValues="true" includeCallStack="true"
						includeCallTime="true" priority="200" severity="Verbose"
						order="0" type="Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
						name="Logging Handler">
            <categories>
              <add name="Business Layer" />
            </categories>
          </add>
          <add exceptionPolicyName="BusinessLayer.ExceptionPolicy" order="0"
						type="Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandler, Microsoft.Practices.EnterpriseLibrary.ExceptionHandling, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
						name="Exception Handler" />
        </handlers>
      </add>
      <add name="ServiceLayer Policies">
        <matchingRules>
          <add match="RIB\.Visual\..+\..+\.ServiceDomain(\..*)*" ignoreCase="false"
						type="RIB.Visual.Platform.OperationalManagement.NamespaceRegexMatchingRule, RIB.Visual.Platform.OperationalManagement"
						name="Namespace Regex Matching Rule" />
        </matchingRules>
        <handlers>
          <add logBehavior="BeforeAndAfter" beforeMessage="Before" afterMessage="After"
						eventId="100" includeParameterValues="true" includeCallStack="true"
						includeCallTime="true" priority="200" severity="Verbose"
						order="0" type="Microsoft.Practices.EnterpriseLibrary.Logging.PolicyInjection.LogCallHandler, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
						name="Logging Handler">
            <categories>
              <add name="Service Layer" />
            </categories>
          </add>
          <add exceptionPolicyName="ServiceLayer.ExceptionPolicy" order="0"
						type="Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PolicyInjection.ExceptionCallHandler, Microsoft.Practices.EnterpriseLibrary.ExceptionHandling, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
						name="Exception Handler" />
        </handlers>
      </add>
    </policies>
  </policyInjection>
  <loggingConfiguration name="Logging Application Block" tracingEnabled="true"
		defaultCategory="General" logWarningsWhenNoCategoriesMatch="true">
    <listeners>
      <add source="Presentation Layer Logging" formatter="Text Formatter"
				log="RIB iTWO" machineName="" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
				traceOutputOptions="LogicalOperationStack" filter="All" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
				name="Formatted EventLog TraceListener (Presentation Layer)" />
      <add source="Service Layer Logging" formatter="Text Formatter"
				log="RIB iTWO" machineName="" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
				traceOutputOptions="LogicalOperationStack" filter="All" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
				name="Formatted EventLog TraceListener (Service Layer)" />
      <add source="Business Layer Logging" formatter="Text Formatter"
				log="RIB iTWO" machineName="" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
				traceOutputOptions="LogicalOperationStack" filter="All" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
				name="Formatted EventLog TraceListener (Business Layer)" />
      <add source="Resource Layer Logging" formatter="Text Formatter"
				log="RIB iTWO" machineName="" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
				traceOutputOptions="LogicalOperationStack" filter="All" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
				name="Formatted EventLog TraceListener (Resource Layer)" />
      <add source="General Logging" formatter="Text Formatter"
				log="RIB iTWO" machineName="" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
				traceOutputOptions="LogicalOperationStack" filter="All" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
				name="Formatted EventLog TraceListener (General)" />
    </listeners>
    <formatters>
      <add template="Timestamp: {timestamp}&#xD;&#xA;Category: {category} | Priority: {priority} | Severity: {severity}&#xD;&#xA;Application Domain: {appDomain} | Machine: {machine} | Process Id: {processId} | Thread Name: {threadName}&#xD;&#xA;Message: {message}&#xD;&#xA;Extended Properties: {dictionary({key} - {value}&#xD;&#xA;)}"
				type="Microsoft.Practices.EnterpriseLibrary.Logging.Formatters.TextFormatter, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
				name="Text Formatter" />
    </formatters>
    <categorySources>
      <add switchValue="Information" name="General">
        <listeners>
          <add name="Formatted EventLog TraceListener (General)" />
        </listeners>
      </add>
      <add switchValue="Information" name="Presentation Layer">
        <listeners>
          <add name="Formatted EventLog TraceListener (Presentation Layer)" />
        </listeners>
      </add>
      <add switchValue="Information" name="Service Layer">
        <listeners>
          <add name="Formatted EventLog TraceListener (Service Layer)" />
        </listeners>
      </add>
      <add switchValue="Information" name="Business Layer">
        <listeners>
          <add name="Formatted EventLog TraceListener (Business Layer)" />
        </listeners>
      </add>
      <add switchValue="Information" name="Resource Access Layer">
        <listeners>
          <add name="Formatted EventLog TraceListener (Resource Layer)" />
        </listeners>
      </add>
    </categorySources>
    <specialSources>
      <allEvents switchValue="All" name="All Events" />
      <notProcessed switchValue="All" name="Unprocessed Category" />
      <errors switchValue="All" name="Logging Errors &amp; Warnings">
        <listeners>
          <add name="Formatted EventLog TraceListener (General)" />
        </listeners>
      </errors>
    </specialSources>
  </loggingConfiguration>
  <exceptionHandling>
    <exceptionPolicies>
      <add name="BusinessLayer.ExceptionPolicy">
        <exceptionTypes>
          <add type="System.Exception, mscorlib" postHandlingAction="ThrowNewException"
						name="Exception">
            <exceptionHandlers>
              <add logCategory="Business Layer" eventId="0" severity="Error"
								title="Business Layer Exception Handling" formatterType="Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter, Microsoft.Practices.EnterpriseLibrary.ExceptionHandling"
								priority="700" useDefaultLogger="false" type="Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Logging.LoggingExceptionHandler, Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
								name="Logging Handler" />
              <add exceptionMessage="BusinessLayerException"
								exceptionMessageResourceType="" exceptionMessageResourceName=""
								wrapExceptionType="RIB.Visual.Platform.OperationalManagement.BusinessLayerException, RIB.Visual.Platform.OperationalManagement"
								type="Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.WrapHandler, Microsoft.Practices.EnterpriseLibrary.ExceptionHandling, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
								name="Wrap Handler" />
            </exceptionHandlers>
          </add>
        </exceptionTypes>
      </add>
      <add name="ServiceLayer.ExceptionPolicy">
        <exceptionTypes>
          <add type="System.Exception, mscorlib" postHandlingAction="ThrowNewException"
						name="Exception">
            <exceptionHandlers>
              <add logCategory="Service Layer" eventId="0" severity="Error"
								title="Service Layer Exception Handling" formatterType="Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter, Microsoft.Practices.EnterpriseLibrary.ExceptionHandling"
								priority="700" useDefaultLogger="false" type="Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Logging.LoggingExceptionHandler, Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Logging, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
								name="Logging Handler" />
              <add exceptionMessage="ServiceLayerException"
								exceptionMessageResourceType="" exceptionMessageResourceName=""
								wrapExceptionType="RIB.Visual.Platform.OperationalManagement.ServiceLayerException, RIB.Visual.Platform.OperationalManagement"
								type="Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.WrapHandler, Microsoft.Practices.EnterpriseLibrary.ExceptionHandling, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
								name="Wrap Handler" />
            </exceptionHandlers>
          </add>
        </exceptionTypes>
      </add>
    </exceptionPolicies>
  </exceptionHandling>
  <cachingConfiguration defaultCacheManager="Security Cache Manager">
    <cacheManagers>
      <add name="Security Cache Manager"
				expirationPollFrequencyInSeconds="5"
				maximumElementsInCacheBeforeScavenging="100"
				numberToRemoveWhenScavenging="10"
				backingStoreName="Null Storage"
				type="Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager, Microsoft.Practices.EnterpriseLibrary.Caching"  />

      <add name="Navigator Data Cache Manager"
				expirationPollFrequencyInSeconds="60"
				maximumElementsInCacheBeforeScavenging="10000"
				numberToRemoveWhenScavenging="100"
				type="Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager, Microsoft.Practices.EnterpriseLibrary.Caching"
				backingStoreName="Null Storage" />

      <add name="Security Cache Manager AccessObject"
				expirationPollFrequencyInSeconds="10"
				maximumElementsInCacheBeforeScavenging="100"
				numberToRemoveWhenScavenging="10"
				type="Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager, Microsoft.Practices.EnterpriseLibrary.Caching"
				backingStoreName="Null Storage" />

      <add name="Session Context Cache Manager"
				expirationPollFrequencyInSeconds="300"
				maximumElementsInCacheBeforeScavenging="1000000"
				numberToRemoveWhenScavenging="100"
				type="Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager, Microsoft.Practices.EnterpriseLibrary.Caching"
				backingStoreName="Null Storage" />
    </cacheManagers>
    <backingStores>
      <add encryptionProviderName="" type="Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.NullBackingStore, Microsoft.Practices.EnterpriseLibrary.Caching, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
					name="Null Storage" />
    </backingStores>
  </cachingConfiguration>
  <appSettings>
    <clear />
  </appSettings>
  <persistence>
    <persistence-mapping>
      <providerMappings>
        <add provider="default" section="persistence-nhibernate" />
      </providerMappings>
      <moduleMappings>
        <add module="Services.Platform" provider="default" />
        <add module="Services.Scheduler" provider="default" />
      </moduleMappings>
    </persistence-mapping>
    <persistence-nhibernate>
      <providers>
        <add name="default" dbparameterfile="Platform.Database.config">
          <modules>
            <add name="Services.Platform" assembly="RIB.Visual.Services.Platform.NHibernate.DataObjects" />
            <add name="Services.Scheduler" assembly="RIB.Visual.Services.Scheduler.NHibernate.DataObjects">
              <references>
                <add name="Services.Platform" />
              </references>
            </add>
          </modules>
        </add>
      </providers>
    </persistence-nhibernate>
  </persistence>

	<connectionStrings>
		<add connectionString="Server=rib-s-sql-dev\dev1;Database=iTWOCloud;Integrated Security=true" name="Default" />
		<add connectionString="Server=.\;Database=iTWOCloud;Integrated Security=true" name="Default.local" />
	</connectionStrings>

	<system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="Default" sendTimeout="00:20:00" bypassProxyOnLocal="true" maxBufferSize="10000000" maxBufferPoolSize="50000000" maxReceivedMessageSize="10000000">
          <readerQuotas maxStringContentLength="10000000" maxArrayLength="10000000" />
        </binding>
        <binding name="Default-Large" sendTimeout="00:30:00" bypassProxyOnLocal="true" maxBufferSize="50000000" maxBufferPoolSize="50000000" maxReceivedMessageSize="50000000">
          <readerQuotas maxStringContentLength="50000000" maxArrayLength="50000000" />
        </binding>
        <binding name="Streaming" sendTimeout="12:00:00" bypassProxyOnLocal="true" maxBufferSize="10000000" maxBufferPoolSize="50000000" maxReceivedMessageSize="2147483648" transferMode="Streamed">
          <readerQuotas maxStringContentLength="10000000" maxArrayLength="10000000" />
        </binding>
      </basicHttpBinding>
      <netTcpBinding>
        <binding name="Default" sendTimeout="00:20:00" maxBufferPoolSize="50000000" maxBufferSize="10000000" maxConnections="1000" maxReceivedMessageSize="10000000" portSharingEnabled="true">
          <readerQuotas maxStringContentLength="10000000" maxArrayLength="10000000" />
          <security mode="None" />
        </binding>
        <binding name="Default-Large" sendTimeout="00:30:00" maxBufferPoolSize="50000000" maxBufferSize="50000000" maxConnections="1000" maxReceivedMessageSize="50000000" portSharingEnabled="true">
          <readerQuotas maxStringContentLength="50000000" maxArrayLength="50000000" />
          <security mode="None" />
        </binding>
        <binding name="Streaming" sendTimeout="12:00:00" maxBufferPoolSize="50000000" maxBufferSize="10000000" maxConnections="1000" maxReceivedMessageSize="2147483648" transferMode="Streamed" portSharingEnabled="true">
          <readerQuotas maxStringContentLength="10000000" maxArrayLength="10000000" />
          <security mode="None" />
        </binding>
      </netTcpBinding>
    </bindings>

    <behaviors>
      <endpointBehaviors>
        <behavior name="Default-Large">
          <dataContractSerializer maxItemsInObjectGraph="1000000" />
        </behavior>
        <behavior name="Default">
          <dataContractSerializer maxItemsInObjectGraph="200000" />
        </behavior>
      </endpointBehaviors>
    </behaviors>

    <client>
      <endpoint address="net.tcp://localhost:4410/Services.Login" binding="netTcpBinding" bindingConfiguration="Default" behaviorConfiguration="Default" contract="RIB.Visual.Services.Login.ServiceDomain.IServiceDomain" name="Services.Login" />
      <endpoint address="net.tcp://localhost:4410/Services.Platform" binding="netTcpBinding" bindingConfiguration="Default" behaviorConfiguration="Default" contract="RIB.Visual.Services.Platform.ServiceDomain.IServiceDomain" name="Services.Platform" />
      <endpoint address="net.tcp://localhost:4410/Act.UserManagement" binding="netTcpBinding" bindingConfiguration="Default" behaviorConfiguration="Default" contract="RIB.Visual.Act.UserManagement.ServiceDomain.IServiceDomain" name="Act.UserManagement" />
      <endpoint address="net.tcp://localhost:4410/Services.Infrastructure" binding="netTcpBinding" bindingConfiguration="Default" behaviorConfiguration="Default" contract="RIB.Visual.Services.Infrastructure.ServiceDomain.IServiceDomain" name="Services.Infrastructure" />
      <endpoint address="net.tcp://localhost:4410/Services.Scheduler" binding="netTcpBinding" bindingConfiguration="Default" behaviorConfiguration="Default" contract="RIB.Visual.Services.Scheduler.ServiceDomain.IServiceDomain" name="Services.Scheduler" />
      <endpoint address="net.tcp://localhost:4410/Services.Notification" binding="netTcpBinding" bindingConfiguration="Default" behaviorConfiguration="Default" contract="RIB.Visual.Services.Notification.ServiceDomain.IServiceDomain" name="Services.Notification" />
    </client>

  </system.serviceModel>
</configuration>