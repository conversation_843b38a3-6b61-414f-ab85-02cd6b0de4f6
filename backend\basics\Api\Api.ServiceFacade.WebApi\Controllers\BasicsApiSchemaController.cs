using System.Collections.Generic;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using System.Web.Http;
using RIB.Visual.Platform.AppServer.Runtime;
using System;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// Represents a request to check the version of an API.
	/// </summary>
	public class CheckVersionRequest
	{
		/// <summary>
		/// Contains the route up to and not including the route version. e.g. model/publicapi/object.
		/// </summary>
		[Required]
		public string Base { get; set; }

		/// <summary>
		/// A list of Api versions. e.g. ["1.0", "2.0", "3.0"].
		/// </summary>
		public string[] Versions { get; set; }
	}

	/// <summary>
	/// Represents a response indicating whether a version is available.
	/// </summary>
	public class CheckVersionResponse
	{
		/// <summary>
		/// Initializes a new instance of the <see cref="CheckVersionResponse"/> class.
		/// </summary>
		public CheckVersionResponse()
		{
		}

		/// <summary>
		/// Initializes a new instance of the <see cref="CheckVersionResponse"/> class with the specified availability.
		/// </summary>
		/// <param name="available">Indicates whether the version is available.</param>
		public CheckVersionResponse(bool available)
		{
			this.Available = available;
		}

		/// <summary>
		/// Indicates whether the given routes are available or not.
		/// </summary>
		[JsonProperty("available")]
		public bool Available { get; set; }
	}

	/// <summary>
	/// Provides methods to interact with API schemas.
	/// </summary>
	public interface IApiSchemaProvider
	{
		/// <summary>
		/// Initializes the API schema provider with the specified request and API description group collection provider.
		/// </summary>
		/// <param name="request">The HTTP request.</param>
		/// <param name="apiDescriptionGroupCollectionProvider">The API description group collection provider.</param>
		void Initialize(HttpRequest request, IApiDescriptionGroupCollectionProvider apiDescriptionGroupCollectionProvider);

		/// <summary>
		/// Gets the API schemas for the specified version and routes.
		/// </summary>
		/// <param name="version">The API version.</param>
		/// <param name="routes">The routes to be queried.</param>
		/// <returns>The API schemas.</returns>
		object GetApiSchemas(string version, IEnumerable<string> routes);

		/// <summary>
		/// Checks if the specified versioned routes are supported.
		/// </summary>
		/// <param name="routes">The routes to be checked.</param>
		/// <param name="includeDeprecated">Indicates whether to include deprecated routes.</param>
		/// <returns>The result of the check.</returns>
		object CheckEndpoints(IEnumerable<CheckVersionRequest> routes, bool? includeDeprecated = false);
	}

	/// <summary>
	/// Controller for handling API schema-related requests.
	/// </summary>
	[RoutePrefix("basics/api/schema/1.0")]
	public class BasicsApiSchemaController : ApiControllerBase
	{
		private readonly IApiSchemaProvider _apiSchemaProvider;

		/// <summary>
		/// Initializes a new instance of the <see cref="BasicsApiSchemaController"/> class.
		/// </summary>
		/// <param name="apiDescriptionGroupCollectionProvider"></param>
		public BasicsApiSchemaController(IApiDescriptionGroupCollectionProvider apiDescriptionGroupCollectionProvider)
		{
			this._apiSchemaProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<IApiSchemaProvider>() ?? throw new Exception("IApiSchemaProvider not found");
			this._apiSchemaProvider.Initialize(this.Request, apiDescriptionGroupCollectionProvider);
		}

		/// <summary>
		/// Retrieves the API schemas for the given API version and routes.
		/// <![CDATA[partial-generated]]>
		/// </summary>
		/// <param name="version">The API version.</param>
		/// <param name="routes">The routes to be queried. e.g. model/, ^model/ or model/publicapi/object, added prefix "^" to match from the beginning of the route</param>
		/// <returns>
		/// <![CDATA[
		///		{
		///			"type":"[RIB.Visual.Cloud.Help.ServiceFacade.WebApi.RouteInfo]",
		///		}
		/// ]]>
		/// </returns>
		[AllowAnonymous]
		[Route("list"), HttpPost]
		public object GetApiSchemas(string version, IEnumerable<string> routes)
		{
			return this._apiSchemaProvider.GetApiSchemas(version, routes);
		}

		/// <summary>
		/// Check if all of a given set of versioned routes are supported.
		/// <![CDATA[partial-generated]]>
		/// </summary>
		/// <param name="routes">The routes to be checked.</param>
		/// <param name="includeDeprecated">Indicates whether to include deprecated routes, default to false.</param>
		/// <returns>
		/// <![CDATA[
		///		{
		///			"type":"RIB.Visual.Basics.Api.ServiceFacade.WebApi.CheckVersionResponse",
		///		}
		/// ]]>
		/// </returns>
		[AllowAnonymous]
		[Route("check"), HttpPost]
		public object CheckEndpoints(IEnumerable<CheckVersionRequest> routes, bool? includeDeprecated = false)
		{
			return this._apiSchemaProvider.CheckEndpoints(routes, includeDeprecated);
		}
	}
}
