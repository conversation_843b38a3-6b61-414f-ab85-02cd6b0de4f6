/*
 * $Id$
 * Copyright (c) RIB Software AG
 * 
 */

namespace RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi
{
    /// <summary/>
    public partial class AudContainerAudTableDto
    {

        ///// <summary/>
        //partial void OnCopy(AudContainerAudTableEntity entity) 
        //{
        //}

        ///// <summary/>
        //partial void OnConstruct(AudContainerAudTableEntity entity) 
        //{
        //}


    }
}
