using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;

namespace RIB.Visual.Awp.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class PackageStructureResourceEntity
	{
		/// <summary>
		/// 
		/// </summary>
		public int Id { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string Code { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int EstResourceTypeFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public DescriptionTranslateType DescriptionInfo { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? Quantity { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityTotal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? CostUnit { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? UomFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? BasCurrencyFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal CostTotal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal Budget { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? MdcCostCodeFk {  get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? ProjectCostCodeFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? MdcMaterialFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool Selected { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<Tuple<int, int, int>> LineItemResourceIds { get; set; }
	}
}
