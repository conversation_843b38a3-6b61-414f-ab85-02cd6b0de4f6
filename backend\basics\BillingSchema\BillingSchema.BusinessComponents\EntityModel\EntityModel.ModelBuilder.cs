//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON>art Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Data.Common;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.ModelConfiguration;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Linq;
using System.Runtime.Serialization;
using System.Xml.Serialization;
using RIB.Visual.Platform.Common;
using System.Data.Entity.Core.EntityClient;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Core.Objects.DataClasses;
using RIB.Visual.Basics.BillingSchema.BusinessComponents;

namespace RIB.Visual.Basics.BillingSchema.BusinessComponents
{
    /// <summary/>
    public partial class ModelBuilder
    {
		#region Constructors
		/// <summary>
		/// Initialize a new ModelBuilder object.
		/// </summary>
		public ModelBuilder()
    {
		}
		#endregion

		private static readonly object Locking = new object();
		private static DbCompiledModel _model;

		/// <summary>Creates a compiled entity model </summary>
		public static DbCompiledModel DbModel
		{
			get
			{
				if (_model == null)
				{
					lock (Locking)
					{
						if (_model != null) return _model;
            var modelBuilder = new DbModelBuilder();

            AddMappings(modelBuilder);
            AddAdditionalMappings(modelBuilder);

            modelBuilder.Conventions.Remove<StoreGeneratedIdentityKeyConvention>();

            _model = modelBuilder.Build(RIB.Visual.Platform.BusinessComponents.DbContext.CreateConnection()).Compile();
					}
				}

				return _model;
			}
		}

		// partial method to add special/additional mappings
		static partial void AddAdditionalMappings(DbModelBuilder modelBuilder);

		/// <summary>
		/// Adds the mapping for each entity of this db context.
		/// </summary>
		/// <param name="modelBuilder"></param>
		public static void AddMappings(DbModelBuilder modelBuilder)
		{

            #region RubricEntity

            modelBuilder.Entity<RubricEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("BAS_RUBRIC");
            // Properties:
            modelBuilder.Entity<RubricEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RubricEntity>()
                .Property(p => p.DescriptionInfo.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RubricEntity>()
                .Property(p => p.DescriptionInfo.DescriptionTr)
                    .HasColumnName(@"DESCRIPTION_TR")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<RubricEntity>(modelBuilder);
            // Association:

            #endregion

            #region RubricCategoryEntity

            modelBuilder.Entity<RubricCategoryEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("BAS_RUBRIC_CATEGORY");
            // Properties:
            modelBuilder.Entity<RubricCategoryEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RubricCategoryEntity>()
                .Property(p => p.RubricFk)
                    .HasColumnName(@"BAS_RUBRIC_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RubricCategoryEntity>()
                .Property(p => p.DescriptionInfo.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RubricCategoryEntity>()
                .Property(p => p.DescriptionInfo.DescriptionTr)
                    .HasColumnName(@"DESCRIPTION_TR")
                    .HasColumnType("int");
            modelBuilder.Entity<RubricCategoryEntity>()
                .Property(p => p.Sorting)
                    .HasColumnName(@"SORTING")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RubricCategoryEntity>()
                .Property(p => p.IsLive)
                    .HasColumnName(@"ISLIVE")
                    .IsRequired()
                    .HasColumnType("bit");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<RubricCategoryEntity>(modelBuilder);
            // Association:

            #endregion

            #region BillingSchemaEntity

            modelBuilder.Entity<BillingSchemaEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("MDC_BILLING_SCHEMA");
            // Properties:
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.LedgerContextFk)
                    .HasColumnName(@"MDC_LEDGER_CONTEXT_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.DescriptionInfo.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.DescriptionInfo.DescriptionTr)
                    .HasColumnName(@"DESCRIPTION_TR")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.Sorting)
                    .HasColumnName(@"SORTING")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.IsDefault)
                    .HasColumnName(@"ISDEFAULT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.IsChained)
                    .HasColumnName(@"ISCHAINED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.ValidFrom)
                    .HasColumnName(@"VALIDFROM")
                    .HasColumnType("date");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.ValidTo)
                    .HasColumnName(@"VALIDTO")
                    .HasColumnType("date");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.Remark)
                    .HasColumnName(@"REMARK")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.UserDefined1)
                    .HasColumnName(@"USERDEFINED1")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.UserDefined2)
                    .HasColumnName(@"USERDEFINED2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.UserDefined3)
                    .HasColumnName(@"USERDEFINED3")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.AutoCorrectVatLimit)
                    .HasColumnName(@"AUTOCORRECT_VATLIMIT")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.AutoCorrectNetLimit)
                    .HasColumnName(@"AUTOCORRECT_NETLIMIT")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.InvStatusOkFk)
                    .HasColumnName(@"INV_STATUS_OK_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.InvStatusErrorFk)
                    .HasColumnName(@"INV_STATUS_ERROR_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.IsChainedPes)
                    .HasColumnName(@"ISCHAINEDPES")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.BilStatusOkFk)
                    .HasColumnName(@"BIL_STATUS_OK_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaEntity>()
                .Property(p => p.BilStatusErrorFk)
                    .HasColumnName(@"BIL_STATUS_ERROR_FK")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<BillingSchemaEntity>(modelBuilder);
            // Association:

            #endregion

            #region BillingSchemaDetailEntity

            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("MDC_BILLING_SCHEMA_DETAIL");
            // Properties:
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.BillingSchemaFk)
                    .HasColumnName(@"MDC_BILLING_SCHEMA_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.RubricCategoryFk)
                    .HasColumnName(@"BAS_RUBRIC_CATEGORY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.Sorting)
                    .HasColumnName(@"SORTING")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.BillingLineTypeFk)
                    .HasColumnName(@"MDC_BILLING_LINE_TYPE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.BillingSchemaDetailFk)
                    .HasColumnName(@"MDC_BILLING_SCHEMA_DETAIL_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.GeneralsTypeFk)
                    .HasColumnName(@"PRC_GENERALSTYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.Value)
                    .HasColumnName(@"VALUE")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.IsEditable)
                    .HasColumnName(@"ISEDITABLE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.Group1)
                    .HasColumnName(@"GROUP1")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.Group2)
                    .HasColumnName(@"GROUP2")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.DescriptionInfo.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.DescriptionInfo.DescriptionTr)
                    .HasColumnName(@"DESCRIPTION_TR")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.Description2Info.Description)
                    .HasColumnName(@"DESCRIPTION2")
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.Description2Info.DescriptionTr)
                    .HasColumnName(@"DESCRIPTION2_TR")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.IsPrinted)
                    .HasColumnName(@"ISPRINTED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.AccountNo)
                    .HasColumnName(@"ACCOUNTNO")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.OffsetAccountNo)
                    .HasColumnName(@"OFFSETACCOUNTNO")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.IsTurnover)
                    .HasColumnName(@"ISTURNOVER")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.TaxCodeFk)
                    .HasColumnName(@"MDC_TAX_CODE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.FinalTotal)
                    .HasColumnName(@"FINAL_TOTAL")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.HasControllingUnit)
                    .HasColumnName(@"HASCONTROLLINGUNIT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.UserDefined1)
                    .HasColumnName(@"USERDEFINED1")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.UserDefined2)
                    .HasColumnName(@"USERDEFINED2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.UserDefined3)
                    .HasColumnName(@"USERDEFINED3")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.IsBold)
                    .HasColumnName(@"ISBOLD")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.IsItalic)
                    .HasColumnName(@"ISITALIC")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.IsUnderline)
                    .HasColumnName(@"ISUNDERLINE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.DetailAuthorAmountFk)
                    .HasColumnName(@"MDC_BLLNG_SCHM_DTL_AA_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.BillingSchemaDetailTaxFk)
                    .HasColumnName(@"MDC_BLLNG_SCHM_DTL_TAX_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.CredFactor)
                    .HasColumnName(@"CREDFACTOR")
                    .HasPrecision(15, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.DebFactor)
                    .HasColumnName(@"DEBFACTOR")
                    .HasPrecision(15, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.CredLineTypeFk)
                    .HasColumnName(@"MDC_CREDLINETYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.DebLineTypeFk)
                    .HasColumnName(@"MDC_DEBLINETYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.CodeRetention)
                    .HasColumnName(@"CODE_RETENTION")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.PaymentTermFk)
                    .HasColumnName(@"BAS_PAYMENT_TERM_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.Formula)
                    .HasColumnName(@"FORMULA")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.IsHidden)
                    .HasColumnName(@"ISHIDDEN")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.IsHiddenIfZero)
                    .HasColumnName(@"ISHIDDENIFZERO")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.IsPrintedZero)
                    .HasColumnName(@"ISPRINTEDZERO")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.CostLineTypeFk)
                    .HasColumnName(@"MDC_COSTLINETYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.SqlStatement)
                    .HasColumnName(@"SQL_STATEMENT")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.IsResetFI)
                    .HasColumnName(@"RESET_IF_FI")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .Property(p => p.IsNetAdjusted)
                    .HasColumnName(@"IS_NET_ADJUSTED")
                    .IsRequired()
                    .HasColumnType("bit");
			modelBuilder.Entity<BillingSchemaDetailEntity>()
				 .Property(p => p.Factor)
					  .HasColumnName(@"FACTOR")
					  .IsRequired()
					  .HasPrecision(15, 6)
					  .HasColumnType("numeric");
			RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<BillingSchemaDetailEntity>(modelBuilder);
            // Associations:
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .HasMany(p => p.BillingSchemaDetailEntities_DetailAuthorAmountFk)
                    .WithOptional(c => c.BillingSchemaDetailEntity_DetailAuthorAmountFk)
                .HasForeignKey(p => new { p.DetailAuthorAmountFk })
                    .WillCascadeOnDelete(false);
            modelBuilder.Entity<BillingSchemaDetailEntity>()
                .HasMany(p => p.BillingSchemaDetailEntities_BillingSchemaDetailTaxFk)
                    .WithOptional(c => c.BillingSchemaDetailEntity_BillingSchemaDetailTaxFk)
                .HasForeignKey(p => new { p.BillingSchemaDetailTaxFk })
                    .WillCascadeOnDelete(false);

            #endregion

            #region BillingLineTypeEntity

            modelBuilder.Entity<BillingLineTypeEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("MDC_BILLING_LINE_TYPE");
            // Properties:
            modelBuilder.Entity<BillingLineTypeEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<BillingLineTypeEntity>()
                .Property(p => p.DescriptionInfo.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<BillingLineTypeEntity>()
                .Property(p => p.DescriptionInfo.DescriptionTr)
                    .HasColumnName(@"DESCRIPTION_TR")
                    .HasColumnType("int");
            modelBuilder.Entity<BillingLineTypeEntity>()
                .Property(p => p.Sorting)
                    .HasColumnName(@"SORTING")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<BillingLineTypeEntity>()
                .Property(p => p.IsDefault)
                    .HasColumnName(@"ISDEFAULT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<BillingLineTypeEntity>()
                .Property(p => p.IsLive)
                    .HasColumnName(@"ISLIVE")
                    .IsRequired()
                    .HasColumnType("bit");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<BillingLineTypeEntity>(modelBuilder);
            // Association:

            #endregion

            #region ComplexTypes

            modelBuilder.ComplexType<DescriptionTranslateType>();

            #endregion

            #region Disabled conventions


            #endregion

        }

    
        /// <summary>
        /// There are no comments for RubricEntity in the schema.
        /// </summary>
        public DbSet<RubricEntity> RubricEntities { get; set; }
    
        /// <summary>
        /// There are no comments for RubricCategoryEntity in the schema.
        /// </summary>
        public DbSet<RubricCategoryEntity> RubricCategoryEntities { get; set; }
    
        /// <summary>
        /// There are no comments for BillingSchemaEntity in the schema.
        /// </summary>
        public DbSet<BillingSchemaEntity> BillingSchemaEntities { get; set; }
    
        /// <summary>
        /// There are no comments for BillingSchemaDetailEntity in the schema.
        /// </summary>
        public DbSet<BillingSchemaDetailEntity> BillingSchemaDetailEntities { get; set; }
    
        /// <summary>
        /// There are no comments for BillingLineTypeEntity in the schema.
        /// </summary>
        public DbSet<BillingLineTypeEntity> BillingLineTypeEntities { get; set; }
    }
}
