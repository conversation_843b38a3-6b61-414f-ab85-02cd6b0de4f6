﻿using RIB.Visual.Basics.LookupData.BusinessComponents;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Core;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using ServicesInfrastructureLogic = RIB.Visual.Services.Infrastructure.BusinessComponents;

namespace RIB.Visual.Basics.AuditTrail.BusinessComponents.Logic
{
	/// <summary>
	/// 
	/// </summary>
	public class BasicsAuditTrailContainerLogic : CommonLogicBase<AudContainerEntity>
	{

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public AudContainerEntity CreateEntity()
		{

			// if (!_ignorePermission) { Permission.Ensure(DefaultAccessGuid, Permissions.Create); }

			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				// var id = this.SequenceManager.GetNext("BAS_FORM");
				// init default values here
				var entity = new AudContainerEntity();
				// entity.Id = id;
				return entity;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entity"></param>
		/// <returns></returns>
		public override AudContainerEntity Save(AudContainerEntity entity)
		{
			//RVPBizComp.Permission.Ensure("f01193df20e34b8d917250ad17a433f1", Permissions.Write);

			//entity.SaveTranslate(this.UserLanguageId, new Func<FormEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });

			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				dbcontext.Save(entity);
				return entity;
			}
		}

	}

}
