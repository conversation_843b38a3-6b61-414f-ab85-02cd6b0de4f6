using System;

namespace RIB.Visual.Basics.Api.Client.Itwo40WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public static class Constant
	{
		//public const string BaseUrl1 = "http://rib-w0839.rib-software.com/itwo40/v1/";
		public const string BaseUrl1 = "https://rib-w0918.rib-software.com/itwo40dev/v1_local/";

		public const string BaseUrl2 = "https://itwo40-int.rib-software.com/itwo40/daily/";
		public const string BaseUrl3 = "https://itwo40.rib-software.com/itwo40/dev1";
		public const string BaseUrl4 = "https://itwo40.rib-software.com/itwo40/dev2";
		public const string BaseUrl5 = "https://itwo40.rib-software.com/itwo40/rel1";
		public const string BaseUrl6 = "https://itwo40.rib-software.com/itwo40/rel2";

		                                          
		public const string IdentityServerUrl1 = "https://itwo40-int.rib-software.com/itwo40/identityserver/core/connect/token";
		public const string IdentityServerUrl2 = "https://itwo40-int.rib-software.com/itwo40/daily/identityservercore/core/connect/token";
		public const string IdentityServerUrl3 = "https://itwo40.rib-software.com/itwo40/dev1/identityserver/core/connect/token";
		public const string IdentityServerUrl4 = "https://itwo40.rib-software.com/itwo40/dev2/identityserver/core/connect/token";
		public const string IdentityServerUrl5 = "https://itwo40.rib-software.com/itwo40/rel1/identityserver/core/connect/token";
		public const string IdentityServerUrl6 = "https://itwo40.rib-software.com/itwo40/rel2/identityserver/core/connect/token";
		public const string IdentityServerUrl7 = "https://rib-w0635.rib-software.com/identity/core/connect/token";

		public const string OperationInquiry = "inquiry";
		public const string OperationLookup = "lookup";
		public const string OperationLookupMaterial = "Materiallookup";


		public const string GetAssignedCompaniesWithRoles = "/basics/company/getassignedcompanieswithroles";
		public const string GetRevitModelsFromProjects = "/model/project/model/getprojectmodelinfo";
		public const string CheckCompaniesWithRoles = "/basics/company/checkcompany";
		

		public const string OperationRelUrlInquiry = "/businesspartner/publicapi/getInquiryAddresses";
		public const string OperationRelUrlLookup = "/businesspartner/publicapi/getInquiryAddresses";
		public const string OperationRelUrlMaterial = "/basics/publicapi/materiallookup/getInquiryMaterial";

		// model upload                
		public const string ModelUpLoadBegin = "/model/main/upload/begin";
		public const string ModelUpLoadData = "/model/main/upload/data";
		public const string ModelUpLoadEnd = "/model/main/upload/end";
		public const string ModelUpLoadProgress = "/model/main/upload/progress";

		//public const string ClientUrl = BaseUrl + "Clients";
		//public const string ClientUrl ="https://rib-s-itwocld5d.rib-software.com/Cloud5D/Daily/client";

		//public const string AppServicesUrl = BaseUrl + "services";
		// public const string AppServicesUrl = "https://rib-w0635.rib-software.com/Cloud5D/v1/services";
		// public  string AppServicesUrl = "https://rib-s-itwocld5d.rib-software.com/Cloud5D/Daily/services";

		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		private static string MakeInquiryRequest(Model theModel)
		{
			// {0} requestId
			// {1} 100.11
			// {2} &id=4124"
			const string requestTemplate =
			//	@"{0}/#/?navigate&operation=inquiry&selection=single&module=businesspartner.main&requestid={1}&company={2}{3}";
			@"{0}/#/api?navigate&operation={4}&selection=single&module=businesspartner.main&requestid={1}&company={2}{3}";
			theModel.RequestId = Guid.NewGuid();
			var urlPart = string.Format(requestTemplate,
				theModel.BaseUrl+ "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&search=" + theModel.SearchPattern,
				theModel.Operation
				);

			return urlPart;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		private static string MakeLookupRequest(Model theModel)
		{
			// {0} requestId
			// {1} 100.11
			// {2} &id=4124"
			const string requestTemplate =
				//	@"{0}/#/?navigate&operation=inquiry&selection=single&module=businesspartner.main&requestid={1}&company={2}{3}";
			@"{0}/#/api?navigate&operation=lookup&module=businesspartner.main&company={2}{3}";
			theModel.RequestId = Guid.NewGuid();
			var urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&search=" + theModel.SearchPattern
				);

			return urlPart;
		}

		private static string MakeMaterialLookupRequest(Model theModel)
		{
			const string requestTemplate =
			 @"{0}/#/api?navigate&operation=inquiry&selection=single&module=basics.materiallookup&requestid={1}&company={2}{3}";

			theModel.RequestId = Guid.NewGuid();

			string urlPart = string.Format(requestTemplate,
				theModel.BaseUrl + "client",
				theModel.RequestIdAsString,
				theModel.CompanyCode,
				string.IsNullOrWhiteSpace(theModel.SearchPattern) ? "" : "&search=" + theModel.SearchPattern
				);

			return urlPart;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="theModel"></param>
		/// <returns></returns>
		public static string MakeRequestUrl(Model theModel)
		{
			if (theModel.Operation.Equals(Constant.OperationInquiry))
				return MakeInquiryRequest(theModel);
			if (theModel.Operation.Equals(Constant.OperationLookup))
				return MakeLookupRequest(theModel);
			if (theModel.Operation.Equals(Constant.OperationLookupMaterial))
				return MakeMaterialLookupRequest(theModel);

			return string.Empty;
		}

	}
}