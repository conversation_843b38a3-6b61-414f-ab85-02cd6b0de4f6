﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;

namespace RIB.Visual.Basics.Api.Client.SyncDataApp
{
	class Program
	{
		static Timer timer = new Timer();

		/// <remarks></remarks>>
		static IdentityModel Model { get; set; }

		/// <remarks></remarks>>
		static string AccessToken { get; set; }

		static void Main(string[] args)
		{
			Init();
			Login();

			string word = string.Empty;
			do
			{
				word = Console.ReadLine().ToLower();
			}
			while (word != "exit");
		}

		static void Init()
		{
			Model = new IdentityModel();
			double interval = 10000;
			double.TryParse(ConfigurationManager.AppSettings["interval"], out interval);
			timer.Interval = interval;
			timer.Elapsed += timer_Elapsed;
		}

		static void timer_Elapsed(object sender, ElapsedEventArgs e)
		{
			//SyncData();
		}

		static void Login()
		{
			Console.WriteLine("Logining...   please wait...");
			BackgroundWorker worker = new BackgroundWorker();
			worker.DoWork += (a, b) =>
			{
				timer.Enabled = false;
				string result = string.Empty;
				if (LoginService.DoLogin(Model, out result))
				{
					AccessToken = result;
				}
			};
			worker.RunWorkerCompleted += worker_RunWorkerCompleted;
			worker.RunWorkerAsync();
		}

		static void worker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
		{
			//Console.WriteLine("\nLogin finished");
			if (AccessToken != null)
			{
				Console.WriteLine("\nLogin successful");
				timer.Enabled = true;
				timer.Start();
				SyncData();
			}
			else
			{
				Console.WriteLine("\nLogin failed");
			}
		}

		static void SyncData()
		{
			var worker = new BackgroundWorker();
			worker.DoWork += (a, b) =>
			{
				try
				{
					timer.Enabled = false;
					Console.WriteLine("\nbegin importing data ... please Wait ");

					string result = SyncDataService.SyncData(AccessToken, Model);
					Console.WriteLine("\n" + result);

				}
				catch (Win32Exception ex)
				{
					Console.WriteLine(ex.Message);
				}
			};

			worker.RunWorkerCompleted += (a, b) =>
			{
				timer.Enabled = true;
			};

			worker.RunWorkerAsync();

		}
	}
}
