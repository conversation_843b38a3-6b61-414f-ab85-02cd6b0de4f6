﻿<?xml version="1.0" encoding="utf-8"?>
<EntityDeveloper Version="6.10.1145.0">
  <ModelSettings xmlns:ed="http://devart.com/schemas/EntityDeveloper/1.0" AppConnectionStringName="ITWOCloudsEntitiesConnectionString" ConnectionStringInAppConfig="False" TargetFramework="Net45" DetectManyToManyAssociations="False" DetectTPTInheritance="False" PreserveSchemaName="False" EntityFrameworkVersion="Version6" IncludeForeignKeysInModel="true" DetectFunctionBasedColumnDefault="false">
    <Connection ConnectionString="Data Source=sql12-dev\dev;Initial Catalog=iTWOCloud;Integrated Security=True;Persist Security Info=True" Provider="System.Data.SqlClient" />
    <Generation>
      <GeneratedFiles>
        <File Name="EntityModel.ModelBuilder.cs" ProjectFolder="EntityModel" />
        <File Name="DdTempIdsEntity.cs" ProjectFolder="Entities" OverwriteMode="None" />
        <File Name="DdTempIdsEntity.Generated.cs" ProjectFolder="Entities" />
        <File Name="ApiRequestItemEntity.cs" ProjectFolder="Entities" OverwriteMode="None" />
        <File Name="ApiRequestItemEntity.Generated.cs" ProjectFolder="Entities" />
        <File Name="ApiRequestItemDto.cs" Project="RIB.Visual.Basics.Api.ServiceFacade.WebApi" ProjectFolder="Dtos" OverwriteMode="None" />
        <File Name="ApiRequestItemDto.Generated.cs" Project="RIB.Visual.Basics.Api.ServiceFacade.WebApi" ProjectFolder="Dtos" />
      </GeneratedFiles>
    </Generation>
    <DatabaseFirstNamingRules>
      <EntitySet PluralizationName="Pluralize" />
      <Class UseSchemaAsPrefix="False" PluralizeCollectionNavigationPropertyName="True" RemoveUnderscores="True" CodeCase="Capitalized" AddPrefix="" AddSuffix="Entity" RemovePrefixes="BAS;SMP;BOQ;BIL;PRJ;BPD;" RemoveSuffixes="" PluralizationName="Unchanged" />
      <Property RemoveUnderscores="True" CodeCase="Capitalized" AddPrefix="" AddSuffix="" RemovePrefixes="" RemoveSuffixes="" PluralizationName="Unchanged" />
    </DatabaseFirstNamingRules>
    <ModelFirst StorageSynchronizationEnabled="False" TargetSchema="dbo" TargetProviderName="System.Data.SqlClient">
      <TargetServer Server="SQL Server" ServerVersion="2012" />
      <ModelFirstNamingRules>
        <Table RemoveUnderscores="False" CodeCase="Unchanged" AddPrefix="" AddSuffix="" RemovePrefixes="" RemoveSuffixes="" PluralizationName="Pluralize" />
        <Column RemoveUnderscores="False" CodeCase="Unchanged" AddPrefix="" AddSuffix="" RemovePrefixes="" RemoveSuffixes="" PluralizationName="Unchanged" />
      </ModelFirstNamingRules>
    </ModelFirst>
    <Diagrams>
      <Diagram Name="MainDiagram" DefaultDiagram="True" />
    </Diagrams>
    <Templates>
      <Template Name="TheDbContext" Description="RIB specific entity Model Creation script" Enabled="True" File="..\..\..\..\..\binpool\buildtools\t4-templates\DbContext.T4">
        <ed:Property Name="ValidationFramework" Type="EntityDeveloper.TemplateEngine.ValidationFramework, EntityDeveloper.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <ValidationFramework>DataAnnotations</ValidationFramework>
        </ed:Property>
        <ed:Property Name="ValidationErrorMessages" Type="EntityDeveloper.TemplateEngine.ValidationErrorMessages, EntityDeveloper.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <ValidationErrorMessages xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" />
        </ed:Property>
        <ed:Property Name="FilePerClass" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="ModelNameAsFilesPrefix" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="HeaderTimestampVersionControlTag" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <string>created for Version 1.0</string>
        </ed:Property>
        <ed:Property Name="EntitiesOutput" Type="EntityDeveloper.TemplateEngine.OutputInfo, EntityDeveloper.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <OutputInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <Project />
            <ProjectFolder>Entities</ProjectFolder>
            <DestinationFolder>..\Entities</DestinationFolder>
          </OutputInfo>
        </ed:Property>
        <ed:Property Name="ContextOutput" Type="EntityDeveloper.TemplateEngine.OutputInfo, EntityDeveloper.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <OutputInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <Project />
            <ProjectFolder>EntityModel</ProjectFolder>
            <DestinationFolder>..\EntityModel</DestinationFolder>
          </OutputInfo>
        </ed:Property>
        <ed:Property Name="GeneratePartialClass" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="ImplementCloneable" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="ImplementINotifyPropertyChanging" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="ImplementINotifyPropertyChanged" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="WcfDataContractAttributes" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="WcfDataMemberOnNavigationProperties" Type="EntityDeveloper.WcfDataMemberGenerationBehavior, EntityDeveloper.Orm.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <WcfDataMemberGenerationBehavior>All</WcfDataMemberGenerationBehavior>
        </ed:Property>
        <ed:Property Name="FluentMapping" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="MappingGenerationStrategy" Type="EntityDeveloper.EntityFramework.CodeFirstMappingGenerationStrategy, EntityDeveloper.Orm.EntityFramework, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <CodeFirstMappingGenerationStrategy>UseOnModelCreatingMethod</CodeFirstMappingGenerationStrategy>
        </ed:Property>
        <ed:Property Name="DatabaseIndependent" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="DisabledConventions" Type="EntityDeveloper.EntityFramework.CodeFirstConfigurationConvention, EntityDeveloper.Orm.EntityFramework, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <CodeFirstConfigurationConvention />
        </ed:Property>
        <ed:Property Name="ConfigurationsOutput" Type="EntityDeveloper.TemplateEngine.OutputInfo, EntityDeveloper.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <OutputInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <Project />
            <ProjectFolder />
            <DestinationFolder />
          </OutputInfo>
        </ed:Property>
        <ed:Property Name="AutoDetectChangesEnabled" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="ProxyCreationEnabled" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="ValidateOnSaveEnabled" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
      </Template>
      <Template Name="DataTransferObject" Description="Use this template to generate Data Transfer Object (DTO) classes." Enabled="True" File="..\..\..\..\..\binpool\buildtools\t4-templates\DataTransferObject.T4">
        <ed:Property Name="ValidationFramework" Type="EntityDeveloper.TemplateEngine.ValidationFramework, EntityDeveloper.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <ValidationFramework>DataAnnotations</ValidationFramework>
        </ed:Property>
        <ed:Property Name="ValidationErrorMessages" Type="EntityDeveloper.TemplateEngine.ValidationErrorMessages, EntityDeveloper.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <ValidationErrorMessages xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" />
        </ed:Property>
        <ed:Property Name="FilePerClass" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="DtosOutput" Type="EntityDeveloper.TemplateEngine.OutputInfo, EntityDeveloper.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <OutputInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <Project>RIB.Visual.Basics.Api.ServiceFacade.WebApi</Project>
            <ProjectFolder>Dtos</ProjectFolder>
            <DestinationFolder>..\..\Api.ServiceFacade.WebApi\Dtos</DestinationFolder>
          </OutputInfo>
        </ed:Property>
        <ed:Property Name="ConvertersOutput" Type="EntityDeveloper.TemplateEngine.OutputInfo, EntityDeveloper.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <OutputInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
            <Project />
            <ProjectFolder />
            <DestinationFolder />
          </OutputInfo>
        </ed:Property>
        <ed:Property Name="GeneratePartialDtos" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="GeneratePartialConverters" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="DtoNamespace" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <string>RIB.Visual.Basics.Api.ServiceFacade.WebApi</string>
        </ed:Property>
        <ed:Property Name="HeaderTimestampVersionControlTag" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <string>created for Version 1.0</string>
        </ed:Property>
        <ed:Property Name="DtoClassNamePrefix" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <string d6p1:nil="true" xmlns:d6p1="http://www.w3.org/2001/XMLSchema-instance" />
        </ed:Property>
        <ed:Property Name="DtoClassNameSuffix" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <string>Dto</string>
        </ed:Property>
        <ed:Property Name="WcfDataContractAttributes" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="WcfDataMemberOnNavigationProperties" Type="EntityDeveloper.WcfDataMemberGenerationBehavior, EntityDeveloper.Orm.Common, Version=6.10.1145.0, Culture=neutral, PublicKeyToken=09af7300eec23701">
          <WcfDataMemberGenerationBehavior>None</WcfDataMemberGenerationBehavior>
        </ed:Property>
        <ed:Property Name="GenerateDtoConstructors" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="UseDtoClassesInAssociations" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>true</boolean>
        </ed:Property>
        <ed:Property Name="GenerateConverters" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="ConverterClassNamePrefix" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <string d6p1:nil="true" xmlns:d6p1="http://www.w3.org/2001/XMLSchema-instance" />
        </ed:Property>
        <ed:Property Name="ConverterClassNameSuffix" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <string>Converter</string>
        </ed:Property>
        <ed:Property Name="JsonNullValueHandling" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
        <ed:Property Name="JsonNullValueHandlingNavProp" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <boolean>false</boolean>
        </ed:Property>
      </Template>
    </Templates>
    <AttributeAssemblies>
      <AttributeAssembly Name="System.ComponentModel.DataAnnotations, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
        <AttributeType Name="ConcurrencyCheckAttribute" AttributeNamespace="System.ComponentModel.DataAnnotations" AttributeAllowMultiple="False" AttributeValidOn="Property, Field">
          <AttributeConstructor />
        </AttributeType>
        <AttributeType Name="CreditCardAttribute" AttributeNamespace="System.ComponentModel.DataAnnotations" AttributeAllowMultiple="False" AttributeValidOn="Property, Field, Parameter">
          <AttributeConstructor />
          <Properties>
            <ed:Property Name="ErrorMessage" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceName" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceType" Type="System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </Properties>
        </AttributeType>
        <AttributeType Name="DisplayAttribute" AttributeNamespace="System.ComponentModel.DataAnnotations" AttributeAllowMultiple="False" AttributeValidOn="Method, Property, Field, Parameter">
          <AttributeConstructor />
          <Properties>
            <ed:Property Name="ShortName" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="Description" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="Prompt" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="GroupName" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ResourceType" Type="System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="AutoGenerateField" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="False" />
            <ed:Property Name="AutoGenerateFilter" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="False" />
            <ed:Property Name="Order" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="0" />
          </Properties>
        </AttributeType>
        <AttributeType Name="KeyAttribute" AttributeNamespace="System.ComponentModel.DataAnnotations" AttributeAllowMultiple="False" AttributeValidOn="Property, Field">
          <AttributeConstructor />
        </AttributeType>
        <AttributeType Name="MaxLengthAttribute" AttributeNamespace="System.ComponentModel.DataAnnotations" AttributeAllowMultiple="False" AttributeValidOn="Property, Field, Parameter">
          <AttributeConstructor>
            <ed:Property Name="length" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="0" />
          </AttributeConstructor>
          <AttributeConstructor />
          <Properties>
            <ed:Property Name="ErrorMessage" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceName" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceType" Type="System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </Properties>
        </AttributeType>
        <AttributeType Name="CompareAttribute" AttributeNamespace="System.ComponentModel.DataAnnotations" AttributeAllowMultiple="False" AttributeValidOn="Property">
          <AttributeConstructor>
            <ed:Property Name="otherProperty" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </AttributeConstructor>
          <Properties>
            <ed:Property Name="ErrorMessage" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceName" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceType" Type="System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </Properties>
        </AttributeType>
        <AttributeType Name="EditableAttribute" AttributeNamespace="System.ComponentModel.DataAnnotations" AttributeAllowMultiple="False" AttributeValidOn="Property, Field">
          <AttributeConstructor>
            <ed:Property Name="allowEdit" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="False" />
          </AttributeConstructor>
          <Properties>
            <ed:Property Name="AllowInitialValue" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="False" />
          </Properties>
        </AttributeType>
        <AttributeType Name="EnumDataTypeAttribute" AttributeNamespace="System.ComponentModel.DataAnnotations" AttributeAllowMultiple="False" AttributeValidOn="Method, Property, Field, Parameter">
          <AttributeConstructor>
            <ed:Property Name="enumType" Type="System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </AttributeConstructor>
          <Properties>
            <ed:Property Name="ErrorMessage" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceName" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceType" Type="System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </Properties>
        </AttributeType>
        <AttributeType Name="RangeAttribute" AttributeNamespace="System.ComponentModel.DataAnnotations" AttributeAllowMultiple="False" AttributeValidOn="Property, Field, Parameter">
          <AttributeConstructor>
            <ed:Property Name="minimum" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="0" />
            <ed:Property Name="maximum" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="0" />
          </AttributeConstructor>
          <AttributeConstructor>
            <ed:Property Name="minimum" Type="System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="0" />
            <ed:Property Name="maximum" Type="System.Double, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="0" />
          </AttributeConstructor>
          <AttributeConstructor>
            <ed:Property Name="type" Type="System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="minimum" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="maximum" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </AttributeConstructor>
          <Properties>
            <ed:Property Name="ErrorMessage" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceName" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceType" Type="System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </Properties>
        </AttributeType>
        <AttributeType Name="RegularExpressionAttribute" AttributeNamespace="System.ComponentModel.DataAnnotations" AttributeAllowMultiple="False" AttributeValidOn="Property, Field, Parameter">
          <AttributeConstructor>
            <ed:Property Name="pattern" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </AttributeConstructor>
          <Properties>
            <ed:Property Name="ErrorMessage" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceName" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceType" Type="System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </Properties>
        </AttributeType>
        <AttributeType Name="RequiredAttribute" AttributeNamespace="System.ComponentModel.DataAnnotations" AttributeAllowMultiple="False" AttributeValidOn="Property, Field, Parameter">
          <AttributeConstructor />
          <Properties>
            <ed:Property Name="AllowEmptyStrings" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="False" />
            <ed:Property Name="ErrorMessage" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceName" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceType" Type="System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </Properties>
        </AttributeType>
        <AttributeType Name="UrlAttribute" AttributeNamespace="System.ComponentModel.DataAnnotations" AttributeAllowMultiple="False" AttributeValidOn="Property, Field, Parameter">
          <AttributeConstructor />
          <Properties>
            <ed:Property Name="ErrorMessage" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceName" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceType" Type="System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </Properties>
        </AttributeType>
        <AttributeType Name="StringLengthAttribute" AttributeNamespace="System.ComponentModel.DataAnnotations" AttributeAllowMultiple="False" AttributeValidOn="Property, Field, Parameter">
          <AttributeConstructor>
            <ed:Property Name="maximumLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="0" />
          </AttributeConstructor>
          <Properties>
            <ed:Property Name="MinimumLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="0" />
            <ed:Property Name="ErrorMessage" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceName" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceType" Type="System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </Properties>
        </AttributeType>
        <AttributeType Name="MinLengthAttribute" AttributeNamespace="System.ComponentModel.DataAnnotations" AttributeAllowMultiple="False" AttributeValidOn="Property, Field, Parameter">
          <AttributeConstructor>
            <ed:Property Name="length" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="0" />
          </AttributeConstructor>
          <Properties>
            <ed:Property Name="ErrorMessage" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceName" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceType" Type="System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </Properties>
        </AttributeType>
      </AttributeAssembly>
      <AttributeAssembly Name="RIB.Visual.Platform.Core, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4" File="..\..\..\..\..\BinPool\Debug.Server\RIB.Visual.Platform.Core.dll" />
      <AttributeAssembly Name="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4" File="..\..\..\..\..\BinPool\Debug.Server\RIB.Visual.Platform.Common.dll">
        <AttributeType Name="DomainNameAttribute" AttributeNamespace="RIB.Visual.Platform.Common" AttributeAllowMultiple="False" AttributeValidOn="Property">
          <AttributeConstructor />
          <Properties>
            <ed:Property Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="0" />
            <ed:Property Name="ErrorMessage" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceName" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ErrorMessageResourceType" Type="System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </Properties>
        </AttributeType>
        <AttributeType Name="SanitizeHtmlAttribute" AttributeNamespace="RIB.Visual.Platform.Common" AttributeAllowMultiple="False" AttributeValidOn="Property">
          <AttributeConstructor />
          <Properties>
            <ed:Property Name="CompleteDocument" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="False" />
          </Properties>
        </AttributeType>
        <AttributeType Name="ConcurrencyTypeAttribute" AttributeNamespace="RIB.Visual.Platform.Common" AttributeAllowMultiple="False" AttributeValidOn="Property">
          <AttributeConstructor />
          <Properties>
            <ed:Property Name="ConcurrencyType" Type="RIB.Visual.Platform.Common.ConcurrencyType, RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4" Default="ManualMerge" />
            <ed:Property Name="RelGroup" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="CustomHint" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="0" />
            <ed:Property Name="IsFixed" Type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" Default="False" />
          </Properties>
        </AttributeType>
        <AttributeType Name="FieldLabelAttribute" AttributeNamespace="RIB.Visual.Platform.Common" AttributeAllowMultiple="False" AttributeValidOn="Property">
          <AttributeConstructor />
          <Properties>
            <ed:Property Name="Key" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
            <ed:Property Name="ModuleSubModule" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
          </Properties>
        </AttributeType>
      </AttributeAssembly>
    </AttributeAssemblies>
  </ModelSettings>
</EntityDeveloper>