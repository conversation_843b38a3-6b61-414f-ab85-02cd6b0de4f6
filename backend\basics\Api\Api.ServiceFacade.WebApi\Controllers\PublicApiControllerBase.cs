using Microsoft.AspNetCore.Mvc.WebApiCompatShim;
using RIB.Visual.Basics.Api.Common;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.Entity.Validation;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// Represents a general Controller class for public api.
	/// </summary>
	public abstract class PublicApiControllerBase : ApiControllerBase
	{
		/// <summary>
		/// Constructor.
		/// </summary>
		protected PublicApiControllerBase()
		{
		}

		/// <summary>
		/// Gets or sets the PermissionDescriptor.
		/// </summary>
		protected string PermissionDescriptor 
		{
			get;
			set;
		}

		private static readonly IPermissionProvider PermissionProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<IPermissionProvider>(null);

		/// <summary>
		/// 
		/// </summary>
		/// <param name="opPermissions"></param>
		/// <param name="msg"></param>
		protected void CheckPermission(Permissions? opPermissions, string msg)
		{
			if (opPermissions != null)
			{
				string hint = null;
				if ((Permissions.Execute & opPermissions) == Permissions.Execute)
				{
					/* now check security ..... */
					if (!Permission.HasExecute(PermissionDescriptor))
					{
						if (string.IsNullOrWhiteSpace(hint))
						{
							hint = PermissionProvider.GetPermissionInfo(PermissionDescriptor) + "(" +PermissionDescriptor + ")";
						}

						throw new SecurityException("No Execute Permission for " + msg)
						{
							ErrorCode = (Int32)ExceptionErrorCodes.GeneralPermission,
							ErrorDetail = hint
						};
					}
				}

				if ((Permissions.Read & opPermissions) == Permissions.Read)
				{
					if (!Permission.HasRead(PermissionDescriptor))
					{
						if (string.IsNullOrWhiteSpace(hint))
						{
							hint = PermissionProvider.GetPermissionInfo(PermissionDescriptor) + "(" + PermissionDescriptor + ")";
						}

						throw new SecurityException("No Read Permission for " + msg)
						{
							ErrorCode = (Int32)ExceptionErrorCodes.GeneralPermission,
							ErrorDetail = hint
						};
					}
				}

				if ((Permissions.Write & opPermissions) == Permissions.Write)
				{
					if (!Permission.HasWrite(PermissionDescriptor))
					{
						if (string.IsNullOrWhiteSpace(hint))
						{
							hint = PermissionProvider.GetPermissionInfo(PermissionDescriptor) + "(" + PermissionDescriptor + ")";
						}

						throw new SecurityException("No Write Permission for " + msg)
						{
							ErrorCode = (Int32)ExceptionErrorCodes.GeneralPermission,
							ErrorDetail = hint
						};
					}
				}

				if ((Permissions.Create & opPermissions) == Permissions.Create)
				{
					if (!Permission.HasCreate(PermissionDescriptor))
					{
						if (string.IsNullOrWhiteSpace(hint))
						{
							hint = PermissionProvider.GetPermissionInfo(PermissionDescriptor) + "(" + PermissionDescriptor + ")";
						}

						throw new SecurityException("No Create Permission for " + msg)
						{
							ErrorCode = (Int32)ExceptionErrorCodes.GeneralPermission,
							ErrorDetail = hint
						};
					}
				}

				if ((Permissions.Delete & opPermissions) == Permissions.Delete)
				{
					if (!Permission.HasDelete(PermissionDescriptor))
					{
						if (string.IsNullOrWhiteSpace(hint))
						{
							hint = PermissionProvider.GetPermissionInfo(PermissionDescriptor) + "(" + PermissionDescriptor + ")";
						}

						throw new SecurityException("No Delete Permission for " + msg)
						{
							ErrorCode = (Int32)ExceptionErrorCodes.GeneralPermission,
							ErrorDetail = hint
						};
					}
				}
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="companyCode"></param>
		/// <param name="opPermissions"></param>
		/// <param name="roleId"></param>
		/// <param name="userDataLanguage"></param>
		/// <param name="msg"></param>
		protected void CheckPermission(string companyCode, Permissions? opPermissions, int? roleId, string userDataLanguage, string msg)
		{
			if (opPermissions != null)
			{
				if ((Permissions.Execute & opPermissions) == Permissions.Execute)
				{
					/* now check security ..... */
					if (!Permission.HasExecute(PermissionDescriptor))
					{
						throw new SecurityException("No Execute Permission for " + msg)
						{
							ErrorCode = (Int32)ExceptionErrorCodes.GeneralPermission,
							ErrorDetail = PermissionDescriptor
						};
					}
				}

				if ((Permissions.Read & opPermissions) == Permissions.Read)
				{
					if (!Permission.HasRead(PermissionDescriptor))
					{
						throw new SecurityException("No Read Permission for " + msg)
						{
							ErrorCode = (Int32)ExceptionErrorCodes.GeneralPermission,
							ErrorDetail = PermissionDescriptor
						};
					}
				}

				if ((Permissions.Write & opPermissions) == Permissions.Write)
				{
					if (!Permission.HasWrite(PermissionDescriptor))
					{
						throw new SecurityException("No Write Permission for " + msg)
						{
							ErrorCode = (Int32)ExceptionErrorCodes.GeneralPermission,
							ErrorDetail = PermissionDescriptor
						};
					}
				}

				if ((Permissions.Create & opPermissions) == Permissions.Create)
				{
					if (!Permission.HasCreate(PermissionDescriptor))
					{
						throw new SecurityException("No Create Permission for " + msg)
						{
							ErrorCode = (Int32)ExceptionErrorCodes.GeneralPermission,
							ErrorDetail = PermissionDescriptor
						};
					}
				}

				if ((Permissions.Delete & opPermissions) == Permissions.Delete)
				{
					if (!Permission.HasDelete(PermissionDescriptor))
					{
						throw new SecurityException("No Delete Permission for " + msg)
						{
							ErrorCode = (Int32)ExceptionErrorCodes.GeneralPermission,
							ErrorDetail = PermissionDescriptor
						};
					}
				}
			}
		}

		/// <summary>
		/// Handles request.
		/// </summary>
		/// <typeparam name="TRequest">The request type</typeparam>
		/// <typeparam name="TResult">The result from request callback</typeparam>
		/// <param name="context">The request context</param>
		/// <param name="requestFn">The request call back function</param>
		/// <returns></returns>
		public HttpResponseMessage ExecuteRequestCore<TRequest, TResult>(ExecuteRequestContext<TRequest> context, Func<IPublicApiExecutionLogger, TResult> requestFn)
			where TRequest : PublicApiRequestBase
		{
			return DoExecute(context.Request, context.Request != null?context.Request.LogOptions:LogOutputInfoFlag.None, requestFn, () => 
			{
				//check companyCode and switch the current context to the Company security setting: ClientId, RoleId, etc.
				CheckPermission(context.TargetPermission, context.ErrorMessage);
			} );
		}

		/// <summary>
		/// Handles request with data return.
		/// </summary>
		/// <typeparam name="TRequest">The request type</typeparam>
		/// <typeparam name="TResult">The result from request callback</typeparam>
		/// <param name="request">The request</param>
		/// <param name="logOptions">The logOptions</param>
		/// <param name="requestFn">The request call back function</param>
		/// <param name="targetPermission">default to null. when null, no permission will be checked.</param>
		/// <param name="errorMsg">default to null. it's just hint when there is permission error</param>
		/// <returns></returns>
		public HttpResponseMessage ExecuteRequestCore<TRequest, TResult>(TRequest request, LogOutputInfoFlag logOptions, Func<IPublicApiExecutionLogger, TResult> requestFn, Permissions? targetPermission = null, string errorMsg = null) 
			where TRequest : class
		{
			return DoExecute(request, logOptions, requestFn, () =>
			{
				//check companyCode and switch the current context to the Company security setting: ClientId, RoleId, etc.
				CheckPermission(targetPermission, errorMsg);
			});
		}

		/// <summary>
		/// Handles request without data return.
		/// </summary>
		/// <typeparam name="TRequest">The request type</typeparam>
		/// <param name="context">The request context</param>
		/// <param name="requestFn">The request call back function</param>
		/// <returns></returns>
		public HttpResponseMessage ExecuteRequestCore<TRequest>(ExecuteRequestContext<TRequest> context, Action<IPublicApiExecutionLogger> requestFn)
			where TRequest : PublicApiRequestBase
		{
			Func<IPublicApiExecutionLogger, object> requestProxyFn = null; 

			if (requestFn != null)
			{
				requestProxyFn = (logger) =>
				{
					requestFn(logger);
					return VoidType.NoValue;
				};
			}

			return ExecuteRequestCore<TRequest, object>(context, requestProxyFn);
		}


		/// <summary>
		/// Handles request without data return.
		/// </summary>
		/// <typeparam name="TRequest">The request type</typeparam>
		/// <param name="request">The request</param>
		/// <param name="logOptions">The logOptions</param>
		/// <param name="requestFn">The request call back function</param>
		/// <param name="targetPermission">Default to Null, when null, no permission will be checked.</param>
		/// <param name="errorMsg">it's just hint when there is permission error</param>
		/// <returns></returns>
		public HttpResponseMessage ExecuteRequestCore<TRequest>(TRequest request, LogOutputInfoFlag logOptions, Action<IPublicApiExecutionLogger> requestFn, Permissions? targetPermission = null, string errorMsg = null)
			where TRequest : class
		{
			Func<IPublicApiExecutionLogger, object> requestProxyFn = null;

			if (requestFn != null)
			{
				requestProxyFn = (logger) =>
				{
					requestFn(logger);
					return VoidType.NoValue;
				};
			}

			return ExecuteRequestCore<TRequest, object>(request, logOptions, requestProxyFn, targetPermission, errorMsg);
		}

		/// <summary>
		/// Retreive data based on incoming request.
		/// </summary>
		/// <typeparam name="TResult">The return type</typeparam>
		/// <param name="logOptions">The logOptions</param>
		/// <param name="requestFn">The request call back function</param>
		/// <param name="targetPermission">Default to Null, when null, no permission will be checked.</param>
		/// <param name="errorMsg">it's just hint when there is permission error</param>
		/// <returns></returns>
		public HttpResponseMessage ExecuteRequestCore<TResult>(LogOutputInfoFlag logOptions, Func<IPublicApiExecutionLogger, TResult> requestFn, Permissions? targetPermission = null, string errorMsg = null)
		{
			return ExecuteRequestCore<VoidType, TResult>(VoidType.NoValue, logOptions, requestFn, targetPermission, errorMsg);
		}

		private HttpResponseMessage DoExecute<TRequest, TResult>(TRequest request, LogOutputInfoFlag logOptions, Func<IPublicApiExecutionLogger, TResult> requestFn, Action securityCheckFn)
			where TRequest : class
		{
			if (request == null)
			{
				return Request.HttpContext.GetHttpRequestMessage().CreateErrorResponse(HttpStatusCode.BadRequest, " The Input Json is not valid! The input parameter cannot be null!");
			}

			if (!ModelState.IsValid)
			{
				return Request.HttpContext.GetHttpRequestMessage().CreateErrorResponse(HttpStatusCode.BadRequest, ModelState);
			}

			var logger = new PublicApiValidationLogger();
			logger.IsDebugEnabled = (LogOutputInfoFlag.Debug & logOptions) != LogOutputInfoFlag.None;
			logger.IsInfoEnabled = (LogOutputInfoFlag.Info & logOptions) != LogOutputInfoFlag.None;
			logger.IsWarningEnabled = (LogOutputInfoFlag.Warning & logOptions) != LogOutputInfoFlag.None;
			logger.IsErrorEnabled = true;
			bool isDetectionOnly = (LogOutputInfoFlag.DetectionOnly & logOptions) != LogOutputInfoFlag.None;

			const string ERROR_KEY = "Errors";
			const string WARNING_KEY = "Warning";

			const string INFO_KEY = "Info";
			const string DEBUG_KEY = "Debug";

			var response = new HttpResponseMessage(HttpStatusCode.OK);
			object objectToSerialize = null;

			Dictionary<string, object> infoList = new Dictionary<string, object>();

			Stopwatch sw = Stopwatch.StartNew();
			logger.WriteDebug(string.Format("[{0}] : Begin to process request", DateTime.UtcNow.ToString()));
			bool hasException = false;
			try
			{
				// check companyCode and switch the current context to the Company security setting: ClientId, RoleId, etc.
				if (securityCheckFn != null)
					securityCheckFn();

				if (requestFn != null)
				{
					if (VoidType.NoValue != request)
					{
						TruncateRequestDto(request, logger, null);
					}

					objectToSerialize = requestFn(logger);
				}
			}
			catch (Exception ex)
			{
				hasException = true;
				

				if (ex is SecurityException)
				{
					var sEx = (SecurityException)ex;
					response.StatusCode = HttpStatusCode.Forbidden;
					logger.WriteError(string.Format("SecurityException: ErrorCode: {0}, ErrorMessage: {1}, ErrorDetail: {2}", sEx.ErrorCode, sEx.ErrorMessage, sEx.ErrorDetail));
				}
				else
				{
					response.StatusCode = HttpStatusCode.InternalServerError;
					logger.WriteError(ex.Message);
				}

				var rootException = GetRootException(ex);
				logger.WriteDebug("=======ROOT EXCEPTION===================");
				var errorMessage = rootException.Message;
				if(rootException is ServiceLayerException)
				{
					var slex = ((ServiceLayerException)rootException);
					errorMessage = string.Format("ServiceLayerException: ErrorCode: {0}, ErrorMessage: {1}, ErrorDetail: {2}", slex.ErrorCode, slex.ErrorMessage, slex.ErrorDetail);
				}
				else if( rootException is BusinessLayerException)
				{
					var bizlex = (BusinessLayerException)rootException;
					errorMessage = string.Format("BusinessLayerException: ErrorCode: {0}, ErrorMessage: {1}, ErrorDetail: {2}", bizlex.ErrorCode, bizlex.ErrorMessage, bizlex.ErrorDetail);
				}
				else if (rootException is ResourceAccessLayerException)
				{
					var reslex = (ResourceAccessLayerException)rootException;
					errorMessage = string.Format("ResourceAccessLayerException: ErrorCode: {0}, ErrorMessage: {1}, ErrorDetail: {2}", reslex.ErrorCode, reslex.ErrorMessage, reslex.ErrorDetail);
				}
				else if (rootException is ValidationException)
				{
					var validateEx = (ValidationException)rootException;
					errorMessage = string.Format("ValidationException: ErrorCode: {0}, ErrorMessage: {1}, ErrorDetail: {2}", validateEx.ErrorCode, validateEx.ErrorMessage, validateEx.ErrorDetail);
				}
				logger.WriteError(errorMessage);
				logger.WriteDebug(errorMessage);

#region EntityValidationException
				var dbEntityValidationException = rootException as DbEntityValidationException;
				if (dbEntityValidationException != null)
				{
					logger.WriteDebug("=====ENTITY VALIDATION EXCEPTION=======");

					foreach (var dbEntityValidationResult in dbEntityValidationException.EntityValidationErrors)
					{
						if (dbEntityValidationResult.Entry != null && dbEntityValidationResult.Entry.Entity != null)
							logger.WriteDebug("For entity: " + dbEntityValidationResult.Entry.Entity.ToString());

						foreach (var dbValidationError in dbEntityValidationResult.ValidationErrors)
						{
							logger.WriteDebug(string.Format("ResultProperty {0}: {1}", dbValidationError.PropertyName, dbValidationError.ErrorMessage));
						}
					}
					logger.WriteDebug("========================================");
				}
#endregion

				logger.WriteDebug("=======S T A C K T R A C E==============");
				logger.WriteDebug(rootException.StackTrace);
				logger.WriteDebug("========================================");
			}

			logger.WriteDebug(string.Format("[{0}] : Finish processing request", DateTime.UtcNow.ToString()));
			logger.WriteDebug(string.Format("[{0}] : Execution Time cost [{1}] ms in total", DateTime.UtcNow.ToString(), sw.ElapsedMilliseconds));
			sw.Stop();

			if (objectToSerialize == null || objectToSerialize == VoidType.NoValue || isDetectionOnly)
			{
				IEnumerable<PublicApiValidationResult> validationResults = null;

				if ((LogOutputInfoFlag.Info & logOptions) != LogOutputInfoFlag.None)
				{
					validationResults = logger.ValidationResults.Where(e => e.MessageType == ValidationMessageType.Information);
					if (validationResults.Any()) //Information
					{
						var validationInfo = validationResults.Select(e => e.ErrorContent);
						infoList.Add(INFO_KEY, validationInfo);
					}
				}

				if ((LogOutputInfoFlag.Debug & logOptions) != LogOutputInfoFlag.None)
				{
					validationResults = logger.ValidationResults.Where(e => e.MessageType == ValidationMessageType.Debug);
					if (validationResults.Any()) //Debug
					{
						var validationInfo = validationResults.Select(e => e.ErrorContent);
						infoList.Add(DEBUG_KEY, validationInfo);
					}
				}

				validationResults = logger.ValidationResults.Where(e => e.MessageType == ValidationMessageType.Error);
				if (validationResults.Any()) //error
				{
					hasException = true;
					if (response.StatusCode == HttpStatusCode.OK)
					{
						response.StatusCode = HttpStatusCode.InternalServerError;
					}

					if ((LogOutputInfoFlag.Error & logOptions) != LogOutputInfoFlag.None)
					{
						var validationInfo = validationResults.Select(e => e.ErrorContent);
						infoList.Add(ERROR_KEY, validationInfo);
					}
				}

				if ((LogOutputInfoFlag.Warning & logOptions) != LogOutputInfoFlag.None)
				{
					validationResults = logger.ValidationResults.Where(e => e.MessageType == ValidationMessageType.Warning);
					if (validationResults.Any())//warnings
					{
						var validationInfo = validationResults.Select(e => e.ErrorContent);
						infoList.Add(WARNING_KEY, validationInfo);
					}
				}
			}


			if ((hasException || objectToSerialize == VoidType.NoValue || isDetectionOnly) && infoList.Any())
			{
				objectToSerialize = infoList;
			}

			if (objectToSerialize != null && (objectToSerialize != VoidType.NoValue || isDetectionOnly))
			{
				response.Content = new StringContent(InquiryFactory.JsonfyObject(objectToSerialize, true));
				response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
			}

			return response;
		}

		private class VoidType
		{
			/// <summary>
			/// 
			/// </summary>
			public static readonly VoidType NoValue = new VoidType();
		}

		private Exception GetRootException(Exception e)
		{
			var header = e;
			while (header.InnerException != null)
			{
				header = header.InnerException;
			}

			return header;
		}

		private void TruncateRequestDto(object request, IPublicApiExecutionLogger logger, string propertyPath)
		{
			if (request == null)
			{
				return;
			}

			const string Length_Template = "The length of <{0}> is truncated from <{1}> to <{2}>";
			const string Value_Template = "The value of <{0}> is truncated from <{1}> to <{2}>; ";

			var properties = request.GetType().GetProperties(BindingFlags.Public | BindingFlags.SetProperty | BindingFlags.Instance);
			foreach (var property in properties)
			{
				if (property.PropertyType == typeof(string))
				{
					var attribute = property.GetCustomAttribute<TruncateAttribute>();
					if (attribute != null)
					{
						var propertyValue = property.GetValue(request) as string;
						if (!string.IsNullOrEmpty(propertyValue))
						{
							if (propertyValue.Length > attribute.MaxLength)
							{
								int oldLength = propertyValue.Length;
								string oldValue = propertyValue;
								propertyValue = propertyValue.Substring(0, attribute.MaxLength);
								//truncate value
								property.SetValue(request, propertyValue);
								string currentPropertyPath = string.IsNullOrEmpty(propertyPath) ? property.Name : propertyPath + "." + property.Name;
								var msg1 = string.Format(Value_Template, currentPropertyPath, oldValue, propertyValue);
								var msg2 = string.Format(Length_Template, propertyPath, oldLength, attribute.MaxLength);
								logger.WriteWarning(msg1 + msg2);
							}
						}
					}
				}
				else if (property.PropertyType == typeof(TranslateTextDescriptorApiDto))
				{
					var attribute = property.GetCustomAttribute<TruncateAttribute>();
					if (attribute != null)
					{
						var trDescriptor = property.GetValue(request) as TranslateTextDescriptorApiDto;
						if (trDescriptor != null && !string.IsNullOrEmpty(trDescriptor.Description))
						{
							if (trDescriptor.Description.Length > attribute.MaxLength)
							{
								int oldLength = trDescriptor.Description.Length;
								string oldValue = trDescriptor.Description;
								//truncate value
								trDescriptor.Description = trDescriptor.Description.Substring(0, attribute.MaxLength);
		
								string currentPropertyPath = string.IsNullOrEmpty(propertyPath) ? property.Name : propertyPath + "." + property.Name + ".Description";
								var msg1 = string.Format(Value_Template, currentPropertyPath, oldValue, trDescriptor.Description);
								var msg2 = string.Format(Length_Template, currentPropertyPath, oldLength, attribute.MaxLength);
								logger.WriteWarning(msg1 + msg2);

								if (trDescriptor.OtherLanguages != null)
								{
									currentPropertyPath = string.IsNullOrEmpty(propertyPath) ? property.Name : propertyPath + "." + property.Name + ".OtherLanguages.Description";
									foreach (var otherItem in trDescriptor.OtherLanguages)
									{
										if (!string.IsNullOrEmpty(otherItem.Description))
										{
											if (otherItem.Description.Length > attribute.MaxLength)
											{
												oldLength = otherItem.Description.Length;
												oldValue = otherItem.Description;
												//truncate value
												otherItem.Description = otherItem.Description.Substring(0, attribute.MaxLength);
												msg1 = string.Format(Value_Template, currentPropertyPath, oldValue, otherItem.Description);
												msg2 = string.Format(Length_Template, currentPropertyPath, oldLength, attribute.MaxLength);
												logger.WriteWarning(msg1 + msg2);
											}
										}
									}
								}
							}
						}
					}
				}
				else if (property.PropertyType.IsClass || property.PropertyType.IsInterface)
				{
					var attribute = property.GetCustomAttribute<TruncatableObjectAttribute>();
					if (attribute != null)
					{
						var propertyInstance = property.GetValue(request);
						if (propertyInstance != null)
						{
							if (propertyInstance is IEnumerable)
							{
								//it's IEnumerable
								var realInstance = propertyInstance as IEnumerable;
								string currentPropertyPath = string.IsNullOrEmpty(propertyPath) ? property.Name : propertyPath + "." + property.Name;
								foreach (var item in realInstance)
								{
									TruncateRequestDto(item, logger, currentPropertyPath);
								}
							}
							else
							{
								//it's a object
								string currentPropertyPath = string.IsNullOrEmpty(propertyPath) ? property.Name : propertyPath + "." + property.Name;
								TruncateRequestDto(propertyInstance, logger, currentPropertyPath);
							}
						}
					}
				}
			}
		}
	}
}
