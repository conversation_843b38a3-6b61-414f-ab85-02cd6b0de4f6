﻿
using System;
using System.Linq;

namespace RIB.Visual.Basics.Api.Common
{

	/// <summary>
	/// Declares a variable with a specified value for use in API documentation comments.
	/// </summary>
	[AttributeUsage(AttributeTargets.Class, AllowMultiple = true, Inherited = false)]
	public sealed class ApiDocResponseExcludingPropertiesAttribute : ApiDocExcludingPropertiesAttributeBase
	{
		/// <summary>
		/// Initializes a new instance.
		/// </summary>
		/// <param name="actionName">The name of the action that will be effected.</param>
		/// <param name="excludingProperties">The properties of dto that will be excluded in API documentation comment.</param>
		/// <exception cref="ArgumentNullException">Any of the arguments is <see langword="null"/>.</exception>
		public ApiDocResponseExcludingPropertiesAttribute(String actionName, String[] excludingProperties)
			: base(actionName, excludingProperties)
		{

		}

		/// <summary>
		/// Initializes a new instance.
		/// </summary>
		/// <param name="actionName">The name of the action that will be effected.</param>
		/// <param name="excludingProperties">The properties of dto that will be excluded in API documentation comment.</param>
		/// <exception cref="ArgumentNullException">Any of the arguments is <see langword="null"/>.</exception>
		public ApiDocResponseExcludingPropertiesAttribute(String actionName, String excludingProperties)
			:base(actionName,excludingProperties)
		{
			
		}
	}
}
