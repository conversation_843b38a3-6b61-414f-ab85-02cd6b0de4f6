{"run": {"command": "nx run modules-procurement-inventory:lint", "startTime": "2025-06-30T14:07:45.548Z", "endTime": "2025-06-30T14:07:49.707Z", "inner": false}, "tasks": [{"taskId": "modules-procurement-inventory:lint", "target": "lint", "projectName": "modules-procurement-inventory", "hash": "11713987052900535117", "startTime": "2025-06-30T14:07:45.600Z", "endTime": "2025-06-30T14:07:49.706Z", "params": "", "cacheStatus": "cache-miss", "status": 1}]}