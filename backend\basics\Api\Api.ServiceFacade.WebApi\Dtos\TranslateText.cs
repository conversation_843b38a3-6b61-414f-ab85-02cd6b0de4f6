﻿using RIB.Visual.Basics.Api.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// Represents a translate text.
	/// </summary>
	public class TranslateText
	{
		/// <summary>
		/// Constructor.
		/// </summary>
		public TranslateText()
		{

		}

		/// <summary>
		/// Description.
		/// </summary>
		[System.ComponentModel.DataAnnotations.StringLength(2000)]
		public string Description
		{
			get;
			set;
		}

		/// <summary>
		/// Culture.
		/// </summary>
		[System.ComponentModel.DataAnnotations.Required]
		[Culture]
		public string Culture
		{
			get;
			set;
		}
	}
}
