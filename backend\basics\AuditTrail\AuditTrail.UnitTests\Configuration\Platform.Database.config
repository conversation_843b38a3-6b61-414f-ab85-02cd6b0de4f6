<?xml version="1.0" encoding="utf-8"?>
<dbsettings>
	<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
		<xs:element name="dbsettings">
			<xs:complexType>
				<xs:sequence>
					<xs:element ref="dbproviders"/>
				</xs:sequence>
			</xs:complexType>
		</xs:element>
		<xs:element name="dbproviders">
			<xs:complexType>
				<xs:sequence>
					<xs:element ref="dbprovider" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:attribute name="default" use="required">
					<xs:simpleType>
						<xs:restriction base="xs:string"/>
					</xs:simpleType>
				</xs:attribute>
			</xs:complexType>
		</xs:element>
		<xs:element name="dbprovider">
			<xs:complexType>
				<xs:attribute name="username">
					<xs:simpleType>
						<xs:restriction base="xs:string"/>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="type" use="required">
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:enumeration value="MSSQL"/>
							<xs:enumeration value="Oracle"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="plainpassword">
					<xs:simpleType>
						<xs:restriction base="xs:string"/>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="name" use="required">
					<xs:simpleType>
						<xs:restriction base="xs:string"/>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="integratedsecurity" type="xs:boolean" use="required"/>
				<xs:attribute name="encryptpassword">
					<xs:simpleType>
						<xs:restriction base="xs:string"/>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="datasource" use="required">
					<xs:simpleType>
						<xs:restriction base="xs:string"/>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="databasename">
					<xs:simpleType>
						<xs:restriction base="xs:string"/>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="serviceid">
					<xs:simpleType>
						<xs:restriction base="xs:string"/>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="servicename">
					<xs:simpleType>
						<xs:restriction base="xs:string"/>
					</xs:simpleType>
				</xs:attribute>
				<xs:attribute name="port">
					<xs:simpleType>
						<xs:restriction base="xs:integer"/>
					</xs:simpleType>
				</xs:attribute>
			</xs:complexType>
		</xs:element>
	</xs:schema>

	<dbproviders default="default">
		<dbprovider name="default"
                type="MSSQL"
                datasource="sql12-dev\DEV"
                databasename="iTWOCloud"
                integratedsecurity="true"
                username=""
                encryptpassword=""
                port=""
                servicename=""
                serviceid=""/>

		<dbprovider name="sql12"
							type="MSSQL"
							datasource="sql12-dev\DEV"
							databasename="iTWOCloud"
							integratedsecurity="true"
							username=""
							encryptpassword=""
							port=""
							servicename=""
							serviceid=""/>

	</dbproviders>
</dbsettings>
