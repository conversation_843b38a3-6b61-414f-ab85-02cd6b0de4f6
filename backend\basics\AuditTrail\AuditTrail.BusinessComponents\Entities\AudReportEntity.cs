﻿using RIB.Visual.Platform.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RIB.Visual.Basics.AuditTrail.BusinessComponents
{
	/// <summary>
	/// Generic entity for all log tables
	/// </summary>
	public partial class AudReportEntity
	{

		/// <summary>
		/// 
		/// </summary>
		public int Id { get; set; }
		
		/// <summary>
		/// 
		/// </summary>
      	public DateTime DateAndTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Description { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string UserName { get; set; }
	
		/// <summary>
		/// 
		/// </summary>
		public string Action { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int? ObjectFK { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int RecordFK { get; set; }

 		/// <summary>
		/// 
		/// </summary>
		public string Container { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string Column { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string OldValue { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string NewValue { get; set; }

	}
}
