<?xml version="1.0" encoding="utf-8"?>
<ribProductSpecification>
	<!-- File Archive Root Default Values -->
	<FileArchives>
		<FileArchive Name="2Col" RootPath="" AppId="D8644227993B494fB52BBE33E9D15CB5" Level="2" />
	</FileArchives>
	<!-- Scheduler Task Types -->
	<TaskSchedule>
		<GlobalParameter>
			<AutoSave>True</AutoSave>
			<ParallelJobNumber>4</ParallelJobNumber>
		</GlobalParameter>
		<!--<Task Name="Synchronize ADS" AssemblyName="RIB.Visual.Act.UserManagement.SchedulerTask" Class="RIB.Visual.Act.UserManagement.SchedulerTask.AdsSynchronizeTask"/>-->
		<!--<Task Name="Database Backup" AssemblyName="RIB.Visual.Services.SchedulerTasks.DatabaseBackupTask" Class="RIB.Visual.Services.SchedulerTasks.DatabaseBackupTask.DatabaseBackupTask"/>-->
		<!--<Task Name="Synchronize Project Structure" AssemblyName="RIB.Visual.Services.ITwoSynchronize.ProjectStructure" Class="RIB.Visual.Services.ITwoSynchronize.ProjectStructure.ProjectStructureSyncTask"/>-->
		<!--<Task Name="2Clash Calculation" AssemblyName="RIB.Visual.Clash.App.BusinessComponents" Class="RIB.Visual.Clash.App.BusinessComponents.SchedulerTask.CalculationTask"/> -->
		<!--<Task Name="Uplift" AssemblyName="RIB.Visual.PlugIn.Uplift.BusinessComponents" Class="RIB.Visual.Uplift.BusinessComponents.UpliftSchedulerTask"/>-->
		<!--<Task Name="2Q Calculation" AssemblyName="RIB.Visual.Quantifier.Start.BusinessComponents" Class="RIB.Visual.Quantifier.Start.BusinessComponents.QuantifierSchedulerTask"/>-->
	</TaskSchedule>
</ribProductSpecification>
