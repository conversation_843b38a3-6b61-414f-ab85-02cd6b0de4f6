using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using RIB.Visual.Awp.Main.BusinessComponents.Logic;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Common.Enum;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.CostCodes.BusinessComponents;
using RIB.Visual.Basics.Material.BusinessComponents;
using RIB.Visual.Basics.MaterialCatalog.BusinessComponents;
using RIB.Visual.Basics.ProcurementStructure.BusinessComponents;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;
using static System.Runtime.InteropServices.JavaScript.JSType;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Awp.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class AwpMainPackageStructureResourceLogic : RVPBizComp.LogicBase
	{

		private readonly MdcCommoditySearchVLogic _MdcCommoditySearchVLogic = new MdcCommoditySearchVLogic();

		/// <summary>
		/// 
		/// </summary>
		/// <param name="filterData"></param>
		/// <returns></returns>
		public IEnumerable<PackageStructureResourceEntity> GetPackageStructureResources(FilterData filterData)
		{
			if (filterData.LineItemIds == null || !filterData.LineItemIds.Any())
			{
				return new List<PackageStructureResourceEntity>();
			}

			var estimateMainResourceProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainResourceLogic>();
			var resources = estimateMainResourceProvider.GetPackageStructureResources(filterData.EstHeaderFk, filterData.LineItemIds).ToList();

			var mdcCostCodeIds = resources.Where(e => !e.MdcMaterialFk.HasValue && e.MdcCostCodeFk.HasValue).Select(e => e.MdcCostCodeFk.Value).ToList();
			var prjCostCodeIds = resources.Where(e => !e.MdcMaterialFk.HasValue && e.ProjectCostCodeFk.HasValue).Select(e => e.ProjectCostCodeFk.Value).ToList();
			var materialIds = resources.Where(e => e.MdcMaterialFk.HasValue).Select(e => e.MdcMaterialFk.Value).ToList();
			if (mdcCostCodeIds != null && mdcCostCodeIds.Any())
			{
				var costCodeDics = new BasicsCostCodesLogic().GetTreeList(new CostCodeTreeFilterOption()
				{
					OnlyGetEstTypeCodeCode = false,
					FilterByCompany = true
				}).ToDictionary(e => e.Id, e => e);
				foreach (var res in resources)
				{
					if (res.MdcCostCodeFk.HasValue && costCodeDics.ContainsKey(res.MdcCostCodeFk.Value))
					{
						var costCode = costCodeDics[res.MdcCostCodeFk.Value];
						res.Code = costCode.Code;
						res.DescriptionInfo = costCode.DescriptionInfo;
						if (costCode.UomFk.HasValue)
						{
							res.BasUomFk = costCode.UomFk.Value;
						}
					}
				}
			}

			if (prjCostCodeIds != null && prjCostCodeIds.Any())
			{
				var projCoCodeLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();
				var projectCostCodeDictionary = projCoCodeLogic.GetAllCostCodesInProject(filterData.ProjectId).ToDictionary(e => e.Id, e => e);
				foreach (var res in resources)
				{
					if (res.ProjectCostCodeFk.HasValue && projectCostCodeDictionary.ContainsKey(res.ProjectCostCodeFk.Value))
					{
						var prjCostCode = projectCostCodeDictionary[res.ProjectCostCodeFk.Value];
						res.Code = prjCostCode.Code;
						res.DescriptionInfo = prjCostCode.DescriptionInfo;
						if (prjCostCode.UomFk.HasValue)
						{
							res.BasUomFk = prjCostCode.UomFk.Value;
						}
					}
				}
			}
			if (materialIds != null && materialIds.Any())
			{
				var materialDics = _MdcCommoditySearchVLogic.GetSearchMaterialByIds(materialIds).ToDictionary(e => e.Id, e => e);
				foreach (var res in resources)
				{
					if (res.MdcMaterialFk.HasValue && materialDics.ContainsKey(res.MdcMaterialFk.Value))
					{
						var material = materialDics[res.MdcMaterialFk.Value];
						res.Code = material.Code;
						res.DescriptionInfo = material.DescriptionInfo;
						res.BasUomFk = material.BasUomFk;
					}
				}
			}

			int index = 0;
			var result = resources.GroupBy(e => new
			{
				Code = e.Code,
				EstResourceTypeFk = e.EstResourceTypeFk,
			}).Select(g =>
			{
				var packageStructureResourceEntity = new PackageStructureResourceEntity();

				packageStructureResourceEntity.Id = index++;
				packageStructureResourceEntity.Code = g.Key.Code;
				packageStructureResourceEntity.Selected = true;
				packageStructureResourceEntity.EstResourceTypeFk = g.Key.EstResourceTypeFk;
				packageStructureResourceEntity.DescriptionInfo = g.Select(e => e.DescriptionInfo).Distinct().Count() == 1 ? g.FirstOrDefault().DescriptionInfo : new DescriptionTranslateType();
				packageStructureResourceEntity.Quantity = g.Sum(x => x.Quantity);
				packageStructureResourceEntity.QuantityTotal = g.Sum(x => x.QuantityTotal);
				packageStructureResourceEntity.CostUnit = g.Select(e => e.CostUnit).Distinct().Count() == 1 ? (decimal?)g.FirstOrDefault().CostUnit : null;
				packageStructureResourceEntity.UomFk = g.Select(e => e.BasUomFk).Distinct().Count() == 1 ? g.FirstOrDefault().BasUomFk : null;
				packageStructureResourceEntity.BasCurrencyFk = g.Select(e => e.BasCurrencyFk).Distinct().Count() == 1 ? g.FirstOrDefault().BasCurrencyFk : null;
				packageStructureResourceEntity.CostTotal = g.Sum(x => x.CostTotal);
				packageStructureResourceEntity.Budget = g.Sum(x => x.Budget);
				packageStructureResourceEntity.MdcMaterialFk = g.Select(e => e.MdcMaterialFk).FirstOrDefault();
				packageStructureResourceEntity.MdcCostCodeFk = g.Key.EstResourceTypeFk == (int)EstResourceType.CostCode ? g.Select(e => e.MdcCostCodeFk).First() : null;
				packageStructureResourceEntity.ProjectCostCodeFk = g.Key.EstResourceTypeFk == (int)EstResourceType.CostCode ? g.Select(e => e.ProjectCostCodeFk).First() : null;
				packageStructureResourceEntity.LineItemResourceIds = g.Select(e => new Tuple<int, int, int>(e.EstHeaderFk, e.EstLineItemFk, e.Id));
				return packageStructureResourceEntity;
			}).OrderBy(o => o.Code).ToList();

			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="filterData"></param>
		/// <returns></returns>
		public IEnumerable<PackageStructureResourceFilterEntity> FilterByCostCode(FilterData filterData)
		{
			if (filterData.LineItemIds == null || !filterData.LineItemIds.Any() || filterData.packageStructureResourceFilterType < 0)
			{
				return new List<PackageStructureResourceFilterEntity>();
			}

			var estimateMainResourceProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainResourceLogic>();
			var resources = estimateMainResourceProvider.GetPackageStructureResources(filterData.EstHeaderFk, filterData.LineItemIds).Where(e => e.EstResourceTypeFk == (int)EstResourceType.CostCode).ToList();

			if (resources == null || !resources.Any())
			{
				return new List<PackageStructureResourceFilterEntity>();
			}
			var CostCodeIds = resources.Select(e => e.MdcCostCodeFk.HasValue ? e.MdcCostCodeFk : e.ProjectCostCodeFk).ToList();

			var result = new Dictionary<int, PackageStructureResourceFilterEntity>();
			var flattenDict = GetMasterCostCodeAndProjectCostCodeChildOnly(filterData.ProjectId).Flatten(e => e.CostCodeChildren).GroupBy(e => e.Id).ToDictionary(g => g.Key, g => g.ToList());

			var packageResourceCostCodes = flattenDict.Where(e => CostCodeIds.Contains(e.Key)).Select(e => e.Value.FirstOrDefault()).ToList();
			if (filterData.packageStructureResourceFilterType == (int)PackageStructureResourceFilterType.FilterByMajorCostCode)
			{
				foreach ( var currentCostCode in packageResourceCostCodes)
				{
					var topParentCostCode = FindTopParent(flattenDict, currentCostCode);
					if (topParentCostCode == null)
					{
						continue;
					}

					if (topParentCostCode.Id == currentCostCode.Id && !result.ContainsKey(topParentCostCode.Id))
					{
						result.Add(topParentCostCode.Id, CreateFilterEntity(topParentCostCode, currentCostCode, null));
					}

					if (topParentCostCode != null && topParentCostCode.Id != currentCostCode.Id)
					{
						if (!result.ContainsKey(topParentCostCode.Id))
						{
							result.Add(topParentCostCode.Id, CreateFilterEntity(topParentCostCode, currentCostCode, null));
						}
						else if (currentCostCode.IsCustomProjectCostCode)
						{
							if (result[topParentCostCode.Id].ProjectCostCodeIds == null)
							{
								result[topParentCostCode.Id].ProjectCostCodeIds = new List<int> { currentCostCode.Id };
							}
							else
							{
								result[topParentCostCode.Id].ProjectCostCodeIds.Add(currentCostCode.Id);
							}
						}
						else
						{
							if (result[topParentCostCode.Id].MdcCostCodeIds == null)
							{
								result[topParentCostCode.Id].MdcCostCodeIds = new List<int> { currentCostCode.Id };
							}
							else
							{
								result[topParentCostCode.Id].MdcCostCodeIds.Add(currentCostCode.Id);
							}
						}
					}
				}
			}

			if (filterData.packageStructureResourceFilterType == (int)PackageStructureResourceFilterType.FilterByCostCodePrcStructure)
			{
				var prcStructureLogic = new PrcStructureLogic();
				var prcStructureCostCodeDics = packageResourceCostCodes.Where(e => e.PrcStructureFk.HasValue).GroupBy(e => e.PrcStructureFk).ToDictionary(g => g.Key.Value, g => g.ToList());
				var unAssignmentPrcStructureCostCode = packageResourceCostCodes.Where(e => !e.PrcStructureFk.HasValue).ToList();

				var prcStructureDics = prcStructureLogic.GetPrcStructureHierarchyByIds(prcStructureCostCodeDics.Keys).Flatten(e => e.PrcChildren).ToDictionary(e => e.Id, e => e);
				foreach (var costCodeGroupDic in prcStructureCostCodeDics)
				{
					if (!prcStructureDics.ContainsKey(costCodeGroupDic.Key))
					{
						continue;
					}

					var currentPrcStructure = prcStructureDics[costCodeGroupDic.Key];
					AddAllNodeStructure(currentPrcStructure, prcStructureDics, result);
				}
				if (unAssignmentPrcStructureCostCode != null && unAssignmentPrcStructureCostCode.Any())
				{
					result.Add(-1, new PackageStructureResourceFilterEntity()
					{
						Id = -1,
						Code = "unAssigiment Package Structure",
						MdcCostCodeIds = unAssignmentPrcStructureCostCode.Where(e => !e.IsCustomProjectCostCode).Select(e => e.Id).ToList(),
						ProjectCostCodeIds = unAssignmentPrcStructureCostCode.Where(e => e.IsCustomProjectCostCode).Select(e => e.Id).ToList()
					});
				}

				foreach (var prcStructureResult in result)
				{
					if (!prcStructureCostCodeDics.ContainsKey(prcStructureResult.Key))
					{
						continue;
					}
					var costCodes = prcStructureCostCodeDics[prcStructureResult.Key];
					prcStructureResult.Value.MdcCostCodeIds = costCodes.Where(e => !e.IsCustomProjectCostCode).Select(e => e.Id).ToList();
					prcStructureResult.Value.ProjectCostCodeIds = costCodes.Where(e => e.IsCustomProjectCostCode).Select(e => e.Id).ToList();
				}
			}

			return result.Values.Where(e => !e.ParentFk.HasValue);			
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="filterData"></param>
		/// <returns></returns>
		public IEnumerable<PackageStructureResourceFilterEntity> FilterByMaterial(FilterData filterData)
		{
			if (filterData.LineItemIds == null || !filterData.LineItemIds.Any() || filterData.packageStructureResourceFilterType < 0)
			{
				return new List<PackageStructureResourceFilterEntity>();
			}

			var estimateMainResourceProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainResourceLogic>();
			var resources = estimateMainResourceProvider.GetPackageStructureResources(filterData.EstHeaderFk, filterData.LineItemIds).Where(e => e.EstResourceTypeFk == (int)EstResourceType.Material).ToList();
			if (resources == null || !resources.Any())
			{
				return new List<PackageStructureResourceFilterEntity>();
			}

			var materialIds = resources.Where(e => e.MdcMaterialFk.HasValue).Select(e => e.MdcMaterialFk.Value).ToList();
			var filterMaterials = _MdcCommoditySearchVLogic.GetSearchMaterialByIds(materialIds);
			var result = new Dictionary<int, PackageStructureResourceFilterEntity>();
			if (filterData.packageStructureResourceFilterType == (int)PackageStructureResourceFilterType.FilterByMaterialCatalog)
			{
				int _mdcContextId = this.GetCompanyInfoProvider().GetMasterDataContext();

				var materialGroupByCatelog = filterMaterials.GroupBy(e => e.MdcMaterialCatalogFk).ToDictionary(g => g.Key, g => g.ToList());
				var materialCatalogIds = materialGroupByCatelog.Keys;
				var materialCatalogs = new MaterialCatalogLogic().GetSearchList(e => e.MdcContextFk == _mdcContextId && e.IsLive, true)
												.Where(e => materialCatalogIds.Contains(e.Id));
				foreach (var catelog in materialCatalogs)
				{
					if (!materialGroupByCatelog.ContainsKey(catelog.Id))
					{
						continue;
					}
					var materials = materialGroupByCatelog[catelog.Id];
					result.Add(catelog.Id, new PackageStructureResourceFilterEntity()
					{
						Id = catelog.Id,
						Code = catelog.Code,
						DescriptionInfo = catelog.DescriptionInfo,
						MdcMaterialIds = materials.Select(e => e.Id).ToList()
					});
				}
			}

			if (filterData.packageStructureResourceFilterType == (int)PackageStructureResourceFilterType.FilterByMaterialPrcStructure)
			{
				var prcStructureLogic = new PrcStructureLogic();
				var prcStructureMaterialDics = filterMaterials.Where(e => e.PrcStructureFk.HasValue).GroupBy(e => e.PrcStructureFk).ToDictionary(g => g.Key.Value, g => g.ToList());
				var unAssignmentPrcStructureMaterial = filterMaterials.Where(e => !e.PrcStructureFk.HasValue).ToList();

				var prcStructureDics = prcStructureLogic.GetPrcStructureHierarchyByIds(prcStructureMaterialDics.Keys).Flatten(e => e.PrcChildren).ToDictionary(e => e.Id, e => e);
				foreach (var MaterialGroupDic in prcStructureMaterialDics)
				{
					if (!prcStructureDics.ContainsKey(MaterialGroupDic.Key))
					{
						continue;
					}

					var currentPrcStructure = prcStructureDics[MaterialGroupDic.Key];
					AddAllNodeStructure(currentPrcStructure, prcStructureDics, result);
				}
				if (unAssignmentPrcStructureMaterial != null && unAssignmentPrcStructureMaterial.Any())
				{
					result.Add(-1, new PackageStructureResourceFilterEntity()
					{
						Id = -1,
						Code = "unAssigiment Package Structure",
						MdcMaterialIds = unAssignmentPrcStructureMaterial.Select(e => e.Id).ToList()
					});
				}

				foreach (var prcStructureResult in result)
				{
					if (!prcStructureMaterialDics.ContainsKey(prcStructureResult.Key))
					{
						continue;
					}
					var materials = prcStructureMaterialDics[prcStructureResult.Key];
					prcStructureResult.Value.MdcMaterialIds = materials.Select(e => e.Id).ToList();
				}
			}

			return result.Values.Where(e => !e.ParentFk.HasValue);
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="currentprcStructure"></param>
		/// <param name="prcStructureDics"></param>
		/// <param name="result"></param>
		/// <returns></returns>
		public void AddAllNodeStructure(IPrcStructureEntity currentprcStructure, Dictionary<int, IPrcStructureEntity> prcStructureDics, Dictionary<int, PackageStructureResourceFilterEntity> result)
		{
			if (!result.ContainsKey(currentprcStructure.Id))
			{
				result.Add(currentprcStructure.Id, CreateFilterEntity(null, null, currentprcStructure));
			}

			if (!currentprcStructure.PrcStructureFk.HasValue || !prcStructureDics.ContainsKey(currentprcStructure.PrcStructureFk.Value))
			{
				return;
			}

			var parent = prcStructureDics[currentprcStructure.PrcStructureFk.Value];
			result[currentprcStructure.Id].ParentFk = parent.Id;
			if (!result.ContainsKey(parent.Id))
			{
				result.Add(parent.Id, CreateFilterEntity(null, null, parent));
			}
			result[parent.Id].PackageStructureResourceFilter.Add(result[currentprcStructure.Id]);
			if (parent.PrcStructureFk.HasValue && prcStructureDics.ContainsKey(parent.PrcStructureFk.Value))
			{
				AddAllNodeStructure(parent, prcStructureDics, result);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="topParentCostCode"></param>
		/// <param name="currentCostCode"></param>
		/// <param name="parentPackageStructure"></param>
		/// <returns></returns>
		public PackageStructureResourceFilterEntity CreateFilterEntity(CostCodeEntity topParentCostCode, CostCodeEntity currentCostCode, IPrcStructureEntity parentPackageStructure)
		{
			if (parentPackageStructure != null)
			{

				return new PackageStructureResourceFilterEntity()
				{
					Id = parentPackageStructure.Id,
					Code = parentPackageStructure.Code,
					DescriptionInfo = parentPackageStructure.DescriptionInfo,
					PackageStructureResourceFilter = new List<PackageStructureResourceFilterEntity>()
				};
			}

			return new PackageStructureResourceFilterEntity()
			{
				Id = topParentCostCode.Id,
				Code = topParentCostCode.Code,
				DescriptionInfo = topParentCostCode.DescriptionInfo,
				MdcCostCodeIds = currentCostCode.IsCustomProjectCostCode ? null : new List<int> { currentCostCode.Id },
				ProjectCostCodeIds = currentCostCode.IsCustomProjectCostCode ? new List<int> { currentCostCode.Id } : null
			};
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="costCodes"></param>
		/// <param name="currentCostCode"></param>
		/// <returns></returns>
		public CostCodeEntity FindTopParent(Dictionary<int, List<CostCodeEntity>> costCodes, CostCodeEntity currentCostCode)
		{
			if (currentCostCode.CostCodeParentFk == null)
			{
				return currentCostCode;
			}

			if (costCodes.ContainsKey(currentCostCode.CostCodeParentFk.Value))
			{
				var parentNode = costCodes[currentCostCode.CostCodeParentFk.Value].FirstOrDefault();
				return FindTopParent(costCodes, parentNode);
			}

			return null;
		}

		/// <summary>
		/// only get structrue tree
		/// </summary>
		/// <returns></returns>
		public IEnumerable<CostCodeEntity> GetMasterCostCodeAndProjectCostCodeChildOnly(int projectId)
		{
			var basicCostCodeLogic = new BasicsCostCodesLogic();
			var basicCostCodesDictionary = basicCostCodeLogic.GetTreeList(new CostCodeTreeFilterOption()
			{
				OnlyGetEstTypeCodeCode = false,
				FilterByCompany = true
			}).ToDictionary(e => e.Id, e => e);

			foreach (var costCode in basicCostCodesDictionary.Values)
			{
				costCode.CostCodeParent = null;
				costCode.CostCodeEntities_CostCodeLevel6Fk = null;
				costCode.CostCodeEntities_CostCodeLevel7Fk = null;
				costCode.CostCodeEntities_CostCodeLevel8Fk = null;

				costCode.CostCodeEntity_CostCodeLevel6Fk = null;
				costCode.CostCodeEntity_CostCodeLevel7Fk = null;
				costCode.CostCodeEntity_CostCodeLevel8Fk = null;
			}

			var projCoCodeLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();
			var projectCostCodeDictionary = projCoCodeLogic.GetAllCostCodesInProject(projectId).ToDictionary(e => e.Id, e => e);

			var waitForMergeCusPrjCostCodeIds = new List<int>();
			var restCusPrjCostCodeEntity = new List<CostCodeEntity>();
			foreach (var item in projectCostCodeDictionary.Values)
			{
				// custom project cost code
				if (!item.MdcCostCodeFk.HasValue)
				{
					if (!item.CostCodeParentFk.HasValue || !projectCostCodeDictionary.ContainsKey(item.CostCodeParentFk.Value))
					{
						continue;
					}

					var parentPrjCostCode = projectCostCodeDictionary[item.CostCodeParentFk.Value];
					if (parentPrjCostCode.MdcCostCodeFk.HasValue && basicCostCodesDictionary.ContainsKey(parentPrjCostCode.MdcCostCodeFk.Value))
					{
						var parent = basicCostCodesDictionary[parentPrjCostCode.MdcCostCodeFk.Value];
						parent.CostCodeChildren.Add(CreateCostCodeByPrjCostCode(parent.Id, item, null, true));
					}
					else
					{
						if (!basicCostCodesDictionary.ContainsKey(item.Id))
						{
							basicCostCodesDictionary.Add(item.Id, CreateCostCodeByPrjCostCode(parentPrjCostCode.Id, item, null, true));
						}
						else
						{
							restCusPrjCostCodeEntity.Add(CreateCostCodeByPrjCostCode(parentPrjCostCode.Id, item, null, true));
						}
						waitForMergeCusPrjCostCodeIds.Add(item.Id);
					}
				}
			}

			foreach (var cusPrjCostCodeId in waitForMergeCusPrjCostCodeIds)
			{
				if (!basicCostCodesDictionary.ContainsKey(cusPrjCostCodeId) || !restCusPrjCostCodeEntity.Any(e => e.Id == cusPrjCostCodeId))
				{
					continue;
				}
				var currentCostCode = basicCostCodesDictionary.ContainsKey(cusPrjCostCodeId) ? basicCostCodesDictionary[cusPrjCostCodeId] : restCusPrjCostCodeEntity.First(e => e.Id == cusPrjCostCodeId);
				if (!currentCostCode.CostCodeParentFk.HasValue || !basicCostCodesDictionary.ContainsKey(currentCostCode.CostCodeParentFk.Value) || !restCusPrjCostCodeEntity.Any(e => e.Id == currentCostCode.CostCodeParentFk.Value))
				{
					continue;
				}
				var parentCostCode = basicCostCodesDictionary.ContainsKey(currentCostCode.CostCodeParentFk.Value) ? basicCostCodesDictionary[currentCostCode.CostCodeParentFk.Value] : restCusPrjCostCodeEntity.First(e => e.Id == currentCostCode.CostCodeParentFk.Value);
				parentCostCode.CostCodeChildren.Add(currentCostCode);
			}

			return basicCostCodesDictionary.Values.Distinct().OrderBy(e => e.Code).ToList();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="parentId"></param>
		/// <param name="item"></param>
		/// <param name="jobRate"></param>
		/// <param name="isCustomProjectCostCode"></param>
		/// <returns></returns>
		private CostCodeEntity CreateCostCodeByPrjCostCode(int parentId, IProjectCostCodesEntity item, IProjectCostCodesJobRateEntity jobRate = null, bool isCustomProjectCostCode = false)
		{
			return new CostCodeEntity
			{
				CostCodeParentFk = parentId,
				Rate = jobRate != null ? jobRate.Rate : item.Rate,
				Id = item.Id,
				Code = item.Code,
				IsLabour = item.IsLabour,
				Remark = item.Remark,
				DayWorkRate = item.DayWorkRate,
				RealFactorQuantity = item.RealFactorQuantity,
				FactorQuantity = item.FactorQuantity,
				RealFactorCosts = item.RealFactorCosts,
				FactorCosts = item.FactorCosts,
				IsRate = item.IsRate,
				DescriptionInfo = new DescriptionTranslateType() { Description = item.DescriptionInfo.Description, Translated = item.DescriptionInfo.Translated },
				Description2Info = new DescriptionTranslateType() { Description = item.Description2, Translated = item.Description2 },
				UomFk = item.UomFk,
				CurrencyFk = item.CurrencyFk,
				CostCodeTypeFk = item.CostCodeTypeFk,
				PrcStructureFk = item.PrcStructureFk,
				//Flag to differentiate Manually Added Project Cost Code
				IsCustomProjectCostCode = isCustomProjectCostCode,
				//set version, if not, the column search will wrong.
				Version = item.Version
			};
		}
	} 
}
