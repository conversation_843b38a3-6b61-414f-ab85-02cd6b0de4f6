﻿using RIB.Visual.Basics.AccountingJournals.BusinessComponents;
using RIB.Visual.Basics.Company.BusinessComponents;
using RIB.Visual.Basics.Company.ServiceFacade.WebApi;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.AccountingJournals.ServiceFacade.WebApi
{
    /// <summary>
    /// AccountingJournalsCompleteDto
    /// </summary>
    public class AccountingJournalsCompleteDto
    {
        /// <summary>
        /// Standard Constructor
        /// </summary>
        public AccountingJournalsCompleteDto()
        {
        }

        /// <summary>
        /// Constructor from RegionCatalogCompleteDto entity
        /// </summary>
        /// <param name="entity">Contains information about the udated elements</param>
        public AccountingJournalsCompleteDto(AccountingJournalsCompleteEntity entity)
		{
            if (entity.CompanyTransheader != null)
            {
                AccountingJournalsDto AccountingJournals = new AccountingJournalsDto(entity.CompanyTransheader);
                this.AccountingJournals = AccountingJournals;
            }

            if (entity.TransactionToSave != null && entity.TransactionToSave.Any())
            {
                this.TransactionToSave = entity.TransactionToSave.Select(e => new CompanyTransactionDto(e)).ToArray();
            }
            if (entity.TransactionToDelete != null && entity.TransactionToDelete.Any())
            {
                this.TransactionToDelete = entity.TransactionToDelete.Select(e => new CompanyTransactionDto(e)).ToArray();
            }
		}

        /// <summary>
        /// The counter for the entities transported in the dto
        /// </summary>
        public int EntitiesCount { get; set; }

        /// <summary>
        /// The identifier for an CompanyTransactionHeader
        /// </summary>
        public int MainItemId { get; set; }

        /// <summary>
        /// AccountingJournalsDto (to be) saved
        /// </summary>
        public AccountingJournalsDto AccountingJournals { get; set; }

        /// <summary>
        /// The CompanyTransaction (to be) saved
        /// </summary>
        public IEnumerable<CompanyTransactionDto> TransactionToSave { get; set; }

        /// <summary>
        /// The CompanyTransaction (to be) deleted
        /// </summary>
        public IEnumerable<CompanyTransactionDto> TransactionToDelete { get; set; }

        /// <summary>
        /// Creates an AccountingJournalsCompleteEntity instance containing the entire actions to be executed within one entire transaction
        /// </summary>
        /// <returns></returns>
        public AccountingJournalsCompleteEntity Copy()
        {
            AccountingJournalsCompleteEntity accountingJournalsComplete = new AccountingJournalsCompleteEntity();

            if (AccountingJournals != null)
            {
                CompanyTransheaderEntity CompanyTransheader = new CompanyTransheaderEntity();
                CompanyTransheader.Id = AccountingJournals.Id;
                var CompanyID = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
                CompanyTransheader.CompanyFk = CompanyID;
                CompanyTransheader.CompanyPeriodFk = AccountingJournals.CompanyPeriodFk;
                CompanyTransheader.TransactionTypeFk = AccountingJournals.TransactionTypeFk;
                CompanyTransheader.Description = AccountingJournals.Description;
                CompanyTransheader.PostingDate = AccountingJournals.PostingDate;
                CompanyTransheader.CommentText = AccountingJournals.CommentText;
                CompanyTransheader.ReturnValue = AccountingJournals.ReturnValue;
                CompanyTransheader.IsSuccess = AccountingJournals.IsSuccess;
                CompanyTransheader.InsertedAt = AccountingJournals.InsertedAt;
                CompanyTransheader.InsertedBy = AccountingJournals.InsertedBy;
                CompanyTransheader.UpdatedAt = AccountingJournals.UpdatedAt;
                CompanyTransheader.UpdatedBy = AccountingJournals.UpdatedBy;
                CompanyTransheader.Version = AccountingJournals.Version;
				CompanyTransheader.CompanyTransheaderFk = AccountingJournals.BasCompanyTransheaderFk;
                CompanyTransheader.CompanyTransheaderStatusFk = AccountingJournals.CompanyTransheaderStatusFk;
                accountingJournalsComplete.CompanyTransheader = CompanyTransheader;
            }

            if (this.TransactionToSave != null && this.TransactionToSave.Any())
            {
                accountingJournalsComplete.TransactionToSave = this.TransactionToSave.Select(e => e.Copy()).ToList();
            }
            if (this.TransactionToDelete != null && this.TransactionToDelete.Any())
            {
                accountingJournalsComplete.TransactionToDelete = this.TransactionToDelete.Select(e => e.Copy()).ToList();
            }

            return accountingJournalsComplete;
        }
    }
}
