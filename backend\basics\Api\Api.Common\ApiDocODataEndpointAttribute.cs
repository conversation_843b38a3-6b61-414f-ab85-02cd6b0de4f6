﻿/*
 * $Id: ApiDocODataEndpointAttribute.cs 583836 2020-04-21 10:35:10Z haagf $
 * Copyright (c) RIB Software SE
 */

using System;
using RIB.Visual.Basics.Core.Core;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Indicates that the endpoint processes input that conforms to the OData specification and should be documented accordingly.
	/// </summary>
	[AttributeUsage(AttributeTargets.Method)]
	public class ApiDocODataEndpointAttribute : ApiDocAttribute
	{
		/// <summary>
		/// Initializes a new instance.
		/// </summary>
		/// <param name="requestMode">The request mode of the endpoint.</param>
		public ApiDocODataEndpointAttribute(ODataRequestMode requestMode = ODataRequestMode.RetrieveItems)
		{
			_requestMode = requestMode;
		}

		private readonly ODataRequestMode _requestMode;

		/// <summary>
		/// The request mode.
		/// </summary>
		public ODataRequestMode RequestMode
		{
			get { return _requestMode; }
		}
	}
}
