using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample;

/// <summary>
/// Read System Info and use the LoginServices method for setting
/// up the request header and checking the response values.
///  
/// </summary>
public static class SystemInfo
{
	public static LogonServices LogonServices { get; set; } 

	#region Get SystemInfo after Login

	/// <summary>
	/// Get SystemInfo via service call asynchronous
	/// </summary>
	/// <returns></returns>
	public static async Task<Response> GetSystemInfoAsync()
	{
		const string name = "GetSystemInfoAsync";
		Console.WriteLine("{0} - Start", name);
		var responseResult = await Task.Run<Response>(() => GetSystemInfo());
		Console.WriteLine("{0} - Done", name);
		return responseResult;
	}

	/// <summary>
	/// Get SystemInfo via service call
	/// </summary>
	/// <returns></returns>
	private static Response GetSystemInfo()
	{
		var fctResponse = new Response() {Result = false};

		using (var client = new HttpClient())
		{
			LogonServices.SetTokenClientContext(client);
			try
			{
				// send Get Call to Backend
				var url = LogonServices.ServicesUrl + Constant.SystemInfo;
				var response = client.GetAsync(url).Result;

				if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.ResetContent)
					return LogonServices.AnalyseHttpStatusCode(response);

				return LogonServices.ReadResponseValue(response, fctResponse);
			}
			catch (Exception ex)
			{
				fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
				return fctResponse;
			}
		}
	}

	#endregion
}