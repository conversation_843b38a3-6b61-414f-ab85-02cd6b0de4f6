﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Provides meta info from the webapi method to help webapihelp to generate docs automatically.
	/// </summary>
	[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
	public class ApiDocParentFieldAttribute : Attribute
	{
		/// <summary>
		/// Constructor.
		/// </summary>
		public ApiDocParentFieldAttribute(string parentProperty = "", int order = 1)
		{
			this.ParentProperty = parentProperty;
			this.Order = order;
		}

		/// <summary>
		/// Specify the LookupType
		/// </summary>
		public string ParentProperty { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int Order { get; set; }

	}
}
