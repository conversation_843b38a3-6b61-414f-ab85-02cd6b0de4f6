﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{C611B718-AD15-42CC-A49B-597BFC67A902}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>RIB.Visual.Basics.Api.UnitTests</RootNamespace>
    <AssemblyName>RIB.Visual.Basics.Api.UnitTests</AssemblyName>
    <WarningLevel>4</WarningLevel>
    <ProjectTypeGuids>{3AC096D0-A1C2-E12C-1390-A8335801FDAB};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFramework>net8.0</TargetFramework>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>.\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <RunCodeAnalysis>false</RunCodeAnalysis>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>false</DebugSymbols>
    <Optimize>true</Optimize>
    <OutputPath>.\bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Platform.AppServer.Runtime">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Runtime.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessComponents">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessComponents.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessEnvironment">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessEnvironment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Core">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.OperationalManagement">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.OperationalManagement.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Localization">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Localization.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.AppServer.Web">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Web.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.ServiceFacade.WebApi.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.BusinessComponents.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.Localization">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.Localization.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.ServiceFacade.WebApi.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Caching">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Runtime.Caching.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Configuration.ConfigurationManager.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="FluentAssertions">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\FluentAssertions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.SqlServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ComponentModel.Composition.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.CodeDom">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.CodeDom.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.UnitTests.Common">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.UnitTests.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{B4F97281-0DBD-4835-9ED8-7DFB966E87FF}" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Platform.AppServer.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Platform.Database.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="RIB.Visual.Basics.Api.UnitTests.dll.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </None>
    <None Include="RIBvisual.snk" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Api.BusinessComponents\RIB.Visual.Basics.Api.BusinessComponents.csproj">
      <Project>{71D3A17F-0F5C-4E70-A37D-BCF5DDCB101B}</Project>
      <Name>RIB.Visual.Basics.Api.BusinessComponents</Name>
      <Private>True</Private>
    </ProjectReference>
    <ProjectReference Include="..\Api.Common\RIB.Visual.Basics.Api.Common.csproj">
      <Project>{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}</Project>
      <Name>RIB.Visual.Basics.Api.Common</Name>
      <Private>True</Private>
    </ProjectReference>
    <ProjectReference Include="..\Api.Core\RIB.Visual.Basics.Api.Core.csproj">
      <Project>{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}</Project>
      <Name>RIB.Visual.Basics.Api.Core</Name>
      <Private>True</Private>
    </ProjectReference>
    <ProjectReference Include="..\Api.ServiceFacade.WebApi\RIB.Visual.Basics.Api.ServiceFacade.WebApi.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BasicsApiSchemaControllerTest.cs" />
    <Compile Include="TestCommon.cs" />
    <Compile Include="InquiryApiTest.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="System.Runtime.Caching" />
    <PackageReference Include="System.Data.SqlClient" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="$(RIBvisualBinPool)\xunit.runner.json">
      <Link>xunit.runner.json</Link>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F" />
  </Target>
</Project>