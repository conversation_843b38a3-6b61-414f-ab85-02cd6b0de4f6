using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.InteropServices;
using System.Security;

namespace RIB.Visual.Basics.Api.Client.WpfApp
{
	/// <summary>
	/// 
	/// </summary>
	public class Model : INotifyPropertyChanged
	{
		private string _baseUrl;
		private string _clientUrl;
		private string _jsonOutput;
		private string _identityServerUrl;
		private string _operation;
		private Dictionary<string, string> _operation2UrlMap = new Dictionary<string, string>();
		private bool _isSelection;
		public Model()
		{
			Password = new SecureString();
			SearchPattern = "Felde,";
			BaseUrls = new string[] { Constant.BaseUrl1, Constant.BaseUrl2, Constant.BaseUrl3, Constant.BaseUrl4, Constant.BaseUrl5, Constant.BaseUrl6, Constant.BaseUrl7, Constant.BaseUrl8, Constant.BaseUrl9 };
			IdentityServerUrls = new string[]
			{
				Constant.IdentityServerUrl1, Constant.IdentityServerUrl2, Constant.IdentityServerUrl3,
				Constant.IdentityServerUrl4, Constant.IdentityServerUrl5, Constant.IdentityServerUrl6,
				Constant.IdentityServerUrl7,Constant.IdentityServerUrl8,Constant.IdentityServerUrl9
			};
			Operations = new string[]
			{
				Constant.OperationInquiry,
				Constant.OperationLookup,
				Constant.OperationInquiryForBusinesspartner,
				Constant.OperationInquiryForInvoice,
				Constant.OperationInquiryForPackage,
				Constant.OperationInquiryForContract,
				Constant.OperationInquiryForEstimate,
				Constant.OperationLookupForBusinesspartner,
				Constant.OperationLookupForInvoice,
				Constant.OperationLookupForPackage,
				Constant.OperationLookupForContract,
				Constant.OperationLookupMaterial,
				Constant.OperationLookupInvoiceDocumentsMain,
				Constant.OperationLookupBillingDocumentsMain,
				Constant.OperationLookupForBpFromBaseline
			};
			_operation2UrlMap.Add(Constant.OperationInquiry, Constant.OperationRelUrlInquiry);
			_operation2UrlMap.Add(Constant.OperationLookup, Constant.OperationRelUrlLookup);
			_operation2UrlMap.Add(Constant.OperationInquiryForBusinesspartner, Constant.OperationRelUrlInquiry);
			_operation2UrlMap.Add(Constant.OperationInquiryForInvoice, Constant.OperationRelUrlInquiry);
			_operation2UrlMap.Add(Constant.OperationInquiryForPackage, Constant.OperationRelUrlInquiry);
			_operation2UrlMap.Add(Constant.OperationInquiryForContract, Constant.OperationRelUrlInquiry);
			_operation2UrlMap.Add(Constant.OperationInquiryForEstimate, Constant.OperationRelUrlInquiry);

			_operation2UrlMap.Add(Constant.OperationLookupForBusinesspartner, Constant.OperationRelUrlInquiry);
			_operation2UrlMap.Add(Constant.OperationLookupForInvoice, Constant.OperationRelUrlLookup);
			_operation2UrlMap.Add(Constant.OperationLookupForPackage, Constant.OperationRelUrlInquiry);
			_operation2UrlMap.Add(Constant.OperationLookupForContract, Constant.OperationRelUrlInquiry);

			_operation2UrlMap.Add(Constant.OperationLookupMaterial, Constant.OperationRelUrlMaterial);
			_operation2UrlMap.Add(Constant.OperationLookupInvoiceDocumentsMain, Constant.OperationRelUrlDocumentProject);
			_operation2UrlMap.Add(Constant.OperationLookupBillingDocumentsMain, Constant.OperationRelUrlDocumentProject);
			_operation2UrlMap.Add(Constant.OperationLookupForBpFromBaseline, Constant.OperationRelUrlInquiry);
		}

		/// <summary>
		/// RequestId
		/// </summary>
		public Guid RequestId { get; set; }

		/// <summary>
		/// RequestIdAsString
		/// </summary>
		public string RequestIdAsString { get { return RequestId.ToString("N"); } }

		/// <summary>
		/// Username
		/// </summary>
		public string Username { get; set; }

		/// <summary>
		/// Password
		/// </summary>
		public SecureString Password { get; set; }


		public string[] BaseUrls { get; set; }

		public string[] IdentityServerUrls { get; set; }

		public string[] Operations { get; set; }



		/// <summary>
		/// BaseUrl
		/// </summary>
		public string BaseUrl
		{
			get { return _baseUrl; }
			set
			{
				if (_baseUrl != value)
				{
					_baseUrl = value.EndsWith(@"/") ? value : value + @"/";
					ServicesUrl = _baseUrl + "services";
					
					if (!string.IsNullOrWhiteSpace(_clientUrl))
					{
						var index = _clientUrl.IndexOf("#/api?navigate");
						if (index > 0)
						{
							var baseurl = _clientUrl.Substring(0, index);
							ClientUrl = _clientUrl.Replace(baseurl, _baseUrl+"client");
						}	
					}
					else
					{
						ClientUrl = _baseUrl + "client";
					}
					OnPropertyChanged("BaseUrl");
					OnPropertyChanged("ClientUrl");
					OnPropertyChanged("ServicesUrl");
				}
			}
		}


		/// <summary>
		/// ClientUrl
		/// </summary>
		public string ClientUrl
		{
			get { return _clientUrl; }
			set { _clientUrl = value; OnPropertyChanged("ClientUrl"); }
		}

		/// <summary>
		/// ServicesUrl
		/// </summary>
		public string ServicesUrl { get; set; }

		/// <summary>
		/// IdentityServerUrl
		/// </summary>
		public string IdentityServerUrl
		{
			get { return _identityServerUrl; }
			set { _identityServerUrl = value; OnPropertyChanged("IdentityServerUrl"); }
		}

		/// <summary>
		/// Operation
		/// </summary>
		public string Operation
		{
			get { return _operation; }
			set
			{
				_operation = value;
				OperationRelUrl = _operation2UrlMap[_operation];

				OnPropertyChanged("Operation");
			}
		}

		/// <summary>
		/// OperationRelUrl
		/// </summary>
		public string OperationRelUrl { get; set; }

		/// <summary>
		/// CompanyCode
		/// </summary>
		public string CompanyCode { get; set; }

		/// <summary>
		/// SearchPattern
		/// </summary>
		public string SearchPattern { get; set; }

		/// <summary>
		/// JsonOutput
		/// </summary>
		public string JsonOutput
		{ 
			get { return _jsonOutput; }
			set { _jsonOutput = value; OnPropertyChanged("JsonOutput"); }
		}

		/// <summary>
		/// IsSelection
		/// </summary>
		public bool IsSelection
		{
			get { return _isSelection; }
			set { _isSelection = value; OnPropertyChanged("IsSelection"); }
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strPassword"></param>
		public void SetToSecurePassword(string strPassword)
		{
			Password = new SecureString();
			if (strPassword.Length > 0)
			{
				foreach (var c in strPassword.ToCharArray()) Password.AppendChar(c);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public string GetUnsecurePassword()
		{
			IntPtr unmanagedString = IntPtr.Zero;
			try
			{
				unmanagedString = Marshal.SecureStringToGlobalAllocUnicode(Password);
				return Marshal.PtrToStringUni(unmanagedString);
			}
			finally
			{
				Marshal.ZeroFreeGlobalAllocUnicode(unmanagedString);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		public event PropertyChangedEventHandler PropertyChanged;

		/// <summary>
		/// 
		/// </summary>
		/// <param name="propertyName"></param>
		protected virtual void OnPropertyChanged(string propertyName)
		{
			if (this.PropertyChanged != null)
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
			}
		}


		/// <summary>
		/// 
		/// </summary>
		public void NotifyChanged()
		{
			OnPropertyChanged("SearchPattern");
			OnPropertyChanged("CompanyCode");
			OnPropertyChanged("Username");
			// OnPropertyChanged("IdentityServerUrl");
			OnPropertyChanged("BaseUrl");
		}
	}
}