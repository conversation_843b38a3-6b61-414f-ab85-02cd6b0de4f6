﻿using System;

namespace RIB.Visual.Basics.Api.Core
{
	/// <summary>
	/// 
	/// </summary>
	/// <typeparam name="TInt"></typeparam>
	public interface IInquiryBase<TInt>
	{
		/// <summary>
		/// unique identifier
		/// </summary>
		TInt Id { get; set; }

		/// <summary>
		/// Name of the inquiry item
		/// </summary>
		String Name { get; set; }

		/// <summary>
		/// more info description of the inquiry item
		/// </summary>
		String Description { get; set; }
	}
}