﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{30EBFD89-24AF-4ABA-97A5-234FC0BF1C9D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RIB.Visual.Basics.Api.Common</RootNamespace>
    <AssemblyName>RIB.Visual.Basics.Api.Common</AssemblyName>
    <TargetFramework>net8.0</TargetFramework>
    <FileAlignment>512</FileAlignment>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation />
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\RIB.Visual.Basics.Api.Common.XML</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <DocumentationFile>bin\Release\RIB.Visual.Basics.Api.Common.XML</DocumentationFile>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="ApiDocAttribute.cs" />
    <Compile Include="ApiDocDtoPropertyIgnoreAttribute.cs" />
    <Compile Include="ApiDocEnableVariablesAttribute.cs" />
    <Compile Include="ApiDocExcludingPropertiesAttributeBase.cs" />
    <Compile Include="ApiDocRequestExcludingPropertiesAttribute.cs" />
    <Compile Include="ApiDocResponseExcludingPropertiesAttribute.cs" />
    <Compile Include="ApiDocIdentifyingQueryStringAttribute.cs" />
    <Compile Include="ApiDocIdRouteAttribute.cs" />
    <Compile Include="ApiDocIgnoreAttribute.cs" />
    <Compile Include="ApiDocODataEndpointAttribute.cs" />
    <Compile Include="ApiDocParentFieldAttribute.cs" />
    <Compile Include="ApiDocMethodRequestAttribute.cs" />
    <Compile Include="ApiDocLookupFieldAttribute.cs" />
    <Compile Include="ApiDocMethodResponseAttribute.cs" />
    <Compile Include="ApiDocMultipartFormDataAttribute.cs" />
    <Compile Include="ApiDocVariableAttribute.cs" />
    <Compile Include="ApiDocVariableAttributeBase.cs" />
    <Compile Include="ApiDocVariableFromStaticMethodAttribute.cs" />
    <Compile Include="CheckIdOrCodeAttribute.cs" />
    <Compile Include="AnyOfIdOrCodeAttribute.cs" />
    <Compile Include="CheckOnlyOneRootAttribute.cs" />
    <Compile Include="CompositeOnlyOneRootKeyAttribute.cs" />
    <Compile Include="CultureAttribute.cs" />
    <Compile Include="IDtoDescriptorAttribute.cs" />
    <Compile Include="ImportContentFileLogger.cs" />
    <Compile Include="IImportContentLogger.cs" />
    <Compile Include="MobilityApiResult.cs" />
    <Compile Include="ImportContentLogger.cs" />
    <Compile Include="SsoInfo.cs" />
    <Compile Include="CompositeUniqueKeyAttribute.cs" />
    <Compile Include="InquiryBase.cs" />
    <Compile Include="InquiryFactory.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="PublicApiValidationLogger.cs" />
    <Compile Include="CheckUniqueAttribute.cs" />
    <Compile Include="StartDateCompareAttribute.cs" />
    <Compile Include="TruncatableObjectAttribute.cs" />
    <Compile Include="TranslationPropertyLengthAttribute.cs" />
    <Compile Include="TruncateAttribute.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="UniqueHintAttribute.cs" />
    <Compile Include="VariableExpansionContext.cs" />
    <Compile Include="../../../AssemblyVersion.cs" Link="Properties/AssemblyVersion.cs" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Platform.OperationalManagement">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.OperationalManagement.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Server.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Server.Common.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <None Include="RIBvisual.snk" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Api.Core\RIB.Visual.Basics.Api.Core.csproj">
      <Project>{6F01EEE3-3222-4AC8-AC5C-F1317B9C53FF}</Project>
      <Name>RIB.Visual.Basics.Api.Core</Name>
      <Private>False</Private>
    </ProjectReference>
  </ItemGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F&#xD;&#xA;" />
  </Target>
</Project>