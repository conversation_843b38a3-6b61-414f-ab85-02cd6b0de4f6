using System.Collections;
using System.Collections.Generic;
using RIB.Visual.Platform.Common;

namespace RIB.Visual.Awp.Main.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public class ServicePackageDto
	{
		/// <summary>
		/// 
		/// </summary>
		public int Id { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int MainItemId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string Reference { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public DescriptionTranslateTypeDto BriefInfo { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int TypeFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? ParentFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<ServicePackageDto> Children { get; set; }

	}
}
