using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using RIB.Visual.Awp.Main.BusinessComponents;
using RIB.Visual.Basics.Common.ServiceFacade.WebApi;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Core.ServiceFacade.WebApi;
using RIB.Visual.Platform.Core;

namespace RIB.Visual.Awp.Main.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public class AwpMainCompleteDto : BaseCompleteDto
	{
		/// <summary>
		/// 
		/// </summary>
		public AwpMainCompleteDto()
		{
			
		}

		#region item assignment
		/// <summary>
		/// 
		/// </summary>
		[JsonConverter(typeof(JSON2DtoByDispatchConverter), "Procurement.Common.PrcItemAssignmentDtoConverter")]
		[DtoEntityMapping(typeof(IDtoEntityMapper), "Procurement.Common.PrcItemAssignmentDtoEntityMapper", DtoEntityMappingAttribute.MapFilter.Allways)]
		public IEnumerable<IIdentifyable> AwpPackageItemAssignmentToSave
		{
			get; set;
		}
		#endregion

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<AwpPackageBoqItemCompleteDto> ServicePackagesToSave { get; set; }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entity"></param>
		public AwpMainCompleteDto(AwpMainCompleteEntity entity)
		{
			EntitiesCount = entity.EntitiesCount;
			MainItemId = entity.MainItemId;

			if (entity.AwpPackageItemAssignmentToSave != null && entity.AwpPackageItemAssignmentToSave.Any())
			{
				var factory = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IPrcItemAssignmentTypeFactory>("PrcItemAssignmentTypeFactory");
				this.AwpPackageItemAssignmentToSave = entity.AwpPackageItemAssignmentToSave.Select(e => factory.CreateFrom(e)).ToList();
			}

			if(entity.ServicePackagesToSave != null && entity.ServicePackagesToSave.Any())
			{
				this.ServicePackagesToSave = entity.ServicePackagesToSave?.Select(e => new AwpPackageBoqItemCompleteDto(e)).ToList();
			}

			this.CopyApplyChangeFrom(entity);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public AwpMainCompleteEntity copy()
		{
			var entity = new AwpMainCompleteEntity()
			{
				EntitiesCount = EntitiesCount,
				MainItemId = MainItemId
			};

			if (this.AwpPackageItemAssignmentToSave != null && this.AwpPackageItemAssignmentToSave.Any())
			{
				var factory = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IPrcItemAssignmentTypeFactory>("PrcItemAssignmentTypeFactory");
				entity.AwpPackageItemAssignmentToSave = this.AwpPackageItemAssignmentToSave.Select(e => factory.CopyTo(e)).ToList();
			}

            if (this.ServicePackagesToSave != null && this.ServicePackagesToSave.Any())
            {
                entity.ServicePackagesToSave = this.ServicePackagesToSave.Select(e => e.Copy());
            }

            this.CopyApplyChangeTo(entity);
			return entity;
		}
	}
}
