//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON>art Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Data.Common;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.ModelConfiguration;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Linq;
using System.Runtime.Serialization;
using System.Xml.Serialization;
using RIB.Visual.Platform.Common;
using System.Data.Entity.Core.EntityClient;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Core.Objects.DataClasses;
using RIB.Visual.Basics.AuditTrail.BusinessComponents;

namespace RIB.Visual.Basics.AuditTrail.BusinessComponents
{
    /// <summary/>
    public partial class ModelBuilder
    {
		#region Constructors
		/// <summary>
		/// Initialize a new ModelBuilder object.
		/// </summary>
		public ModelBuilder()
    {
		}
		#endregion

		static private readonly Object _locking = new Object();
		static private DbCompiledModel _model;

		/// <summary>
		/// Creates a compiled entity model
		/// </summary>
		static public DbCompiledModel DbModel
		{
			get
			{
				if (_model == null)
				{
					lock (_locking)
					{
						if (_model == null)
						{
							var modelBuilder = new DbModelBuilder();

							AddMappings(modelBuilder);
							AddAdditionalMappings(modelBuilder);

							modelBuilder.Conventions.Remove<StoreGeneratedIdentityKeyConvention>();

							_model = modelBuilder.Build(Platform.BusinessComponents.DbContext.CreateConnection()).Compile();
						}
					}
				}

				return _model;
			}
		}

		// partial method to add special/additional mappings
		static partial void AddAdditionalMappings(DbModelBuilder modelBuilder);

		/// <summary>
		/// Adds the mapping for each entity of this db context.
		/// </summary>
		/// <param name="modelBuilder"></param>
		static public void AddMappings(DbModelBuilder modelBuilder)
		{

            #region DdTempIdsEntity

            modelBuilder.Entity<DdTempIdsEntity>()
                .HasKey(p => new { p.Id, p.RequestId })
                .ToTable("BAS_DDTEMPIDS");
            // Properties:
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.RequestId)
                    .HasColumnName(@"REQUESTID")
                    .IsRequired()
                    .HasMaxLength(32)
                    .HasColumnType("char");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key1)
                    .HasColumnName(@"KEY1")
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key2)
                    .HasColumnName(@"KEY2")
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key3)
                    .HasColumnName(@"KEY3")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<DdTempIdsEntity>(modelBuilder);

            #endregion

            #region AudColumnEntity

            modelBuilder.Entity<AudColumnEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("AUD_COLUMN");
            // Properties:
            modelBuilder.Entity<AudColumnEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<AudColumnEntity>()
                .Property(p => p.AudTableFk)
                    .HasColumnName(@"AUD_TABLE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<AudColumnEntity>()
                .Property(p => p.Columnname)
                    .HasColumnName(@"COLUMNNAME")
                    .IsRequired()
                    .HasMaxLength(32)
                    .HasColumnType("varchar");
            modelBuilder.Entity<AudColumnEntity>()
                .Property(p => p.Isenabletracking)
                    .HasColumnName(@"ISENABLETRACKING")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<AudColumnEntity>()
                .Property(p => p.Isdeleted)
                    .HasColumnName(@"ISDELETED")
                    .IsRequired()
                    .HasColumnType("bit");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<AudColumnEntity>(modelBuilder);

            #endregion

            #region AudContainerEntity

            modelBuilder.Entity<AudContainerEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("AUD_CONTAINER");
            // Properties:
            modelBuilder.Entity<AudContainerEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<AudContainerEntity>()
                .Property(p => p.ContainerUuid)
                    .HasColumnName(@"CONTAINER_UUID")
                    .IsRequired()
                    .HasMaxLength(32)
                    .HasColumnType("char");
            modelBuilder.Entity<AudContainerEntity>()
                .Property(p => p.DescriptionInfo.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<AudContainerEntity>()
                .Property(p => p.DescriptionInfo.DescriptionTr)
                    .HasColumnName(@"DESCRIPTION_TR")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<AudContainerEntity>(modelBuilder);
            // Association:
            modelBuilder.Entity<AudContainerEntity>()
                .HasMany(p => p.AudContainer2AudTableEntities)
                    .WithRequired(c => c.AudContainerEntity)
                .HasForeignKey(p => new { p.AudCointainerFk })
                    .WillCascadeOnDelete(false);

            #endregion

            #region AudContainer2AudTableEntity

            modelBuilder.Entity<AudContainer2AudTableEntity>()
                .HasKey(p => new { p.AudCointainerFk, p.AudTableFk })
                .ToTable("AUD_CONTAINER2AUD_TABLE");
            // Properties:
            modelBuilder.Entity<AudContainer2AudTableEntity>()
                .Property(p => p.AudCointainerFk)
                    .HasColumnName(@"AUD_COINTAINER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<AudContainer2AudTableEntity>()
                .Property(p => p.AudTableFk)
                    .HasColumnName(@"AUD_TABLE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<AudContainer2AudTableEntity>(modelBuilder);

            #endregion

            #region AudTableEntity

            modelBuilder.Entity<AudTableEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("AUD_TABLE");
            // Properties:
            modelBuilder.Entity<AudTableEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<AudTableEntity>()
                .Property(p => p.Tablename)
                    .HasColumnName(@"TABLENAME")
                    .IsRequired()
                    .HasMaxLength(30)
                    .HasColumnType("varchar");
            modelBuilder.Entity<AudTableEntity>()
                .Property(p => p.Logtablename)
                    .HasColumnName(@"LOGTABLENAME")
                    .IsRequired()
                    .HasMaxLength(30)
                    .HasColumnType("varchar");
            modelBuilder.Entity<AudTableEntity>()
                .Property(p => p.DescriptionTr)
                    .HasColumnName(@"DESCRIPTION_TR")
                    .HasColumnType("int");
            modelBuilder.Entity<AudTableEntity>()
                .Property(p => p.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<AudTableEntity>()
                .Property(p => p.Isenabletracking)
                    .HasColumnName(@"ISENABLETRACKING")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<AudTableEntity>()
                .Property(p => p.Archivedays)
                    .HasColumnName(@"ARCHIVEDAYS")
                    .IsRequired()
                    .HasColumnType("smallint");
            modelBuilder.Entity<AudTableEntity>()
                .Property(p => p.Purgedays)
                    .HasColumnName(@"PURGEDAYS")
                    .IsRequired()
                    .HasColumnType("smallint");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<AudTableEntity>(modelBuilder);
            // Associations:
            modelBuilder.Entity<AudTableEntity>()
                .HasMany(p => p.AudColumnEntities)
                    .WithRequired(c => c.AudTableEntity)
                .HasForeignKey(p => new { p.AudTableFk })
                    .WillCascadeOnDelete(false);
            modelBuilder.Entity<AudTableEntity>()
                .HasMany(p => p.AudContainer2AudTableEntities)
                    .WithRequired(c => c.AudTableEntity)
                .HasForeignKey(p => new { p.AudTableFk })
                    .WillCascadeOnDelete(false);

            #endregion

            #region ComplexTypes

            modelBuilder.ComplexType<DescriptionTranslateType>();

            #endregion

            #region Disabled conventions


            #endregion

        }

    
        /// <summary>
        /// There are no comments for DdTempIdsEntity in the schema.
        /// </summary>
        public DbSet<DdTempIdsEntity> DdTempIdsEntities { get; set; }
    
        /// <summary>
        /// There are no comments for AudColumnEntity in the schema.
        /// </summary>
        public DbSet<AudColumnEntity> AudColumnEntities { get; set; }
    
        /// <summary>
        /// There are no comments for AudContainerEntity in the schema.
        /// </summary>
        public DbSet<AudContainerEntity> AudContainerEntities { get; set; }
    
        /// <summary>
        /// There are no comments for AudContainer2AudTableEntity in the schema.
        /// </summary>
        public DbSet<AudContainer2AudTableEntity> AudContainer2AudTableEntities { get; set; }
    
        /// <summary>
        /// There are no comments for AudTableEntity in the schema.
        /// </summary>
        public DbSet<AudTableEntity> AudTableEntities { get; set; }
    }
}
