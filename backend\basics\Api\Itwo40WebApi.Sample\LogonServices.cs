using System;
using System.Net;
using System.Net.Http;
using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;
using System.Net.Http.Headers;

namespace RIB.Visual.Basics.Api.Client.Itwo40WebApi.Sample
{
	/// <summary>
	/// This class is a helper class for logging into the system and
	/// calling services like: GetCompanies, checkcompany, systeminfo and a sample
	/// reading a business partner...
	/// 
	/// </summary>
	public class LogonServices
	{
		/// <summary>the property hold the jwt access token</summary>
		public string Token { get; set; }

		/// <summary>This property hold the services Url </summary>
		public string ServicesUrl { get; set; }

		/// <summary> 
		/// After successfully logged in and validating the context we save the secureContext into this member.
		/// this context is used for further api call
		/// </summary>
		public ServiceClientContext ServiceClientContext { get; set; }

		/// <summary>
		/// This method is used for analyzing the response from web-service calls
		/// 
		/// </summary>
		/// <param name="response"></param>
		/// <returns></returns>
		public Response AnalyseHttpStatusCode(HttpResponseMessage response)
		{
			var fctRespnse = new Response() { Result = false };
			if (response.StatusCode == HttpStatusCode.BadRequest)
			{
				var result = response.Content.ReadAsStringAsync().Result;
				//Console.WriteLine(JArray.Parse(result));
				try
				{
					var jObject = JObject.Parse(result);
					var jResult = JsonConvert.SerializeObject(jObject, Formatting.Indented);
					fctRespnse.ResponseValue = response + "\n" + jResult;
				}
				catch (Exception)
				{
					fctRespnse.ResponseValue = result;
				}
				return fctRespnse;
			}
			fctRespnse.ResponseValue = response.ToString();
			return fctRespnse;
		}

		/// <summary>
		/// This method added the accesstoken via "Bearer" and the Clientcontext via "Client-Context"
		/// into the service request header.
		/// 
		/// </summary>
		/// <param name="client"></param>
		public void SetTokenClientContext(HttpClient client)
		{
			//client.SetBearerToken(Token); // set token from identity server into web-api header
			client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", Token);
			var clientContext = (ServiceClientContext ?? new ServiceClientContext()).ToString(Formatting.None);
			client.DefaultRequestHeaders.Add("Client-Context", clientContext);

		}

		/// <summary>
		/// This method reads the response from the service call and translates it into an JObject
		/// 
		/// </summary>
		/// <param name="response"></param>
		/// <param name="fctResponse"></param>
		/// <returns></returns>
		public Response ReadResponseValue(HttpResponseMessage response, Response fctResponse)
		{
			var theResponse = response.Content.ReadAsStringAsync().Result;
			fctResponse.ResponseValue = theResponse;
			if (!string.IsNullOrWhiteSpace(theResponse))
			{
				fctResponse.ObjValue = JsonConvert.DeserializeObject(theResponse) as JObject;
				if (fctResponse.ObjValue == null)
				{
					fctResponse.ArrValue = JsonConvert.DeserializeObject(theResponse) as JArray;
				}
			}
			fctResponse.Result = true;
			return fctResponse;
		}

		/// <summary>
		/// Just a helper class show the claims in the access token
		/// </summary>
		/// <returns></returns>
		public string GetTokenClaimsInfo()
		{
			var str = new StringBuilder();
			//			var x = new System.IdentityModel.Tokens.JwtSecurityToken(Token);
			var x = new System.IdentityModel.Tokens.Jwt.JwtSecurityToken(Token);

			if (x.Payload != null)
			{
				foreach (var claim in x.Payload)
				{
					str.AppendLine(string.Format("Claim: {0}\t{1}", claim.Key, claim.Value));
				}
			}
			str.AppendLine(string.Format("ValidFrom: \t{0}", x.ValidFrom));
			str.AppendLine(string.Format("ValidTo:   \t{0}", x.ValidTo));
			return str.ToString();
		}

		#region CheckCompanyToAssignedCompaniesAsync

		/// <summary>
		/// This method is used for select a company context via the company Code, 
		/// this is the easiest way to get an valid the company context
		/// </summary>
		/// <param name="requestedSignedInCompanyCode"></param>
		/// <param name="requestedRoleId"></param>
		/// <returns></returns>
		public async Task<Response> CheckCompanyviaCompanyCodeAsync(string requestedSignedInCompanyCode, int? requestedRoleId)
		{

			Console.WriteLine("CheckCompanyviaCompanyCodeAsync - Start");
			// ReSharper disable once ConvertClosureToMethodGroup
			var t = new Task<Response>(() => CheckCompanyviaCompanyCode(requestedSignedInCompanyCode, requestedRoleId));
			t.Start();
			var responseResult = await t;
			Console.WriteLine("CheckCompanyviaCompanyCodeAsync - Done");
			return responseResult;
		}


		private Response CheckCompanyviaCompanyCode(string requestedSignedInCompanyCode, int? requestedRoleId)
		{
			var fctResponse = new Response() { Result = false };
			using (var client = new HttpClient())
			{
				SetTokenClientContext(client);

				try
				{
					// send Get Call to Backend
					var url = ServicesUrl + Constant.CheckCompaniesbyCode +
										string.Format("?requestedSignedInCompanyCode={0}&requestedRoleId={1}",
											requestedSignedInCompanyCode, requestedRoleId);

					var response = client.GetAsync(url).Result;

					if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.ResetContent)
						return AnalyseHttpStatusCode(response);

					return ReadResponseValue(response, fctResponse);

				}
				catch (Exception ex)
				{
					fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
					return fctResponse;
				}
			}
		}


		#endregion

		#region CheckCompanyToAssignedCompaniesAsync

		/// <summary>
		/// This method is used for select a company context via the parameters:
		///		requestedSignedInCompanyId, requestedCompanyId, requestedPermissionClientId, requestedRoleId
		/// 
		/// where 
		/// requestedSignedInCompanyId is the signing in company or profite center
		/// requestedCompanyId is the legally independent entity, in case of a profite center the parent, other often same as requestedSignedInCompanyId
		/// requestedPermissionClientId is the companyId where the next permission record is located,reuqired for permission calcalation
		/// requestedRoleId the expected roleId, i.e.  1 for the ribadmin role
		/// 
		/// this is the easiest way to get an valid the company context
		/// </summary>
		/// <param name="requestedSignedInCompanyId"></param>
		/// <param name="requestedRoleId"></param>
		/// <returns></returns>
		public async Task<Response> CheckCompanyToAssignedCompaniesAsync(int requestedSignedInCompanyId, int requestedCompanyId, int requestedPermissionClientId, int requestedRoleId)
		{

			Console.WriteLine("CheckCompanyToAssignedCompanies - Start");
			// ReSharper disable once ConvertClosureToMethodGroup
			var t = new Task<Response>(() => CheckCompanyToAssignedCompanies(requestedSignedInCompanyId, requestedCompanyId, requestedPermissionClientId, requestedRoleId));
			t.Start();
			var responseResult = await t;
			Console.WriteLine("CheckCompanyToAssignedCompanies - Done");
			return responseResult;
		}


		private Response CheckCompanyToAssignedCompanies(int requestedSignedInCompanyId, int requestedCompanyId, int requestedPermissionClientId, int requestedRoleId)
		{
			var fctResponse = new Response() { Result = false };
			using (var client = new HttpClient())
			{
				SetTokenClientContext(client);

				try
				{
					// send Get Call to Backend
					var url = ServicesUrl + Constant.CheckCompaniesWithRoles +
					string.Format("?requestedSignedInCompanyId={0}&requestedCompanyId={1}&requestedPermissionClientId={2}&requestedRoleId={3}",
											requestedSignedInCompanyId, requestedCompanyId, requestedPermissionClientId, requestedRoleId);

					var response = client.GetAsync(url).Result;

					if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.ResetContent)
						return AnalyseHttpStatusCode(response);

					return ReadResponseValue(response, fctResponse);

				}
				catch (Exception ex)
				{
					fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
					return fctResponse;
				}
			}
		}


		#endregion

		#region Get Companies With Roles
		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public async Task<Response> GetCompaniesWithRolesAsync()
		{

			Console.WriteLine("GetCompaniesWithRoles - Start");
			// ReSharper disable once ConvertClosureToMethodGroup
			var t = new Task<Response>(() => GetCompaniesWithRoles());
			t.Start();
			var responseResult = await t;
			Console.WriteLine("GetCompaniesWithRoles - Done");

			return responseResult;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		private Response GetCompaniesWithRoles()
		{
			var fctResponse = new Response() { Result = false };

			using (var client = new HttpClient())
			{
				SetTokenClientContext(client);

				try
				{
					// send Get Call to Backend
					var url = ServicesUrl + Constant.GetAssignedCompaniesWithRoles;
					var response = client.GetAsync(url).Result;

					if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.ResetContent)
						return AnalyseHttpStatusCode(response);

					return ReadResponseValue(response, fctResponse);

				}
				catch (Exception ex)
				{
					fctResponse.ResponseValue = ex.Message + "\r\n" + ex.StackTrace;
					return fctResponse;
				}
			}
		}

		#endregion

		#region itwo40 login
		/// <summary>
		/// This method logs the user into the itwo4.0 system via user/password combination
		/// 
		/// </summary>
		/// <param name="theServiceData"></param>
		/// <returns></returns>
		public async Task<string> DoLoginAsync(ServiceData theServiceData)
		{
			Console.WriteLine("DoLoginAsync - Start");
			var t = new Task<string>(() => DoLogin(theServiceData));
			t.Start();
			var result = await t;
			Console.WriteLine("DoLoginAsync - Done");
			return result;
		}

		/// <summary>
		/// This method call the iTWOCloud WEB-Api. 
		/// Header will be enhanced by token and context
		/// we use new logon api with username and password in body via post
		/// </summary>
		public string DoLogin(ServiceData theServiceData)
		{
			// send Get Call to Backend
			using (var client = new HttpClient())
			{
				string result = null;
				try
				{
					var url = ServicesUrl + Constant.DoLogonUrl20;
					var data = string.Format("{{\"username\": \"{0}\",\"password\": \"{1}\"}}",
												theServiceData.Username, theServiceData.GetUnsecurePassword());
					var myContent = new StringContent(data, Encoding.UTF8, "application/json"); // defined communication format and add parameter content into it
					var response = client.PostAsync(url, myContent).Result;
					var theResult = response.Content.ReadAsStringAsync().Result;
					if (response.IsSuccessStatusCode)
					{
						result = JsonConvert.DeserializeObject<string>(theResult);
						return result;
					}
					result = "\r\nLogin failed! Error: " + theResult;
					return result;
				}
				catch (AccessViolationException ex)
				{
					result = ex.Message + "\r\nLogin failed wrong username/password! Error: " + ex.StackTrace;
					return result;
				}
				catch (Exception ex)
				{
					result = ex.Message + "\r\nLogin failed! other failure, Error: " + ex.StackTrace;
					return result;
				}
			}
		}
		#endregion login
	}
}