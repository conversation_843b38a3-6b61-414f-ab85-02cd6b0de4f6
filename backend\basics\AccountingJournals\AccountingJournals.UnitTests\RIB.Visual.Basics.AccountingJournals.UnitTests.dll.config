<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="RIB.Visual.Platform.AppServer.Console.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>

  <appSettings>
    <add key="owin:appStartup" value="RIB.Visual.Platform.AppServer.Web.Startup,RIB.Visual.Platform.AppServer.Web" />
    <add key="identity:localValidationMode" value="true" />
    <add key="identity:authority" value="https://rib-s-itwocld5d.rib-software.com/identity/core/" />
    <add key="identity:resultCacheDuration" value="120" />
    <add key="identity:x509Thumbprint" value="FB1AE09DFB411E29FA5E18BF4EA3DFC616A01239" />
    <add key="identity:x509SerialNumber" value="" />
    <add key="reporting:createConfiguration" value="true" />
    <add key="reporting:reportBasePath" value="\\rib-software.com\rib-dfs\iTWO-SVC-Data\sql12-dev\iTWOCloud\reports" />
    <add key="reporting:outputPath" value="\\rib-software.com\rib-dfs\iTWO-SVC-Data\sql12-dev\iTWOCloud\downloads\reports" />
    <add key="client:relativeUrl" value="clients" />
    <add key="dbcontext:trace" value="true" />
  </appSettings>
  <connectionStrings>
    <add connectionString="Server=rib-s-sql-dev\dev1;Database=iTWOCloud;Integrated Security=true" name="Default" />
  </connectionStrings>
  <entityFramework>
    <interceptors>
      <interceptor type="System.Data.Entity.Infrastructure.Interception.DatabaseLogger, EntityFramework">
        <parameters>
          <parameter value="C:\Temp\EF61-SQLLog-Output.log" />
        </parameters>
      </interceptor>
    </interceptors>
  </entityFramework>
</configuration>
