using System.Collections.Generic;
using System.Web.Http;
using RIB.Visual.Awp.Main.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;
using RIB.Visual.Basics.Core.Core;
using System.Linq;
using RIB.Visual.Boq.Main.BusinessComponents;

namespace RIB.Visual.Awp.Main.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	[RoutePrefix("awp/main")]
	public class AwpMainController: ApiControllerBase<PackageStructureLineItemLogic>
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		[Route("hierarchy")]
		[HttpPost]
		public IEnumerable<GroupingStructureNode> Hierarchy(PackageStructureLineItemGroupingRequest request)
		{
			var packageLogic = new PackageStructureLineItemLogic();
			packageLogic.ActivateCollaborators(request.ProjectId);

			return PackageStructureLineItemLogic.Hierarchy(request);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="dto"></param>
		/// <returns></returns>
		[Route("update")]
		[HttpPost]
		public AwpMainCompleteDto Update(AwpMainCompleteDto dto)
		{
			var AwpMainCompleteEntity = dto.copy();
			if (AwpMainCompleteEntity.AwpPackageItemAssignmentToSave != null && AwpMainCompleteEntity.AwpPackageItemAssignmentToSave.Any())
			{
				var identificationData = AwpMainCompleteEntity.AwpPackageItemAssignmentToSave.Select(e => new IdentificationData() { Id = e.Id });

				var prcItemAssignmentEntitys = Injector.Get<IPrcItemAssignmentLogic>().GetByIdentificationData(identificationData);

				Injector.Get<IPrcItemAssignmentLogic>().SavePrcItemAssignments(AwpMainCompleteEntity.AwpPackageItemAssignmentToSave);

				var items = new List<IPrcItemAssignmentEntity>();
				foreach (var item in AwpMainCompleteEntity.AwpPackageItemAssignmentToSave)
				{
					items.Add((IPrcItemAssignmentEntity)item);
				}
				Injector.Get<IPrcItemAssignmentLogic>().CalculatePackage(items, prcItemAssignmentEntitys);

				List<IPrcItemAssignmentEntity> prcItemAssignmentEntities = new List<IPrcItemAssignmentEntity>();

				if (prcItemAssignmentEntitys != null)
				{
					foreach (var item in AwpMainCompleteEntity.AwpPackageItemAssignmentToSave.OfType<IPrcItemAssignmentEntity>())
					{
						var entity = prcItemAssignmentEntitys.FirstOrDefault(e => e.Id == item.Id && (e.PrcPackageFk != item.PrcPackageFk || e.BoqItemFk != item.BoqItemFk || e.PrcItemFk != item.PrcItemFk));

						if (entity != null)
						{
							prcItemAssignmentEntities.Add(entity);
						}
					}
				}

				if (prcItemAssignmentEntities.Any())
				{
					Injector.Get<IPrcItemAssignmentLogic>().CalculatePackage(prcItemAssignmentEntitys);
				}
				dto.AwpPackageItemAssignmentToSave = AwpMainCompleteEntity.AwpPackageItemAssignmentToSave;
			}

            // Save ServicePackagesToSave
            if (AwpMainCompleteEntity.ServicePackagesToSave != null && AwpMainCompleteEntity.ServicePackagesToSave.Any())
            {
				var boqItemId2BriefInfo = AwpMainCompleteEntity.ServicePackagesToSave.Where(e => e.ServicePackages != null).GroupBy(e => e.ServicePackages.Id).ToDictionary(e => e.Key, e => e.First().ServicePackages.BriefInfo);
                
				new BoqItemAwpLogic().UpdateBoqItemBriefInfo(boqItemId2BriefInfo);
            }

			return dto;
		}
	}
}
