﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Platform.Core;
using Newtonsoft.Json;
using RIB.Visual.Basics.Api.Common;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public interface IPagination
	{
		/// <summary>
		/// 
		/// </summary>
		int? PageIndex { get; set; }

		/// <summary>
		/// 
		/// </summary>
		int? PageSize { get; set; }
	}

	/// <summary>
	/// 
	/// </summary>
	public interface IPatternSearchable
	{
		/// <summary>
		/// 
		/// </summary>
		string Pattern { get; set; }
	}

	/// <summary>
	/// 
	/// </summary>
	public interface ICriteriaSearchable
	{
		/// <summary>
		/// 
		/// </summary>
		string Filter { get; set; }
	}

	/// <summary>
	/// 
	/// </summary>
	public interface IParentIdFilter
	{
		/// <summary>
		/// 
		/// </summary>
		IEnumerable<int> ParentIds { get; set; }
	}

	/// <summary>
	/// 
	/// </summary>
	public interface INonActiveFilter
	{
		/// <summary>
		/// 
		/// </summary>
		bool IncludeNonActiveItems { get; set; }
	}

	/// <summary>
	/// 
	/// </summary>
	public interface IFetchAll
	{
	}

	/// <summary>
	/// 
	/// </summary>
	public abstract class PublicApiFilterRequest : PublicApiRequestBase, IPagination, IPatternSearchable, ICriteriaSearchable, IParentIdFilter, INonActiveFilter, IFetchAll
	{
		/// <summary>
		/// 
		/// </summary>
		protected PublicApiFilterRequest()
		{
			Options = new FilterOptions();
		}

		//public bool GetAll { get; set; } //90% -> small data

		private int? _pageIndex;

		/// <summary>
		/// Specify page index for the query. Start form zero.
		/// </summary>
		[ApiDocDtoPropertyIgnore]
		[JsonProperty("pageIndex")]
		int? IPagination.PageIndex
		{
			get
			{
				if (Options.HasPagination)
				{
					return _pageIndex;
				}

				throw new NotSupportedException("Pagination is not enabled. Please set [Options.HasPagination] in the Request constructor to activate it for your requirement");
			}
			set
			{
				if (Options.HasPagination)
				{
					_pageIndex = value;
				}
			}
		}

		private int? _pageSize;

		/// <summary>
		/// Specify page size for the query.
		/// </summary>
		[ApiDocDtoPropertyIgnore]
		[JsonProperty("pageSize")]
		int? IPagination.PageSize
		{
			get
			{
				if (Options.HasPagination)
				{
					return _pageSize;
				}

				throw new NotSupportedException("Pagination is not enabled. Please set [Options.HasPagination] in the Request constructor to activate it for your requirement");
			}
			set
			{
				if (Options.HasPagination)
				{
					_pageSize = value;
				}
			}
		}

		private string _pattern;
		/// <summary>
		/// 
		/// </summary>
		[ApiDocDtoPropertyIgnore]
		[JsonProperty("pattern")]
		string IPatternSearchable.Pattern
		{
			get
			{
				if (Options.HasPatternSearch)
				{
					return _pattern;
				}

				throw new NotSupportedException("PatternSearch is not enabled. Please set [Options.HasPatternSearch] in the Request constructor to activate it for your requirement");
			}
			set
			{
				if (Options.HasPatternSearch)
				{
					_pattern = value;
				}
			}
		}

		private string _fiter;
		/// <summary>
		/// Filter string.
		/// i.e:
		/// "{\"operator\":\"and\",\"criterions\":[{\"operator\":\"in\",\"attribute\":\"Title\",\"valueList\":[\"Frau\",\"Unternehmen\"]},{\"operator\":\"in\",\"attribute\":\"Clerk\",\"valueList\":[\"Mustermann, Max\"]}]}"
		/// it means Title has value "Frau" or "Unternehmen" and Clerk has value "Mustermann, Max".
		/// </summary>		
		[ApiDocDtoPropertyIgnore]
		[JsonProperty("filter")]
		string ICriteriaSearchable.Filter
		{
			get
			{
				if (Options.HasCriteriaSearch)
				{
					return _fiter;
				}

				throw new NotSupportedException("CriteriaSearch is not enabled. Please set [Options.HasCriteriaSearch] in the Request constructor to activate it for your requirement");
			}
			set
			{
				if (Options.HasCriteriaSearch)
				{
					_fiter = value;
				}
			}
		}

		private IEnumerable<int> _parentIds;

		/// <summary>
		/// Specify parent ids for the query.
		/// </summary>
		[ApiDocDtoPropertyIgnore]
		[JsonProperty("parentIds")]
		IEnumerable<int> IParentIdFilter.ParentIds //small data only
		{
			get
			{
				if (Options.HasParentIdFilter)
				{
					return _parentIds;
				}

				throw new NotSupportedException("ParentIdFilter is not enabled. Please set [Options.HasParentIdFilter] in the Request constructor to activate it for your requirement");
			}
			set
			{
				if (Options.HasParentIdFilter)
				{
					_parentIds = value;
				}
			}
		}

		private bool? _includeNonActiveItems;
		/// <summary>
		/// 
		/// </summary>
		[ApiDocDtoPropertyIgnore]
		[JsonProperty("includeNonActive")]
		bool INonActiveFilter.IncludeNonActiveItems
		{
			get
			{
				if (Options.HasNonActiveFilter)
				{
					return _includeNonActiveItems.Value;
				}

				throw new NotSupportedException("NonActiveFilter is not enabled. Please set [Options.HasNonActiveFilter] in the Request constructor to activate it for your requirement");
			}
			set
			{
				if (Options.HasNonActiveFilter)
				{
					_includeNonActiveItems = value;
				}
			}
		}

		/// <summary>
		/// 
		/// </summary>
		[ApiDocDtoPropertyIgnore]
		[JsonIgnore]
		protected FilterOptions Options
		{
			get;
			set;
		}

		/// <summary>
		/// 
		/// </summary>
		public class FilterOptions
		{
			/// <summary>
			/// 
			/// </summary>
			public FilterOptions()
			{
				
			}

			/// <summary>
			/// 
			/// </summary>
			public bool HasPatternSearch
			{
				get;
				set;
			}

			/// <summary>
			/// 
			/// </summary>
			public bool HasCriteriaSearch
			{
				get;
				set;
			}

			/// <summary>
			/// 
			/// </summary>
			public bool HasParentIdFilter
			{
				get;
				set;
			}

			/// <summary>
			/// 
			/// </summary>
			public bool HasPagination
			{
				get;
				set;
			}

			/// <summary>
			/// 
			/// </summary>
			public bool HasNonActiveFilter
			{
				get;
				set;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <typeparam name="T"></typeparam>
		/// <returns></returns>
		public bool TryGetFilterInfo<T>(out T obj) 
		{
			bool ret = false;

			if ((typeof(T) == typeof(IPagination) && Options.HasPagination) ||
				(typeof(T) == typeof(IPatternSearchable)&& Options.HasPatternSearch) ||
				(typeof(T) == typeof(ICriteriaSearchable) && Options.HasCriteriaSearch) ||
				(typeof(T) == typeof(IParentIdFilter) && Options.HasParentIdFilter) ||
				(typeof(T) == typeof(INonActiveFilter) && Options.HasNonActiveFilter) ||
				(typeof(T) == typeof(IFetchAll) && !Options.HasPagination && !Options.HasPatternSearch && !Options.HasCriteriaSearch && !Options.HasParentIdFilter && !Options.HasNonActiveFilter))
			{
				ret = true;
				obj = (T)((object)this);
			}
			else
			{
				obj = default(T);
			}

			return ret;
		}
	}
}
