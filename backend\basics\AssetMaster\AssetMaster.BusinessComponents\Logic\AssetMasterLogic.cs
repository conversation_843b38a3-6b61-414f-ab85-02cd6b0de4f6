using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data.Entity;
using System.Threading.Tasks;
using System.Transactions;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.LookupData.BusinessComponents;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;
using System.Data.Entity.Infrastructure;
using System.ComponentModel.Composition;
using RIB.Visual.Cloud.Common.Core;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core.Final;
using RIB.Visual.Basics.Common.BusinessComponents.Final;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using DbContext = RIB.Visual.Platform.BusinessComponents.DbContext;
using System.Security.AccessControl;
using RIB.Visual.Basics.Core.Core;

namespace RIB.Visual.Basics.AssetMaster.BusinessComponents
{
	/// <summary/>
	[Export(typeof(IEntityInfoProvider))]
	public class LastObjectEntityInfo : LastObjectEntityInfoBase
	{
		/// <summary/>
		public LastObjectEntityInfo()
			: base("basics.assetmaster") { }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entityInfos"></param>
		/// <returns></returns>
		public override IEnumerable<IEntityInfoData> GetEntityInfoData(IEnumerable<IEntityInfoData> entityInfos)
		{
			return GetEntityInfo<AssetMasterEntity>(
					ModelBuilder.DbModel,
					entityInfos,
					(query, ids) => query.Where(entity => ids.Contains(entity.Id)),
					e => e.Id,
					e => String.Join(",", new string[] { e.Code, e.DescriptionInfo.Description }),
					new AssetMasterLogic().Translate
					);
		}
	}


	/// <summary>
	/// Asset Master Business Logic should be placed here
	/// 
	/// class derived from platform LogicBase Class
	/// </summary>
	[Export(typeof(IAssetMasterLogic))]
	public class AssetMasterLogic : EntityLogicLegacyAdapter<AssetMasterEntity>, IAssetMasterLogic
	{

		/// <summary>
		/// Default constructor
		/// </summary>
		public AssetMasterLogic()
		{
			this.GetTranslatedProperties = new Func<AssetMasterEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo };
			this.ParentIdFn = e => e.AssetMasterParentFk;
			this.GetChildren = e => e.AssetMasterChildren;
			this.PermissionGUID = "3c17e18947514d48ab6417ed2d991f63";
			SetTempMatchingFunc<DdTempIdsEntity>((e, tmp) => (e.Id == tmp.Id));
			this.IsRootEntity = e => true;
			this.OrderByExpressions = new List<OrderTerm>()

{
		 OrderTerm.Create(e=>e.Code)
};


		}

		/// <summary>
		/// Get Tree
		/// </summary>
		/// <param name="filterValue"></param>
		/// <returns></returns>
		public IEnumerable<AssetMasterEntity> GetTree(string filterValue = "")
		{
			IEnumerable<AssetMasterEntity> query = GetTreeCore("MDC_ASSETMASTR_ITEMTREE_IDS_SP", filterValue, CompanyInfo.GetMasterDataContext());
			PostProcess(query);
			return query;
		}

		/// <summary>
		/// GetSearchList
		/// </summary>
		/// <param name="filterValue"></param>
		/// <returns></returns>
		public override IEnumerable<AssetMasterEntity> GetSearchList(string filterValue = "")
		{
			filterValue = string.IsNullOrEmpty(filterValue) ? "IsLive = true And AllowAssignment = true" : string.Format("({0}) {1}", filterValue, "And (IsLive = true And AllowAssignment = true)");
			return GetTree(filterValue).OrderBy(e => e.Code);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <param name="response"></param>
		/// <returns></returns>
		protected override RIB.Visual.Basics.Common.BusinessComponents.Final.SearchSpecification<AssetMasterEntity, int> GetListSearchContext(FilterRequest request, out FilterResponse response)
		{
			var context = base.GetListSearchContext(request, out response);
			var contextId = CompanyInfo.GetMasterDataContext();
			context.CustomPredicate = e => e.MdcContextFk == contextId;
			context.RestrictToActivePredicate = e => e.IsLive;
			return context;
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="searchContext"></param>
		/// <param name="dbContext"></param>
		/// <param name="query"></param>
		/// <returns></returns>
		protected override IQueryable<AssetMasterEntity> EvaluatePatternSearch(RIB.Visual.Basics.Common.BusinessComponents.Final.SearchSpecification<AssetMasterEntity, int> searchContext, DbContext dbContext, IQueryable<AssetMasterEntity> query)
		{
			var option = new QueryFilterOptions<AssetMasterEntity>();
			option.Add(e => e.Code);
			option.Add(e => e.DescriptionInfo);
			option.IgnoreSearchPattern = true;
			option.SearchText = searchContext.FilterIn.Pattern;
			return query.JoinTrAndFilter<AssetMasterEntity, TranslationEntity>(dbContext.Set<TranslationEntity>(), option);
		}
		/// <summary>
		/// create an empty uom entity
		/// intialiaze the primary key (id) from sequence manager
		/// </summary>
		/// <returns></returns>
		public AssetMasterEntity CreateEntity(int parentId)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var id = this.SequenceManager.GetNext("MDC_ASSET_MASTER");

				// init default values here
				var entity = new AssetMasterEntity();
				entity.Id = id;
				entity.MdcContextFk = CompanyInfo.GetMasterDataContext();
				entity.IsLive = true;

				if (parentId > 0)
				{
					entity.AssetMasterParentFk = parentId;
				}

				return entity;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entities"></param>
		/// <param name="dbContext"></param>
		protected override void SavePostProcessing(IEnumerable<AssetMasterEntity> entities, RVPBizComp.DbContext dbContext)
		{
			foreach (var item in entities)
			{
				dbContext.ExecuteStoredProcedure("MDC_ASSET_MASTER_LEVEL_PROC", CompanyInfo.GetMasterDataContext());
			}
		}

		///// <summary>
		///// Save entity in transaction
		///// </summary>
		///// <returns></returns>
		//public IEnumerable<AssetMasterEntity> SaveEntityTransactional(AssetMasterEntity entity)
		//{
		//	try
		//	{
		//		using (var transaction = TransactionScopeFactory.Create())
		//		{
		//			if (entity.AddressEntity != null)
		//			{
		//				var lgcAddress = new AddressLogic();
		//				lgcAddress.SaveNewOrUpdate(lgcAddress.GetDbModel(), entity.AddressEntity);
		//				entity.AddressFk = entity.AddressEntity.Id;
		//			}

		//			if (entity.AddressEntity == null)
		//			{
		//				entity.AddressFk = null;
		//			}

		//			List<AssetMasterEntity> assetMasterList = new List<AssetMasterEntity>();
		//			getAssetMasterList(entity, assetMasterList);
		//			var result = Save(assetMasterList as IEnumerable<AssetMasterEntity>);
		//	//		var result = Save(entity);

		//			transaction.Complete();
		//			return result;
		//		}

		//	}
		//	catch (Exception e)
		//	{
		//		throw e;
		//	}

		//}

		/// <summary>
		/// Save entities in transaction
		/// </summary>
		/// <returns></returns>
		public IEnumerable<AssetMasterEntity> SaveEntitiesTransactional(IEnumerable<AssetMasterEntity> entities)
		{
			try
			{
				using (var transaction = TransactionScopeFactory.CreateRequiresNew())
				{

					List<AssetMasterEntity> _assetMasterList = new List<AssetMasterEntity>();
					foreach (var entity in entities)
					{
						if (entity.AddressEntity != null)
						{
							var lgcAddress = new AddressLogic();
							lgcAddress.SaveNewOrUpdate(lgcAddress.GetDbModel(), entity.AddressEntity);
							entity.AddressFk = entity.AddressEntity.Id;
						}

						if (entity.AddressEntity == null)
						{
							entity.AddressFk = null;
						}

						//List<AssetMasterEntity> assetMasterList = new List<AssetMasterEntity>();
						//getAssetMasterList(entity, assetMasterList);
						//_assetMasterList.AddRange(assetMasterList);
					}
					//var result = Save(_assetMasterList as IEnumerable<AssetMasterEntity>);
					foreach (var entity in entities)
					{
						Save(entity);
					}
					//var result = Save(entities as IEnumerable<AssetMasterEntity>);

					transaction.Complete();
					return entities;
				}

			}
			catch
			{
				throw;
			}

		}

		private void getAssetMasterList(AssetMasterEntity entity, List<AssetMasterEntity> assetMasterList)
		{
			if (entity != null)
			{
				assetMasterList.Add(entity);
			}

			foreach (var item in entity.AssetMasterChildren)
			{
				getAssetMasterList(item, assetMasterList);
			}
		}

		/// <summary>
		/// GetDbModel
		/// </summary>
		/// <returns></returns>
		public override DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}

		/// <summary>
		/// Return asset master tree structure according to the login master data context id
		/// </summary>
		/// <returns></returns>
		public IEnumerable<AssetMasterEntity> GetAssetMasterLookupTree()
		{
			return GetSearchList(string.Empty);
		}

		/// <summary>
		/// Fill relate properties of TEntity
		/// </summary>
		/// <param name="items"></param>
		/// <returns></returns>
		protected override void PostProcess(IEnumerable<AssetMasterEntity> items)
		{
			base.PostProcess(items);

			var addressLogic = new AddressLogic();
			var addressIds = CollectAddressIds(items);
			var addresses = addressLogic.GetSearchList(x => addressIds.Contains(x.Id));
			SetAddress(items, addresses);


		}

		private List<int> CollectAddressIds(IEnumerable<AssetMasterEntity> items, List<int> ids = null)
		{
			if (ids == null)
			{
				ids = new List<int>();
			}

			foreach (AssetMasterEntity master in items)
			{
				if (master.AddressFk != null)
				{
					ids.Add(master.AddressFk.Value);
				}

				if (master.AssetMasterChildren != null && master.AssetMasterChildren.Count > 0)
				{
					CollectAddressIds(master.AssetMasterChildren, ids);
				}
			}

			return ids;
		}

		private void SetAddress(IEnumerable<AssetMasterEntity> items, IEnumerable<AddressEntity> addresses)
		{
			foreach (var entity in items)
			{
				entity.AddressEntity = addresses.FirstOrDefault(x => x.Id == entity.AddressFk);
				if (entity.AssetMasterChildren != null && entity.AssetMasterChildren.Count > 0)
				{
					SetAddress(entity.AssetMasterChildren, addresses);
				}
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		protected override TreeOptions GetLookupTreeOptions(LookupSearchRequest request)
		{

			var lazyLoad = request.TreeState.Depth.HasValue;
			var options = !lazyLoad
				? new TreeOptions("MDC_ASSETMASTR_ITEMTREE_IDS_SP", CompanyInfo.GetMasterDataContext())
				: new TreeOptions("MDC_ASSETMASTR_ITEMTREE_IDS_SP", CompanyInfo.GetMasterDataContext(), request.TreeState.StartId, request.TreeState.Depth.Value + 1);

			options.StartId = request.TreeState.StartId;
			options.Depth = request.TreeState.Depth ?? 10;
			options.TreeFilter = (e) => e.IsLive;
			options.RequirePaging = request.RequirePaging;
			options.LazyLoad = lazyLoad;
			options.OrderFieldName = "Code";
			return options;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="commonQuery"></param>
		/// <param name="request"></param>
		/// <param name="context"></param>
		/// <returns></returns>
		protected override IQueryable<AssetMasterEntity> DoBuildLookupSearchFilter(IQueryable<AssetMasterEntity> commonQuery, LookupSearchRequest request, RVPBizComp.DbContext context)
		{

			Expression<Func<AssetMasterEntity, bool>> predicate = e => true;
			switch (request.FilterKey)
			{
				case "basics-asset-master-filter":
				case "basics-asset-master-dialog-filter":

					int? assetMasterFK = request.TryGetParameterValueAsInt("AssetMasterFk");
					if (assetMasterFK.HasValue)
					{
						predicate = predicate.And(e => e.AssetMasterLevel1Fk == assetMasterFK || e.AssetMasterLevel2Fk == assetMasterFK || e.AssetMasterLevel3Fk == assetMasterFK || e.AssetMasterLevel4Fk == assetMasterFK || e.AssetMasterLevel5Fk == assetMasterFK);
					}

					break;
			}

			if (!string.IsNullOrWhiteSpace(request.SearchText) && request.SearchFields != null && request.SearchFields.Any())
			{
				var queryOptions = new QueryFilterOptions<AssetMasterEntity>()
				{
					SearchText = request.SearchText,
					IgnoreSearchPattern = true
				};
				queryOptions.Add(request.SearchFields);

				commonQuery = commonQuery.JoinTrAndFilter(context.Set<TranslationEntity>(), queryOptions);
			}

			commonQuery = commonQuery.Where(predicate);

			return commonQuery;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entitySet"></param>
		/// <returns></returns>
		protected IEnumerable<AssetMasterEntity> ProcessData(IEnumerable<AssetMasterEntity> entitySet)
		{
			this.PostProcess(entitySet);
			var entitySetOrder = entitySet.OrderBy(e => e.Code);
			return entitySetOrder;
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="entities"></param>
		protected override void PrepareDelete(IEnumerable<AssetMasterEntity> entities)
		{
			foreach (var entity in entities)
			{
				ForEach(entity, e =>
				{
					e.AssetMasterChildren = new List<AssetMasterEntity>();
				});
			}
		}

		private void ForEach(AssetMasterEntity entity, Action<AssetMasterEntity> action)
		{
			var children = DoGetChildren(entity);

			if (children != null)
			{
				foreach (var childItem in children)
				{
					ForEach(childItem, action);
				}
			}

			if (action != null)
			{
				action(entity);
			}
		}

		/// <summary>
		/// Return asset master tree structure according to the login master data context id
		/// </summary>
		/// <returns></returns>
		IEnumerable<IAssetMasterEntity> IAssetMasterLogic.GetAssetMasterLookupTree()
		{
			return GetSearchList(string.Empty);
		}
	}
}
