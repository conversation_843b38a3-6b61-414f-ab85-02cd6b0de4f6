using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.AccessControl;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using RIB.Visual.Basics.Api.Core;

namespace RIB.Visual.Basics.Api.Common
{

	/// <summary>
	/// 
	/// </summary>
	public class InquiryContext
	{
		/// <summary>
		/// 
		/// </summary>
		[JsonProperty("userlanguageId")]
		public int? UserLanguageId { get; set; }

	}

	/// <summary>
	/// Factory class holding general methods for dealing with inquiry items
	/// </summary>
	public static class InquiryFactory
	{


		/// <summary>
		/// 
		/// </summary>
		/// <param name="toBeSerialized"></param>
		/// <returns></returns>
		public static String SerializeInquireItems(object toBeSerialized)
		{

			String returnedJson;
			var serializer = new JsonSerializer();
			serializer.Converters.Add(new JavaScriptDateTimeConverter());
			serializer.NullValueHandling = NullValueHandling.Ignore;

			using (var sw = new StringWriter())
			{
				using (var writer = new JsonTextWriter(sw))
				{
					serializer.Serialize(writer, toBeSerialized);
				}

				returnedJson = sw.ToString();

			}

			return returnedJson;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="jsonfiedItem"></param>
		/// <returns></returns>
		public static InquiryContext GetInquiryContext(string jsonfiedItem)
		{
			if (string.IsNullOrWhiteSpace(jsonfiedItem))
			{
				return null;
			}

			return JsonConvert.DeserializeObject<InquiryContext>(jsonfiedItem);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <typeparam name="TInquiryItemClass"></typeparam>
		/// <param name="jsonfiedObject"></param>
		/// <returns></returns>
		public static IEnumerable<TInquiryItemClass> GetInquireItemFromString<TInquiryItemClass>(string jsonfiedObject)
		{
			if (jsonfiedObject == null)
			{
				return null;
			}
			try
			{
				JArray inquireJson = JArray.Parse(jsonfiedObject);

				IEnumerable<TInquiryItemClass> res =
					inquireJson.Select(o => JsonConvert.DeserializeObject<TInquiryItemClass>(o.ToString()));

				return res;
			}
			catch (JsonException) { }

			return null;

		}

		/// <summary>
		/// 
		/// </summary>
		/// <typeparam name="TInquiryItemClass"></typeparam>
		/// <param name="apiRequestItem"></param>
		/// <returns></returns>
		public static IEnumerable<TInquiryItemClass> GetInquireItemFromString<TInquiryItemClass>(IApiRequestItemEntity apiRequestItem)
		{

			return GetInquireItemFromString<TInquiryItemClass>(apiRequestItem.ItemData);

		}


		/// <summary>
		/// Serializes any object (class) into a json string
		/// with indented formatting
		/// </summary>
		/// <param name="obj"></param>
		/// <param name="compactFormat"></param>
		/// <returns></returns>
		public static String JsonfyObject(object obj, bool compactFormat = false)
		{
			JsonSerializerSettings jsonSettings;
			if (compactFormat)
			{
				jsonSettings = new JsonSerializerSettings
				{
					Formatting = Formatting.None,
					ContractResolver = new CamelCasePropertyNamesContractResolver(),
					DateTimeZoneHandling = DateTimeZoneHandling.Utc
				};
			}
			else
			{
				jsonSettings = new JsonSerializerSettings { Formatting = Formatting.Indented };
			}

			var jsonStream = JsonConvert.SerializeObject(obj, jsonSettings);

			return jsonStream;
		}

	}
}