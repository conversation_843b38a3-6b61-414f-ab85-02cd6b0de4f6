<Window x:Class="RIB.Visual.Basics.Api.Client.WpfApp.MainWindow"
		xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
		xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" Title="iTWO Cloud Sample (Address Connector) 04.Nov.15@V1.2"
		  MinHeight="450" MinWidth="800" Height="700" Width="900" Margin="3" WindowStyle="ThreeDBorderWindow"
		WindowStartupLocation="CenterScreen" Icon="RIB.ico" Background="#FFF3F1F1">
	
	<Grid Margin="5">
		<Grid.RowDefinitions>
			<RowDefinition Height="Auto" MinHeight="28"></RowDefinition>
			<RowDefinition Height="*"></RowDefinition>
		</Grid.RowDefinitions>

		<Grid Grid.Row="0" Margin="0,5,0,20">
			<Label FontSize="14">This Program is used for testing iTWOCloud External Inquiry (InquireAddresses)</Label>
		</Grid>

		<Grid Grid.Row="1" Margin="0">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="122"></ColumnDefinition>
				<ColumnDefinition />
			</Grid.ColumnDefinitions>
			<Grid.RowDefinitions>
				<RowDefinition Height="28"></RowDefinition>
				<RowDefinition Height="28"></RowDefinition>
				<RowDefinition Height="28"></RowDefinition>
				<RowDefinition Height="28"></RowDefinition>
				<RowDefinition Height="Auto" MinHeight="28"></RowDefinition>
				<RowDefinition Height="28"></RowDefinition>
				<RowDefinition Height="28"></RowDefinition>
				<RowDefinition Height="28"></RowDefinition>
				<RowDefinition Height="28"></RowDefinition>
				<RowDefinition Height="28"></RowDefinition>
				<RowDefinition Height="Auto"></RowDefinition>
				<RowDefinition />
				<!--<RowDefinition Height="28"></RowDefinition>-->
			</Grid.RowDefinitions>

			<Label Grid.Column="0" Grid.Row="0">Identity Server Url</Label>
			<Label Grid.Column="0" Grid.Row="1">Username</Label>
			<Label Grid.Column="0" Grid.Row="2">Password</Label>
			<Label Grid.Column="0" Grid.Row="3">Base Url</Label>
			<Label Grid.Column="0" Grid.Row="4">Client Url</Label>
			<Label Grid.Column="0" Grid.Row="5">Services Url</Label>
			<Label Grid.Column="0" Grid.Row="6">Operation</Label>
			<Label Grid.Column="0" Grid.Row="7">Company Code</Label>
			<Label Grid.Column="0" Grid.Row="8">Search Pattern</Label>
			<Label Grid.Column="0" Grid.Row="9">Selection</Label>
			<ComboBox Grid.Column="1" Grid.Row="0" Margin="1,2" SelectedItem="{Binding TheModel.IdentityServerUrl}"
					ItemsSource="{Binding TheModel.IdentityServerUrls}" SelectedIndex="0"
					ToolTip="Select IdentityServerUrl from List">
			</ComboBox>

			<TextBox Text="{Binding TheModel.Username}" Grid.Column="1" Grid.Row="1" Margin="1,2" />
			<PasswordBox Name="Password" Grid.Column="1" Grid.Row="2" Margin="1,2"></PasswordBox>
			<!--<TextBox Text="{Binding TheModel.BaseUrl, Mode=TwoWay}" Grid.Column="1" Grid.Row="2" Margin="1,2"
					ToolTip="Attention: always with / at the end!!!"></TextBox>-->

			<ComboBox Grid.Column="1" Grid.Row="3" Margin="1,2" SelectedItem="{Binding TheModel.BaseUrl}"
					ItemsSource="{Binding TheModel.BaseUrls}" SelectedIndex="0" ToolTip="Select BaseUrl from List">
			</ComboBox>

			<TextBox Text="{Binding TheModel.ClientUrl}" Grid.Column="1" TextWrapping="Wrap" Grid.Row="4" Margin="1,1,1,3"
					IsReadOnly="True" IsTabStop="False" Height="50"></TextBox>
			<TextBox Text="{Binding TheModel.ServicesUrl}" Grid.Column="1" Grid.Row="5" Margin="1,2" IsReadOnly="True"
					IsTabStop="False"></TextBox>

			<ComboBox Grid.Column="1" Grid.Row="6" Margin="1,2" SelectedItem="{Binding TheModel.Operation}"
					ItemsSource="{Binding TheModel.Operations}" SelectedIndex="0" ToolTip="Select operationType for request" SelectionChanged="ComboBox_SelectionChanged">
			</ComboBox>
			<TextBox Text="{Binding TheModel.CompanyCode}" Grid.Column="1" Grid.Row="7" Margin="1,2"></TextBox>
			<TextBox Text="{Binding TheModel.SearchPattern}" Grid.Column="1" Grid.Row="8" Margin="1,2"></TextBox>
			<RadioButton GroupName="Selection" Content="Single"  Grid.Column="2" Grid.Row="9"  IsChecked="{Binding TheModel.IsSelection}" Checked="RadioButton_Checked"></RadioButton>
			<RadioButton GroupName="Selection" Content="Multiple" Grid.Column="3" Grid.Row="9" Width="66" IsChecked="{Binding !TheModel.Selection.IsSelection}"  Checked="RadioButton_Checked"></RadioButton>

			<StackPanel Margin="0,3" Orientation="Horizontal" Grid.Row="10" Grid.Column="0" HorizontalAlignment="Right"
					Grid.ColumnSpan="2">

				<CheckBox IsChecked="{Binding DeleteInquiryAfterRead}" HorizontalAlignment="Left" Margin="0,0,20,0"
						VerticalAlignment="Center">Delete Inquiry After Read</CheckBox>

				<Button Name="Login" Width="120" Margin="3" Click="OnLogin" Height="28">Login</Button>
				<Button Name="AppServerLogin" Width="120" Margin="3" Click="OnLoginViaAppServer" Height="28">Login(AppServer)</Button>
				<Button Name="StartInquiry" Width="120" Margin="3" Click="OnStartInquiry" IsEnabled="{Binding CanStartInquiry}">Start Operation</Button>
				<Button Name="RequestData" Width="120" Margin="3" Click="OnRequestData" IsEnabled="{Binding CanRequestData}">RequestData</Button>
				<Button Name="Reset" Width="120" Margin="3,3,0,3" Click="OnReset" HorizontalAlignment="Right">Reset</Button>
			</StackPanel>
			<Border Grid.Row="11" Grid.Column="0" Grid.ColumnSpan="2" Margin="0" BorderThickness="0"
					BorderBrush="{DynamicResource {x:Static SystemColors.ScrollBarBrushKey}}">

				<TextBox Text="{Binding TheModel.JsonOutput}" TextWrapping="Wrap" FontSize="11" IsEnabled="True"
						IsReadOnly="True" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" Opacity="0.9" />

			</Border>

		</Grid>
	</Grid>
</Window>
