/*
 * $Id$
 * Copyright (c) RIB Software AG
 * 
 */

using System.Collections.Generic;
namespace RIB.Visual.Basics.AccountingJournals.ServiceFacade.WebApi
{
    /// <summary/>
    public partial class CompanyTransHeaderVDto
    {

        ///// <summary/>
        //partial void OnCopy(CompanyTransHeaderVEntity entity) 
        //{
        //}

        ///// <summary/>
        //partial void OnConstruct(CompanyTransHeaderVEntity entity) 
        //{
        //}

        /// <summary>
        /// ErrMsg
        /// </summary>
        public IEnumerable<string> ErrMsg { get; set; }
    }
}
