//using System.Globalization;
//using System.Runtime.Caching;
//using Microsoft.VisualStudio.TestTools.UnitTesting;
//using System.Collections.Generic;
//using System.Threading;
//using System.Diagnostics;
//using System;
//using RIB.Visual.Basics.Api.BusinessComponents;
//using RIB.Visual.Platform.BusinessComponents;
//using RIB.Visual.Platform.OperationalManagement;
//using System.Configuration;
//using System.IO;
//using System.Collections;
//using RIB.Visual.Platform.Core;

//namespace RIB.Visual.Basics.Api.UnitTests
//{
//	/// <summary>
//	///This is a test class for SequenceLogicTest and is intended
//	///to contain all SequenceLogicTest Unit Tests
//	///</summary>
//	[TestClass()]
//	public class InquiryApiTest
//	{

//		private TestContext _testContextInstance;

//		/// <summary>
//		///Gets or sets the test context which provides
//		///information about and functionality for the current test run.
//		///</summary>
//		public TestContext TestContext
//		{
//			get
//			{
//				return _testContextInstance;
//			}
//			set
//			{
//				_testContextInstance = value;
//			}
//		}

//		// 
//		//You can use the following additional attributes as you write your tests:
//		//
//		//Use ClassInitialize to run code before running the first test in the class
//		[ClassInitialize()]
//		public static void MyClassInitialize(TestContext testContext)
//		{
//			TestCommon.InitializeSelfHosting();
//			//TestCommon.InitializeForServerConnectionOld("ribadmin");
////			TestCommon.InitializeForServerConnection("ribadmin", "ribadmin");

//		}

//		//
//		//Use TestCleanup to run code after each test has run
//		//[ClassCleanup()]
//		//public static void MyClassCleanup()
//		//{

//		//}

//		[TestMethod]
//		public void readApiRequestItems_Test()
//		{

//			//TestCommon.InitializeForServerConnection("ribadmin");
//			//
//			//var id = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.DatabaseDefaultLanguageId;
			
//			var logic = new BasicsApiInquiryLogic();

//			var requestItems = logic.GetFilteredRequestItem(); // read All ....


//		}

//	}
//}
