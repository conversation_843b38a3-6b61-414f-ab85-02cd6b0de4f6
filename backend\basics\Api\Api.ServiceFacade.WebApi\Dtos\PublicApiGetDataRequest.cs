﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Platform.Core;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public class PublicApiGetDataRequest : PublicApiRequestBase
	{
		/// <summary>
		/// Specify the query is get all data.
		/// </summary>
		public bool GetAll { get; set; }

		//public IEnumerable<IdentificationData> CompositeIds { get; set; }// todo lst: rename to compositeId.

		/// <summary>
		/// Specify parent ids for the query.
		/// </summary>
		public IEnumerable<int> ParentIds { get; set; }

		/// <summary>
		/// Specify page index for the query. Start form zero.
		/// </summary>
		public int? PageIndex { get; set; }

		/// <summary>
		/// Specify page size for the query.
		/// </summary>
		public int? PageSize { get; set; }
	}
}
