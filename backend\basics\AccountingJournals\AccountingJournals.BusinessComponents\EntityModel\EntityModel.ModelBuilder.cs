﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON>art Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Data.Common;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.ModelConfiguration;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Linq;
using System.Runtime.Serialization;
using System.Xml.Serialization;
using RIB.Visual.Platform.Common;
using System.Data.Entity.Core.EntityClient;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Core.Objects.DataClasses;
using RIB.Visual.Basics.AccountingJournals.BusinessComponents;

namespace RIB.Visual.Basics.AccountingJournals.BusinessComponents
{
    /// <summary/>
    public partial class ModelBuilder
    {
		#region Constructors
		/// <summary>
		/// Initialize a new ModelBuilder object.
		/// </summary>
		public ModelBuilder()
    {
		}
		#endregion

		private static readonly object Locking = new object();
		private static DbCompiledModel _model;

		/// <summary>Creates a compiled entity model </summary>
		public static DbCompiledModel DbModel
		{
			get
			{
				if (_model == null)
				{
					lock (Locking)
					{
						if (_model != null) return _model;
            var modelBuilder = new DbModelBuilder();

            AddMappings(modelBuilder);
            AddAdditionalMappings(modelBuilder);

            modelBuilder.Conventions.Remove<StoreGeneratedIdentityKeyConvention>();

            _model = modelBuilder.Build(RIB.Visual.Platform.BusinessComponents.DbContext.CreateConnection()).Compile();
					}
				}

				return _model;
			}
		}

		// partial method to add special/additional mappings
		static partial void AddAdditionalMappings(DbModelBuilder modelBuilder);

		/// <summary>
		/// Adds the mapping for each entity of this db context.
		/// </summary>
		/// <param name="modelBuilder"></param>
		public static void AddMappings(DbModelBuilder modelBuilder)
		{

            #region CompanyTransHeaderVEntity

            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .HasKey(p => new { p.CompanyFk, p.CompanyPeriodFk, p.EndDate, p.Id, p.Issuccess, p.PostingDate, p.StartDate, p.TradingPeriod, p.TradingYear, p.TransactiontypeFk })
                .ToTable("BAS_COMPANY_TRANSHEADER_V");
            // Properties:
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.CompanyFk)
                    .HasColumnName(@"BAS_COMPANY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.CompanyPeriodFk)
                    .HasColumnName(@"BAS_COMPANY_PERIOD_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.PostingDate)
                    .HasColumnName(@"POSTING_DATE")
                    .IsRequired()
                    .HasColumnType("date");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.TransactiontypeFk)
                    .HasColumnName(@"BAS_TRANSACTIONTYPE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.Issuccess)
                    .HasColumnName(@"ISSUCCESS")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.ReturnValue)
                    .HasColumnName(@"RETURN_VALUE")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.CompanyTransheaderFk)
                    .HasColumnName(@"BAS_COMPANY_TRANSHEADER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.CommentText)
                    .HasColumnName(@"COMMENT_TEXT")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.CompanyTransHeaderStatusFk)
                    .HasColumnName(@"COMPANYTRANSHEADERSTATUSFK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.StartDate)
                    .HasColumnName(@"START_DATE")
                    .IsRequired()
                    .HasColumnType("date");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.EndDate)
                    .HasColumnName(@"END_DATE")
                    .IsRequired()
                    .HasColumnType("date");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.TradingPeriod)
                    .HasColumnName(@"TRADING_PERIOD")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.TradingYear)
                    .HasColumnName(@"TRADING_YEAR")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.TypeDescriptionInfo.Description)
                    .HasColumnName(@"TYPE_DESCRIPTION")
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.TypeDescriptionInfo.DescriptionTr)
                    .HasColumnName(@"TYPE_DESCRIPTION_TR")
                    .HasColumnType("int");
            modelBuilder.Entity<CompanyTransHeaderVEntity>()
                .Property(p => p.TypeAbbreviation)
                    .HasColumnName(@"TYPE_ABBREVIATION")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");

            #endregion

            #region CompanyTransHeaderStatusHistoryEntity

            modelBuilder.Entity<CompanyTransHeaderStatusHistoryEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("BAS_COMPANYTRNHDSTATHSTY");
            // Properties:
            modelBuilder.Entity<CompanyTransHeaderStatusHistoryEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<CompanyTransHeaderStatusHistoryEntity>()
                .Property(p => p.CompanyTransheaderFk)
                    .HasColumnName(@"BAS_COMPANY_TRANSHEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<CompanyTransHeaderStatusHistoryEntity>()
                .Property(p => p.BasCompanytranshdrstatOldFk)
                    .HasColumnName(@"BAS_COMPANYTRANSHDRSTAT_OLD_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<CompanyTransHeaderStatusHistoryEntity>()
                .Property(p => p.BasCompanytranshdrstatNewFk)
                    .HasColumnName(@"BAS_COMPANYTRANSHDRSTAT_NEW_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<CompanyTransHeaderStatusHistoryEntity>()
                .Property(p => p.Remark)
                    .HasColumnName(@"REMARK")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<CompanyTransHeaderStatusHistoryEntity>(modelBuilder);

            #endregion

            #region ComplexTypes

            modelBuilder.ComplexType<DescriptionTranslateType>();

            #endregion

            #region Disabled conventions


            #endregion

        }

    
        /// <summary>
        /// There are no comments for CompanyTransHeaderVEntity in the schema.
        /// </summary>
        public DbSet<CompanyTransHeaderVEntity> CompanyTransHeaderVEntities { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyTransHeaderStatusHistoryEntity in the schema.
        /// </summary>
        public DbSet<CompanyTransHeaderStatusHistoryEntity> CompanyTransHeaderStatusHistoryEntities { get; set; }
    }
}
