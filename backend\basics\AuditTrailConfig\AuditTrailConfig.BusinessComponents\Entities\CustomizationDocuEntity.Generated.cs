﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.AuditTrailConfig.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Basics.AuditTrailConfig.BusinessComponents.CustomizationDocuEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("BAS_CUSTOMIZATION_DOCU")]
    public partial class CustomizationDocuEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new CustomizationDocuEntity object.
        /// </summary>
        public CustomizationDocuEntity()
        {
            OnConstruct(); // call partial constructor if present             
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id { 
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TableName in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TABLENAME", TypeName = "nvarchar(252)", Order = 1)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string TableName { 
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Documentation in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DOCUMENTATION", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string Documentation { 
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 3)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string Remark { 
            get; set;
        }


        #endregion
    
        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            CustomizationDocuEntity obj = new CustomizationDocuEntity();
            obj.Id = Id;
            obj.TableName = TableName;
            obj.Documentation = Documentation;
            obj.Remark = Remark;
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(CustomizationDocuEntity clonedEntity);

    }


}
