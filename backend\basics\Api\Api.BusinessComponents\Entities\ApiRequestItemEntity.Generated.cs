//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.Api.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Basics.Api.BusinessComponents.ApiRequestItemEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("BAS_APIREQUESTITEM")]
    public partial class ApiRequestItemEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new ApiRequestItemEntity object.
        /// </summary>
        public ApiRequestItemEntity()
        {
          this.Status = 0;
            OnConstruct(); // call partial constructor if present             
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for RequestId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQUESTID", TypeName = "varchar(32)", Order = 1)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string RequestId
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ModuleName in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MODULENAME", TypeName = "varchar(252)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string ModuleName
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for RequestContext in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQUESTCONTEXT", TypeName = "text", Order = 11)]
        public virtual string RequestContext
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ItemData in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ITEMDATA", TypeName = "text", Order = 3)]
        public virtual string ItemData
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for Status in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("STATUS", TypeName = "int", Order = 4)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Status
        {
            get;
            set;
        }

    
        /// <summary>
        /// There are no comments for ValidUntil in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VALIDUNTIL", TypeName = "datetime", Order = 5)]
        public virtual System.DateTime? ValidUntil
        {
            get;
            set;
        }


        #endregion
    
        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            ApiRequestItemEntity obj = new ApiRequestItemEntity();
            obj.Id = Id;
            obj.RequestId = RequestId;
            obj.ModuleName = ModuleName;
            obj.RequestContext = RequestContext;
            obj.ItemData = ItemData;
            obj.Status = Status;
            obj.ValidUntil = ValidUntil;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedBy = UpdatedBy;
            obj.InsertedAt = InsertedAt;
            obj.UpdatedAt = UpdatedAt;
            obj.Version = Version;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(ApiRequestItemEntity clonedEntity);

    }


}
