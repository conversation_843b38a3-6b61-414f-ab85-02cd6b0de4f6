﻿using System;
using System.ComponentModel.DataAnnotations;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// 
	/// </summary>
	[AttributeUsage(AttributeTargets.Property)]
	public class CompositeRootKeyAttribute : Attribute
	{
		/// <summary>
		/// 
		/// </summary>
		public string Value { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsKey { get; set; }


		/// <summary>
		/// Constructor
		/// </summary>
		public CompositeRootKeyAttribute()
		{
		}

		/// <summary>
		/// Constructor
		/// </summary>
		public CompositeRootKeyAttribute(string value)
		{
			this.Value = value;
		}

		/// <summary>
		/// Constructor
		/// </summary>
		public CompositeRootKeyAttribute(bool isKey)
		{
			this.IsKey = isKey;
		}
	}
}
