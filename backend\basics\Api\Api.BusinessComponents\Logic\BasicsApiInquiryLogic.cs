using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Transactions;
using Newtonsoft.Json;
using RIB.Visual.Basics.Core.BusinessComponents;
using RIB.Visual.Platform.BusinessComponents;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.Api.BusinessComponents
{

	/// <summary>
	/// UoM Business Logic should be placed here
	///
	/// class derived from platform LogicBase Class
	/// </summary>
	public class BasicsApiInquiryLogic : RVPBizComp.LogicBase
	{
		/// <summary>
		/// Provides access to the database model.
		/// </summary>
		/// <returns></returns>
		public override System.Data.Entity.Infrastructure.DbCompiledModel GetDbModel() => ModelBuilder.DbModel;

		/// <summary>
		/// Save detached ApiRequestItemEntity entity into Database
		/// </summary>
		/// <returns></returns>
		public ApiRequestItemEntity GetInquiry(string requestId)
		{
			ApiRequestItemEntity requestItem = null;
			if (!string.IsNullOrWhiteSpace(requestId))
			{
				using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
				{
					requestItem = dbcontext.GetFiltered<ApiRequestItemEntity>(i => i.RequestId == requestId).FirstOrDefault();
				}
			}

			return requestItem;
		}

		/// <summary>
		/// Returns the UoM sorted by the Sorted filed.
		/// if filter is not null, this expression is used for filtering the data
		/// if filter is null, all items will be returned
		/// </summary>
		/// <returns></returns>
		public IEnumerable<ApiRequestItemEntity> GetFilteredRequestItem(Expression<Func<ApiRequestItemEntity, bool>> filter = null)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				IEnumerable<ApiRequestItemEntity> entities = null;
				if (filter != null)
				{
					entities = dbcontext.GetFiltered<ApiRequestItemEntity>(filter);
				}
				else
				{
					entities = dbcontext.GetAll<ApiRequestItemEntity>();
				}
				return entities;
			}
		}



		/// <summary>
		/// create an empty uom entity
		/// intialiaze the primary key (id) from sequence manager
		/// </summary>
		/// <returns></returns>
		public ApiRequestItemEntity CreateEntity(string requestId)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var id = this.SequenceManager.GetNext("BAS_APIREQUESTITEM");

				// init default values here
				var entity = new ApiRequestItemEntity();
				entity.Id = id;
				entity.RequestId = requestId;
				entity.ModuleName = "";
				entity.RequestContext = "";
				entity.ItemData = "";
				entity.ValidUntil = DateTime.UtcNow.AddHours(6); // this request is valid for the next x hours

				return entity;
			}
		}


		/// <summary>
		/// Save detached UoM entity into Database
		/// </summary>
		/// <returns></returns>
		public ApiRequestItemEntity SaveEntity(ApiRequestItemEntity entity)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				return dbcontext.Save(entity);
			}

		}


		/// <summary>
		/// Save detached ApiRequestItemEntity entity into Database
		/// </summary>
		/// <param name="requestId"></param>
		/// <param name="moduleName"></param>
		/// <param name="status"></param>
		/// <param name="requestContext"></param>
		/// <param name="itemlist"></param>
		/// <param name="deleteOld"></param>
		/// <returns></returns>
		public ApiRequestItemEntity SaveInquiries(string requestId, string moduleName, int status, string requestContext, string itemlist, bool deleteOld=false)
		{
			ApiRequestItemEntity requestItem = null;
			if (!string.IsNullOrWhiteSpace(requestId))
			{
				if (deleteOld)
				{
					DeleteInquiry(requestId);
				}
				requestItem = CreateEntity(requestId);
				requestItem.ModuleName = moduleName;
				requestItem.Status = status;
				requestItem.RequestContext = string.Format("{{ userlanguageId: {0} }}", this.UserLanguageId);  // save in Json format
				requestItem.ItemData = itemlist;

				// now save it
				requestItem = SaveEntity(requestItem);
			}
			return requestItem;
		}


		/// <summary>
		/// deletes the requestitem with REQUESTID = requestId parameter
		/// </summary>
		/// <param name="requestId"></param>
		public bool DeleteInquiry(String requestId)
		{
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				const string sql = "DELETE FROM BAS_APIREQUESTITEM WHERE REQUESTID=@p0";
				var retValue = dbContext.ExecuteSqlCommand(sql, requestId);

				return retValue == 1;
			}
		}

		/// <summary>
		/// deletes the requestitem with REQUESTID = requestId parameter
		/// </summary>
		/// <returns></returns>
		public void DeleteEntity(string requestId)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var entity = dbcontext.GetFiltered<ApiRequestItemEntity>(i => i.RequestId == requestId).FirstOrDefault();
				if (entity != null) { dbcontext.Delete(entity); }
			}
		}


		/// <summary>
		/// this is a code sample for enclosing multiple modification operations via multiple dbcontexts
		/// within one transaction.
		///
		///
		/// </summary>
		/// <returns></returns>
		public void SaveEntityTransactional(ApiRequestItemEntity entity)
		{

			using (var transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				// your dbcontext transactions with local dbcontext
				using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
				{
					dbcontext.Save(entity);
				}

				// Calling an action like save/delete/update in other BusinessCompenent
				// i.e. Cloud.Common
				// var logic = new RIB.Visual.Cloud.Common.BusinessComponents.xyz();
				// sample code
				// logic.Save(new UserEntity());

				// if all Saves/Deletes etc. finish well, we commit this transaction as valid
				transaction.Complete();
			}
		}
	}
}
