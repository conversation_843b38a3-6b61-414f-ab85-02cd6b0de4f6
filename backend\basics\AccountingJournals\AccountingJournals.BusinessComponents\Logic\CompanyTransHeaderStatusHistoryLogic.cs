﻿using System;
using System.Linq;
using System.Collections.Generic;
using RIB.Visual.Basics.Common.BusinessComponents;

namespace RIB.Visual.Basics.AccountingJournals.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class CompanyTransHeaderStatusHistoryLogic : LogicBase<CompanyTransHeaderStatusHistoryEntity>
	{
		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public override System.Data.Entity.Infrastructure.DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="headerId"></param>
		public bool DeleteByHeaderId(int headerId)
		{
			bool flag = false;
			IEnumerable<CompanyTransHeaderStatusHistoryEntity> historyEntities = this.GetSearchList(e => e.CompanyTransheaderFk == headerId);
			if (historyEntities == null || historyEntities.Count() == 0)
			{
				flag = true;
			}
			else
			{
				flag = this.Delete(ModelBuilder.DbModel, historyEntities);
			}
			return flag;
		}
	}
}
