﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Basics.AssetMaster.BusinessComponents;

namespace RIB.Visual.Basics.AssetMaster.ServiceFacade.WebApi
{


    /// <summary>
    /// There are no comments for AssetMasterEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("MDC_ASSET_MASTER")]
    public partial class AssetMasterDto : RIB.Visual.Platform.Core.ITypedDto<AssetMasterEntity>
    {
        #region Constructors

        /// <summary>
        /// Initializes an instance of class AssetMasterDto.
        /// </summary>
        public AssetMasterDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class AssetMasterDto.
        /// </summary>
        /// <param name="entity">the instance of class AssetMasterEntity</param>
        public AssetMasterDto(AssetMasterEntity entity)
        {
            Id = entity.Id;
            AssetMasterParentFk = entity.AssetMasterParentFk;
            MdcContextFk = entity.MdcContextFk;
            Code = entity.Code;
            AddressFk = entity.AddressFk;
            IsLive = entity.IsLive;
            AssetMasterLevel1Fk = entity.AssetMasterLevel1Fk;
            Remark = entity.Remark;
            UserDefined1 = entity.UserDefined1;
            UserDefined2 = entity.UserDefined2;
            UserDefined3 = entity.UserDefined3;
            UserDefined4 = entity.UserDefined4;
            UserDefined5 = entity.UserDefined5;
            AssetMasterLevel2Fk = entity.AssetMasterLevel2Fk;
            AssetMasterLevel3Fk = entity.AssetMasterLevel3Fk;
            AssetMasterLevel4Fk = entity.AssetMasterLevel4Fk;
            AssetMasterLevel5Fk = entity.AssetMasterLevel5Fk;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;
            AllowAssignment = entity.AllowAssignment;

            if (entity.DescriptionInfo != null )
            {
                DescriptionInfo = new DescriptionTranslateTypeDto(entity.DescriptionInfo);
            }

            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }
    
        /// <summary>
        /// There are no comments for AssetMasterParentFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_PARENT_FK", TypeName = "int", Order = 1)]
        public int? AssetMasterParentFk { get; set; }
    
        /// <summary>
        /// There are no comments for MdcContextFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTEXT_FK", TypeName = "int", Order = 2)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int MdcContextFk { get; set; }
    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"code")]
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 3)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string Code { get; set; }
    
        /// <summary>
        /// There are no comments for DescriptionInfo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"translation")]
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 4, TranslationColumnName = "DESCRIPTION_TR")]
        public DescriptionTranslateTypeDto DescriptionInfo { get; set; }
    
        /// <summary>
        /// There are no comments for AddressFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ADDRESS_FK", TypeName = "int", Order = 6)]
        public int? AddressFk { get; set; }
    
        /// <summary>
        /// There are no comments for IsLive in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"boolean")]
        [RIB.Visual.Platform.Common.MappedColumn("ISLIVE", TypeName = "bit", Order = 7)]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsLive { get; set; }
    
        /// <summary>
        /// There are no comments for AssetMasterLevel1Fk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_LEVEL1_FK", TypeName = "int", Order = 14)]
        public int? AssetMasterLevel1Fk { get; set; }
    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(max)", Order = 8)]
        public string Remark { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined1 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined2 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 11)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined3 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined4 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 13)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined5 { get; set; }
    
        /// <summary>
        /// There are no comments for AssetMasterLevel2Fk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_LEVEL2_FK", TypeName = "int", Order = 15)]
        public int? AssetMasterLevel2Fk { get; set; }
    
        /// <summary>
        /// There are no comments for AssetMasterLevel3Fk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_LEVEL3_FK", TypeName = "int", Order = 16)]
        public int? AssetMasterLevel3Fk { get; set; }
    
        /// <summary>
        /// There are no comments for AssetMasterLevel4Fk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_LEVEL4_FK", TypeName = "int", Order = 17)]
        public int? AssetMasterLevel4Fk { get; set; }
    
        /// <summary>
        /// There are no comments for AssetMasterLevel5Fk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_LEVEL5_FK", TypeName = "int", Order = 18)]
        public int? AssetMasterLevel5Fk { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 19)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime InsertedAt { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 20)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 21)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 22)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }
    
        /// <summary>
        /// There are no comments for Version in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 23)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }
    
        /// <summary>
        /// There are no comments for AllowAssignment in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"boolean")]
        [RIB.Visual.Platform.Common.MappedColumn("ALLOW_ASSIGNMENT", TypeName = "bit", Order = 24)]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool AllowAssignment { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(AssetMasterEntity); }
        }

        /// <summary>
        /// Copy the current AssetMasterDto instance to a new AssetMasterEntity instance.
        /// </summary>
        /// <returns>a new instance of class AssetMasterEntity</returns>
        public AssetMasterEntity Copy()
        {
          var entity = new AssetMasterEntity();

          entity.Id = this.Id;
          entity.AssetMasterParentFk = this.AssetMasterParentFk;
          entity.MdcContextFk = this.MdcContextFk;
          entity.Code = this.Code;
          entity.AddressFk = this.AddressFk;
          entity.IsLive = this.IsLive;
          entity.AssetMasterLevel1Fk = this.AssetMasterLevel1Fk;
          entity.Remark = this.Remark;
          entity.UserDefined1 = this.UserDefined1;
          entity.UserDefined2 = this.UserDefined2;
          entity.UserDefined3 = this.UserDefined3;
          entity.UserDefined4 = this.UserDefined4;
          entity.UserDefined5 = this.UserDefined5;
          entity.AssetMasterLevel2Fk = this.AssetMasterLevel2Fk;
          entity.AssetMasterLevel3Fk = this.AssetMasterLevel3Fk;
          entity.AssetMasterLevel4Fk = this.AssetMasterLevel4Fk;
          entity.AssetMasterLevel5Fk = this.AssetMasterLevel5Fk;
          entity.InsertedAt = this.InsertedAt;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedAt = this.UpdatedAt;
          entity.UpdatedBy = this.UpdatedBy;
          entity.Version = this.Version;
          entity.AllowAssignment = this.AllowAssignment;

          if (this.DescriptionInfo != null )
          {

               entity.DescriptionInfo = new DescriptionTranslateType(this.DescriptionInfo);
          }

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(AssetMasterEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(AssetMasterEntity entity);
    }

}
