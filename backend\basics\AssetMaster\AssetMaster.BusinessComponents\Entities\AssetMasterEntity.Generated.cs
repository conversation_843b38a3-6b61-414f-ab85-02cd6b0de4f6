﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Basics.AssetMaster.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Basics.AssetMaster.BusinessComponents.AssetMasterEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("MDC_ASSET_MASTER")]
    public partial class AssetMasterEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new AssetMasterEntity object.
        /// </summary>
        public AssetMasterEntity()
        {
          this.DescriptionInfo = new DescriptionTranslateType();
          this.IsLive = true;
          this.AllowAssignment = true;
            this.AssetMasterChildren = new HashSet<AssetMasterEntity>();
            this.DdTempIdsEntities = new HashSet<DdTempIdsEntity>();
            OnConstruct(); // call partial constructor if present
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AssetMasterParentFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_PARENT_FK", TypeName = "int", Order = 1)]
        public virtual int? AssetMasterParentFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcContextFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTEXT_FK", TypeName = "int", Order = 2)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int MdcContextFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"code")]
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 3)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DescriptionInfo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"translation")]
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 4, TranslationColumnName = "DESCRIPTION_TR")]
        public virtual DescriptionTranslateType DescriptionInfo {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AddressFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ADDRESS_FK", TypeName = "int", Order = 6)]
        public virtual int? AddressFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsLive in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"boolean")]
        [RIB.Visual.Platform.Common.MappedColumn("ISLIVE", TypeName = "bit", Order = 7)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsLive {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AssetMasterLevel1Fk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_LEVEL1_FK", TypeName = "int", Order = 14)]
        public virtual int? AssetMasterLevel1Fk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(max)", Order = 8)]
        public virtual string Remark {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined1 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined2 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 11)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined3 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined4 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"description")]
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 13)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined5 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AssetMasterLevel2Fk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_LEVEL2_FK", TypeName = "int", Order = 15)]
        public virtual int? AssetMasterLevel2Fk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AssetMasterLevel3Fk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_LEVEL3_FK", TypeName = "int", Order = 16)]
        public virtual int? AssetMasterLevel3Fk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AssetMasterLevel4Fk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_LEVEL4_FK", TypeName = "int", Order = 17)]
        public virtual int? AssetMasterLevel4Fk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AssetMasterLevel5Fk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_LEVEL5_FK", TypeName = "int", Order = 18)]
        public virtual int? AssetMasterLevel5Fk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AllowAssignment in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.DomainName(Name = @"boolean")]
        [RIB.Visual.Platform.Common.MappedColumn("ALLOW_ASSIGNMENT", TypeName = "bit", Order = 24)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool AllowAssignment {
            get; set;
        }


        #endregion

        #region Navigation Properties
    
        /// <summary>
        /// There are no comments for AssetMasterChildren in the schema.
        /// </summary>
        public virtual ICollection<AssetMasterEntity> AssetMasterChildren
        {
            get;
            set;
        }
    
        /// <summary>
        /// There are no comments for AssetMasterParent in the schema.
        /// </summary>
        public virtual AssetMasterEntity AssetMasterParent { get; set; }
    
        /// <summary>
        /// There are no comments for DdTempIdsEntities in the schema.
        /// </summary>
        public virtual ICollection<DdTempIdsEntity> DdTempIdsEntities
        {
            get;
            set;
        }

        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            AssetMasterEntity obj = new AssetMasterEntity();
            obj.Id = Id;
            obj.AssetMasterParentFk = AssetMasterParentFk;
            obj.MdcContextFk = MdcContextFk;
            obj.Code = Code;
            obj.DescriptionInfo = (DescriptionTranslateType)DescriptionInfo.Clone();
            obj.AddressFk = AddressFk;
            obj.IsLive = IsLive;
            obj.AssetMasterLevel1Fk = AssetMasterLevel1Fk;
            obj.Remark = Remark;
            obj.UserDefined1 = UserDefined1;
            obj.UserDefined2 = UserDefined2;
            obj.UserDefined3 = UserDefined3;
            obj.UserDefined4 = UserDefined4;
            obj.UserDefined5 = UserDefined5;
            obj.AssetMasterLevel2Fk = AssetMasterLevel2Fk;
            obj.AssetMasterLevel3Fk = AssetMasterLevel3Fk;
            obj.AssetMasterLevel4Fk = AssetMasterLevel4Fk;
            obj.AssetMasterLevel5Fk = AssetMasterLevel5Fk;
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            obj.AllowAssignment = AllowAssignment;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(AssetMasterEntity clonedEntity);

    }


}
