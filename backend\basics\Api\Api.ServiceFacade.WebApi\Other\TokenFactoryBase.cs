using IdentityModel.Client;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.Server.Common;
using RIB.Visual.Services.Platform.BusinessComponents;
using System;
using System.Net;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	internal abstract class TokenFactoryBase
	{
		protected string _ssoServiceUrl;
		protected string _ssoServiceServiceUrl;

		protected int _idpId;

		/// <summary>
		/// 
		/// </summary>
		protected TokenFactoryBase()
		{

		}

		/// <summary>
		/// 
		/// </summary>
		protected static TokenResponse ResultUnauthorizedTicket
		{
			get
			{ return TokenResponseStaticMethods.CreateTokenResponse(HttpStatusCode.Unauthorized, "Ticket validation failed. Can not login with this ticket."); }
		}

		/// <summary>
		/// 
		/// </summary>
		protected static TokenResponse ResultUnauthorizedLogonname
		{
			get
			{ return TokenResponseStaticMethods.CreateTokenResponse(HttpStatusCode.Unauthorized, "Logon name validation failed. Can not login with this Logon name."); }
		}

		/// <summary>
		/// 
		/// </summary>
		protected static TokenResponse ResultUnauthorizedFailed
		{
			get
			{ return TokenResponseStaticMethods.CreateTokenResponse(HttpStatusCode.Unauthorized, "Validation failed. Can not login."); }
		}

		protected TokenResponse CheckLogonNameandCreateToken(string logonName)
		{
			if (string.IsNullOrWhiteSpace(logonName))
			{
				return ResultUnauthorizedLogonname;
			}

			var userLogic = new UserLogic();
			var user = userLogic.LoadUser(logonName);
			if (user == null)
			{
				return ResultUnauthorizedFailed;
			}

			if (user.State != 1) //user should be enable.
			{
				return ResultUnauthorizedFailed;
			}

			var userExtProviderLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IUserExtProviderLogic>();
			if (userExtProviderLogic == null)
			{
				return ResultUnauthorizedFailed;
			}

			var userId = userExtProviderLogic.ValidateIdmUser(_idpId, logonName);
			if (!userId.HasValue)
			{
				return ResultUnauthorizedFailed;
			}

			var idServerLogon = new IdentityServerLogon();
			var tokenResponse = idServerLogon.SsoRequestToken(logonName, _idpId);
			if (!tokenResponse.IsError)
			{
				return tokenResponse;
			}

			var err = tokenResponse.Error;
			if (tokenResponse.Error != null && tokenResponse.Error.Equals("invalid_client"))
			{
				err = "invalid_client: IdentityServer configuration error, requested sso client 'itwo40.sso' not there!";
			}
			else
				if (tokenResponse.HttpStatusCode == HttpStatusCode.InternalServerError)
				{
					err = "Internal Server Error: IdentityServer configuration error, please check idsrv configuration!";
				}

			return TokenResponseStaticMethods.CreateTokenResponse(HttpStatusCode.Unauthorized, "Ticket validation failed. " + err);
		}

		protected virtual void ReadConfig()
		{
			_ssoServiceUrl = AppSettingsReader.ReadString("sso.providerurl");
			_ssoServiceServiceUrl = AppSettingsReader.ReadString("sso.serviceurl");
			_idpId = AppSettingsReader.ReadInt("idm:identityproviderid");
		}

		protected abstract ValidateResult ValidateTicket(string ticket);

		public TokenResponse CreateTokenFromTicket(string ticket)
		{
			ReadConfig();

			try
			{
				ValidateResult result = ValidateTicket(ticket);
				if (result.StatusCode == HttpStatusCode.OK)
				{
					return CheckLogonNameandCreateToken(result.LogonName);
				}

				return TokenResponseStaticMethods.CreateTokenResponse(HttpStatusCode.Unauthorized, string.Format("{0} {1} ",
					"Ticket validation failed. Can not login with this ticket.", result.ErrorCode));
			}
			catch (Exception ex)
			{
				return TokenResponseStaticMethods.CreateTokenResponse(HttpStatusCode.InternalServerError, string.Format("{0} Message: {1} Stacktrace: {2} ",
					"Ticket validation failed. Can not login with this ticket.", ex.Message, ex.StackTrace));
			}
		}
	}
}
