using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Awp.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class GroupingStructureNode
	{
		/// <summary>
		/// 
		/// </summary>
		public int Id { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int EntityHeaderFK { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int EntityId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? EntityParentFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string Code { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string Description { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? DescriptionTr { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int LineTypeFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? Quantity { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? WqQuantity { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? QuantityTotal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? BasUomFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? PrcStructureFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? UnitRate { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? FinalPrice { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? CostTotal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? GrandCostUnitTarget { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? GrandTotal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? AssignmentCount { get; set; }

		/// <summary>
		/// assignment line item ids concat with ','
		/// </summary>
		public string AssignmentLineItemIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? UnAssignmentCount { get; set; }

		/// <summary>
		/// unassignment line item ids concat with ','
		/// </summary>
		public string UnAssignmentLineItemIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public PackageStructureLineItemGroupingType GroupingType { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int StructureLevel { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? BoqHeaderFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? BoqItemFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string Reference { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? BoqLineTypeFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? BoqItemBasisFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public List<GroupingStructureNode> Children = new List<GroupingStructureNode>();

		/// <summary>
		/// gets the assignment line item ids list.
		/// </summary>
		public IEnumerable<int> AssignmentLineItemIdsList { get; set; }

		/// <summary>
		/// gets the unassignment line item ids list.
		/// </summary>
		public IEnumerable<int> UnAssignmentLineItemIdsList { get; set; }
	}
}
