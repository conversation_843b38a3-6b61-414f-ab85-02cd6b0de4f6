using Newtonsoft.Json.Linq;

namespace RIB.Visual.Basics.Api.Client.Itwo40WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public class Response
	{
		/// <summary>
		/// 
		/// </summary>
		// ReSharper disable once UnusedAutoPropertyAccessor.Global
		public bool Result { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string ResponseValue { get; set; }

		
		// ReSharper disable once UnusedAutoPropertyAccessor.Global
		/// <summary>
		/// 
		/// </summary>
		public JObject ObjValue { get; set; }

	}
}