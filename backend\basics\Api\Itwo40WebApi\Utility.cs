using System;
using System.ComponentModel;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace RIB.Visual.Basics.Api.Client.Itwo40WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public class Utility
	{
		/// <summary/>
		public Model TheModel { get; set; }

		/// <summary/>
		// ReSharper disable once MemberCanBePrivate.Global
		public string CompanyRolesResponse { get; set; }


		private void worker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
		{
			Console.WriteLine("Logon done...");
			Console.WriteLine(string.Format("Token: {0}", RevitUploadServices.Token));
		}


		/// <summary>
		/// 
		/// </summary>
		public async void LoginAppServerviaAsync()
		{
			TheModel.JsonOutput = "Login via Application Server running...   Please wait...";
			TheModel.SetToSecurePassword("ribadmin");

			RevitUploadServices.Token = await RevitUploadServices.DoLoginAsync(TheModel);

			Console.WriteLine(RevitUploadServices.GetTokenClaimsInfo());

		}

		/// <summary>
		/// 
		/// </summary>
		public async void GetCompaniesWithRolesAwait()
		{
			RevitUploadServices.ServicesUrl = TheModel.ServicesUrl;
			RevitUploadServices.Token = await RevitUploadServices.DoLoginAsync(TheModel);
			Console.WriteLine(RevitUploadServices.GetTokenClaimsInfo());

			Console.WriteLine("Logon Done....");

			var response = await RevitUploadServices.GetCompaniesWithRolesAsync();
			WritelineObjectResult(response);
			CompanyRolesResponse = response.ResponseValue;

		}

		/// <summary>
		/// 
		/// </summary>
		public async void GetProjectWithRevitModelAwait()
		{
			RevitUploadServices.ServicesUrl = TheModel.ServicesUrl;
			RevitUploadServices.Token = await RevitUploadServices.DoLoginAsync(TheModel);
			Console.WriteLine(RevitUploadServices.GetTokenClaimsInfo());

			Console.WriteLine("Logon Done....");

			var response = await RevitUploadServices.GetCompaniesWithRolesAsync();
			CompanyRolesResponse = response.ResponseValue;
			var jObject = JObject.Parse(CompanyRolesResponse);
			var res = JsonConvert.SerializeObject(jObject, Formatting.Indented);
			Console.WriteLine(res);

			//await LoginAppServerviaAsync();
			//Client-Context:{"signedInClientId":1,"clientId":1,"permissionClientId":1,"permissionRoleId":1,"dataLanguageId":1,"language":"en","culture":"en-gb"}

			var myContext = new ServiceClientContext(1, 1, 1, 1, 1, "en", "en-gb");
			RevitUploadServices.ServiceClientContext = myContext;
			var projectWithModels = await RevitUploadServices.GetRevitModelsFromProjectsAsync();
			WritelineArrayResult(projectWithModels);

		}

		private void WritelineArrayResult(Response response)
		{
			var xyz = JArray.Parse(response.ResponseValue);
			var res = JsonConvert.SerializeObject(xyz, Formatting.Indented);
			Console.WriteLine(res);
		}

		private void WritelineObjectResult(Response response)
		{
			var xyz = JObject.Parse(response.ResponseValue);
			var res = JsonConvert.SerializeObject(xyz, Formatting.Indented);
			Console.WriteLine(res);
		}

		/// <summary>
		/// 
		/// </summary>
		public async void UploadModelAwait()
		{
			RevitUploadServices.ServicesUrl = TheModel.ServicesUrl;
			RevitUploadServices.Token = await RevitUploadServices.DoLoginAsync(TheModel);
			Console.WriteLine(RevitUploadServices.GetTokenClaimsInfo());


			Console.WriteLine("Logon Done....");

			var response = await RevitUploadServices.GetCompaniesWithRolesAsync();
			WritelineObjectResult(response);
			// sample for iterating thru JObject....
			var companies = response.ObjValue["companies"];
			var roles= response.ObjValue["roles"];
			var rolesLookup = response.ObjValue["rolesLookup"];
			foreach (var role in rolesLookup)
			{
				var roleId = role["key"];
				var roleName = role["value"];
				Console.WriteLine("Role: {0}={1}", roleId,roleName);
			}

			//Client-Context:{"signedInClientId":1,"clientId":1,"permissionClientId":1,"permissionRoleId":1,"dataLanguageId":1,"language":"en","culture":"en-gb"}
			var myContext = new ServiceClientContext(1, 1, 1, 1, 1, "en", "en-gb");
			RevitUploadServices.ServiceClientContext = myContext;

			var checkResult= await RevitUploadServices.CheckCompanyToAssignedCompaniesAsync(1,1,1,1);
			WritelineObjectResult(checkResult);
			 
			// Handle   S e c u r e C l i e n t   C o n t e x t   S e c u r e C l i e n t   C o n t e x t 
			//
			// 1. read SecureClientRole info from checkResult of previous call to CheckCompanyToAssignedCompaniesAsync()
			// 2. create ClientContext with SecureClientRole
			// 3. set ServiceClientContext
			// 4. perform all subsequent calls
			//
			// pickup secure context from result
			// var securePart = JObject.Parse(checkResult.ResponseValue).Value<string>("secureClientRolePart");
			var securePart = checkResult.ObjValue["secureClientRolePart"].ToString();
			myContext = new ServiceClientContext(securePart, 1, "en", "en-gb");
			RevitUploadServices.ServiceClientContext = myContext;


			var projectWithModels = await RevitUploadServices.GetRevitModelsFromProjectsAsync();
			WritelineArrayResult(projectWithModels);

			//Client-Context:{"signedInClientId":1,"clientId":1,"permissionClientId":1,"permissionRoleId":1,"dataLanguageId":1,"language":"en","culture":"en-gb"}
			var uploadUuid = await RevitUploadServices.UploadModelBeginAsync(1);
			Console.WriteLine(uploadUuid.ResponseValue);


			var uploadResult = await RevitUploadServices.UploadModelDataAsync(uploadUuid.ResponseValue, 1, "dfgdfsdgssfj");
			Console.WriteLine(uploadUuid.ResponseValue);

			var endResult = await RevitUploadServices.UploadModelEndAsync(uploadUuid.ResponseValue, false);
			Console.WriteLine(endResult.ResponseValue);

			var progressResult = await RevitUploadServices.UploadModelProgressAsync(uploadUuid.ResponseValue);
			Console.WriteLine(progressResult.ResponseValue);

		}

	}
}