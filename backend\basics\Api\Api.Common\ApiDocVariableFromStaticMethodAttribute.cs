﻿/*
 * $Id: ApiDocVariableFromStaticMethodAttribute.cs 576980 2020-02-21 11:15:08Z haagf $
 * Copyright (c) RIB Software SE
 */

using System;
using System.Globalization;
using System.Reflection;

namespace RIB.Visual.Basics.Api.Common
{
	/// <summary>
	/// Declares a variable and retrieves its value from a static method for use in API documentation comments.
	/// </summary>
	[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true, Inherited = false)]
	public sealed class ApiDocVariableFromStaticMethodAttribute : ApiDocVariableAttributeBase
	{
		/// <summary>
		/// Initializes a new instance.
		/// </summary>
		/// <param name="name">The identifying name of the variable.</param>
		/// <param name="methodName">The name of the static method.</param>
		/// <exception cref="ArgumentNullException">Any of the arguments is <see langword="null"/>.</exception>
		public ApiDocVariableFromStaticMethodAttribute(String name, String methodName)
			: base(name)
		{
			if (methodName == null)
			{
				throw new ArgumentNullException("methodName");
			}

			MethodName = methodName;
		}

		/// <summary>
		/// The value of the variable.
		/// </summary>
		public String MethodName { get; private set; }

		/// <summary>
		/// Expands the variable based on the given context object.
		/// </summary>
		/// <param name="context">The context object.</param>
		/// <returns>The expanded value of the variable.</returns>
		/// <exception cref="ArgumentNullException"><paramref name="context"/> is <see langword="null"/>.</exception>
		public override String Expand(VariableExpansionContext context)
		{
			if (context == null)
			{
				throw new ArgumentNullException("context");
			}

			var method = context.Type.GetMethod(MethodName,
				BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.FlattenHierarchy);
			if (method == null)
			{
				throw new InvalidOperationException(String.Format(CultureInfo.InvariantCulture,
					"Unable to find method {0} on type {1}.",
					MethodName, context.Type));
			}

			if (method.GetParameters().Length > 0)
			{
				throw new InvalidOperationException(String.Format(CultureInfo.InvariantCulture,
					"Method {0} on type {2} is expected to be parameterless, but has {1} parameter(s).",
					method.Name, method.GetParameters().Length, context.Type));
			}

			return (method.Invoke(null, new Object[0]) ?? new Object()).ToString();
		}
	}
}
