//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using RIB.Visual.Basics.AuditTrail.BusinessComponents.Logic;
//using Microsoft.VisualStudio.TestTools.UnitTesting;
//using RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi;
//using RIB.Visual.Platform.ServiceFacade.WebApi;
//using RIB.Visual.Basics.AuditTrail.BusinessComponents;

//namespace RIB.Visual.Basics.AuditTrail.UnitTests
//{
//	[TestClass()]
//	public class BasicsAuditTrailLogicTests
//	{

//		private TestContext testContextInstance;

//		/// <summary>
//		///Gets or sets the test context which provides
//		///information about and functionality for the current test run.
//		///</summary>
//		public TestContext TestContext
//		{
//			get { return testContextInstance; }
//			set { testContextInstance = value; }
//		}

//		#region Additional test attributes
//		// 
//		//You can use the following additional attributes as you write your tests:
//		//
//		//Use ClassInitialize to run code before running the first test in the class
//		[ClassInitialize()]
//		public static void MyClassInitialize(TestContext testContext)
//		{
//			TestCommon.InitializeSelfHosting();
//			TestCommon.InitializeForServerConnection();

//		}

//		#endregion


//		[TestMethod()]
//		public void GetListTest()
//		{

//			var logic = new BasicsAuditTrailLogic();

//			AudFilterEntity filter = new AudFilterEntity();
//			filter.RecordFk = 2062;
//			filter.DateFrom = DateTime.Now.AddYears(-1);
//			filter.DateTo = filter.DateFrom.AddYears(2);
//			filter.ContainerList = new string[] { "75dcd826c28746bf9b8bbbf80a1168e8", "E48C866C714440F08A1047977E84481F" };
//			// filter.ColumnList = new string[] { "75dcd826c28746bf9b8bbbf80a1168e8", "E48C866C714440F08A1047977E84481F" };
//			filter.Action = "A";
//			// filter.LogOnNameContains = "sa";

//			var result = logic.GetList(filter);

//			Assert.IsNotNull(result);
//		}

//		[TestMethod()]
//		public void GetSchemaTest()
//		{

//			var logic = new DefaultController();

//			var schemaList = new SchemaRequestDto[1]
//			{
//				new SchemaRequestDto()
//				{
//					AssemblyName = "RIB.Visual.Basics.AuditTrail.BusinessComponents",
//					ModuleSubModule = null,
//					TypeName = "LogEntity"
//				}
//			};
//			var schemaList1 = new SchemaRequestDto[1]
//			{
//				new SchemaRequestDto()
//				{
//					AssemblyName = "RIB.Visual.Basics.AuditTrail.BusinessComponents",
//					ModuleSubModule = null,
//					TypeName = "RIB.Visual.Basics.AuditTrail.BusinessComponents.LogEntity"
//				}
//			};

//			var res2 = logic.GetSchema(schemaList1);
//			var res1 = logic.GetSchema(schemaList);


//			//Assert.IsNotNull(result);
//		}
	
//	}
//}
