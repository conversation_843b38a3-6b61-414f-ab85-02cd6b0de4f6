﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35919.96 d17.13
MinimumVisualStudioVersion = 16
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Main", "Main", "{BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Foundation", "Foundation", "{99DF2B87-942B-456C-8065-19D82BFD4B66}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Service Layer", "Service Layer", "{ECB189F5-611B-463C-87E5-384B127E9BB4}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Service Facades", "Service Facades", "{14439C06-8163-48A3-97E8-5541F28E465A}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Business Layer", "Business Layer", "{DC8CA2C8-CB16-4927-9D35-3207505075D9}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTests", "UnitTests", "{C35FCB33-C320-4750-8E23-C28CEF8E57F2}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{ED4B0F69-FAA0-4CF5-82FD-DA161441434A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Awp.Main.Core", "Main.Core\RIB.Visual.Awp.Main.Core.csproj", "{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Awp.Main.BusinessComponents", "Main.BusinessComponents\RIB.Visual.Awp.Main.BusinessComponents.csproj", "{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Awp.Main.Common", "Main.Common\RIB.Visual.Awp.Main.Common.csproj", "{7DAC796E-F383-4330-97A9-840454663121}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Awp.Main.UnitTests", "Main.UnitTests\RIB.Visual.Awp.Main.UnitTests.csproj", "{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Awp.Main.Localization", "Main.Localization\RIB.Visual.Awp.Main.Localization.csproj", "{86A7C400-52BC-4558-8467-F44D7D5168E5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Awp.Main.ServiceFacade.WebApi", "Main.ServiceFacade.WebApi\RIB.Visual.Awp.Main.ServiceFacade.WebApi.csproj", "{64920861-E0EC-4A3C-86D9-AC10695020B0}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}.Release|Any CPU.Build.0 = Release|Any CPU
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}.Release|Any CPU.Build.0 = Release|Any CPU
		{7DAC796E-F383-4330-97A9-840454663121}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7DAC796E-F383-4330-97A9-840454663121}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7DAC796E-F383-4330-97A9-840454663121}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7DAC796E-F383-4330-97A9-840454663121}.Release|Any CPU.Build.0 = Release|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Release|Any CPU.Build.0 = Release|Any CPU
		{86A7C400-52BC-4558-8467-F44D7D5168E5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{86A7C400-52BC-4558-8467-F44D7D5168E5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{86A7C400-52BC-4558-8467-F44D7D5168E5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{86A7C400-52BC-4558-8467-F44D7D5168E5}.Release|Any CPU.Build.0 = Release|Any CPU
		{64920861-E0EC-4A3C-86D9-AC10695020B0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{64920861-E0EC-4A3C-86D9-AC10695020B0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{64920861-E0EC-4A3C-86D9-AC10695020B0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{64920861-E0EC-4A3C-86D9-AC10695020B0}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{99DF2B87-942B-456C-8065-19D82BFD4B66} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{ECB189F5-611B-463C-87E5-384B127E9BB4} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{14439C06-8163-48A3-97E8-5541F28E465A} = {ECB189F5-611B-463C-87E5-384B127E9BB4}
		{DC8CA2C8-CB16-4927-9D35-3207505075D9} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{C35FCB33-C320-4750-8E23-C28CEF8E57F2} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D} = {99DF2B87-942B-456C-8065-19D82BFD4B66}
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40} = {DC8CA2C8-CB16-4927-9D35-3207505075D9}
		{7DAC796E-F383-4330-97A9-840454663121} = {99DF2B87-942B-456C-8065-19D82BFD4B66}
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E} = {C35FCB33-C320-4750-8E23-C28CEF8E57F2}
		{86A7C400-52BC-4558-8467-F44D7D5168E5} = {99DF2B87-942B-456C-8065-19D82BFD4B66}
		{64920861-E0EC-4A3C-86D9-AC10695020B0} = {14439C06-8163-48A3-97E8-5541F28E465A}
	EndGlobalSection
	GlobalSection(SolutionConfigurationMains) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|ARM = Debug|ARM
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|ARM = Release|ARM
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationMains) = postSolution
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|Any CPU.Build.0 = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|ARM.ActiveCfg = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|x64.ActiveCfg = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|x86.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|ARM.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|x64.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|x86.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|ARM.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|x64.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|x86.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|x64.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|x86.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|Any CPU.Build.0 = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|ARM.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|x64.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|x86.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|ARM.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|x64.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|x86.ActiveCfg = Release|Any CPU
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}.Release|Any CPU.Build.0 = Release|Any CPU
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}.Release|ARM.ActiveCfg = Release|Any CPU
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}.Release|x64.ActiveCfg = Release|Any CPU
		{CEED469F-EEF6-4BEF-9AF7-D4C61CE02E6D}.Release|x86.ActiveCfg = Release|Any CPU
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}.Debug|x64.ActiveCfg = Debug|Any CPU
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}.Release|Any CPU.Build.0 = Release|Any CPU
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}.Release|ARM.ActiveCfg = Release|Any CPU
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}.Release|x64.ActiveCfg = Release|Any CPU
		{1951E2DF-007A-4613-ACB0-1D5C5B25EC40}.Release|x86.ActiveCfg = Release|Any CPU
		{7DAC796E-F383-4330-97A9-840454663121}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7DAC796E-F383-4330-97A9-840454663121}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7DAC796E-F383-4330-97A9-840454663121}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{7DAC796E-F383-4330-97A9-840454663121}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7DAC796E-F383-4330-97A9-840454663121}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7DAC796E-F383-4330-97A9-840454663121}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7DAC796E-F383-4330-97A9-840454663121}.Release|Any CPU.Build.0 = Release|Any CPU
		{7DAC796E-F383-4330-97A9-840454663121}.Release|ARM.ActiveCfg = Release|Any CPU
		{7DAC796E-F383-4330-97A9-840454663121}.Release|x64.ActiveCfg = Release|Any CPU
		{7DAC796E-F383-4330-97A9-840454663121}.Release|x86.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|Any CPU.Build.0 = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|ARM.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|x64.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|x86.ActiveCfg = Release|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Release|Any CPU.Build.0 = Release|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Release|ARM.ActiveCfg = Release|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Release|x64.ActiveCfg = Release|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Release|x86.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|ARM.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|x64.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|x86.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|Any CPU.Build.0 = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|ARM.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|x64.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|x86.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|ARM.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|x64.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|x86.ActiveCfg = Release|Any CPU
		{86A7C400-52BC-4558-8467-F44D7D5168E5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{86A7C400-52BC-4558-8467-F44D7D5168E5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{86A7C400-52BC-4558-8467-F44D7D5168E5}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{86A7C400-52BC-4558-8467-F44D7D5168E5}.Debug|x64.ActiveCfg = Debug|Any CPU
		{86A7C400-52BC-4558-8467-F44D7D5168E5}.Debug|x86.ActiveCfg = Debug|Any CPU
		{86A7C400-52BC-4558-8467-F44D7D5168E5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{86A7C400-52BC-4558-8467-F44D7D5168E5}.Release|Any CPU.Build.0 = Release|Any CPU
		{86A7C400-52BC-4558-8467-F44D7D5168E5}.Release|ARM.ActiveCfg = Release|Any CPU
		{86A7C400-52BC-4558-8467-F44D7D5168E5}.Release|x64.ActiveCfg = Release|Any CPU
		{86A7C400-52BC-4558-8467-F44D7D5168E5}.Release|x86.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|Any CPU.Build.0 = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|ARM.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|x64.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|x86.ActiveCfg = Release|Any CPU
		{64920861-E0EC-4A3C-86D9-AC10695020B0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{64920861-E0EC-4A3C-86D9-AC10695020B0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{64920861-E0EC-4A3C-86D9-AC10695020B0}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{64920861-E0EC-4A3C-86D9-AC10695020B0}.Debug|x64.ActiveCfg = Debug|Any CPU
		{64920861-E0EC-4A3C-86D9-AC10695020B0}.Debug|x86.ActiveCfg = Debug|Any CPU
		{64920861-E0EC-4A3C-86D9-AC10695020B0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{64920861-E0EC-4A3C-86D9-AC10695020B0}.Release|Any CPU.Build.0 = Release|Any CPU
		{64920861-E0EC-4A3C-86D9-AC10695020B0}.Release|ARM.ActiveCfg = Release|Any CPU
		{64920861-E0EC-4A3C-86D9-AC10695020B0}.Release|x64.ActiveCfg = Release|Any CPU
		{64920861-E0EC-4A3C-86D9-AC10695020B0}.Release|x86.ActiveCfg = Release|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Release|Any CPU.Build.0 = Release|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Release|ARM.ActiveCfg = Release|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Release|x64.ActiveCfg = Release|Any CPU
		{50A79112-0318-432F-B66E-43BC1B9CBC7A}.Release|x86.ActiveCfg = Release|Any CPU
	EndGlobalSection
	GlobalSection(TestCaseManagementSettings) = postSolution
		CategoryFile = RIB.Visual.Awp.Main.vsmdi
	EndGlobalSection
	GlobalSection(DPCodeReviewSolutionGUID) = preSolution
		DPCodeReviewSolutionGUID = {00000000-0000-0000-0000-000000000000}
	EndGlobalSection
	GlobalSection(TextTemplating) = postSolution
		TextTemplating = 1
	EndGlobalSection
EndGlobal
