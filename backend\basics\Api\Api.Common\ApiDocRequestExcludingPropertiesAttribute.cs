﻿
using System;
using System.Linq;

namespace RIB.Visual.Basics.Api.Common
{

	/// <summary>
	/// Declares a variable with a specified value for use in API documentation comments.
	/// </summary>
	[AttributeUsage(AttributeTargets.Class, AllowMultiple = true, Inherited = false)]
	public sealed class ApiDocRequestExcludingPropertiesAttribute : ApiDocExcludingPropertiesAttributeBase
	{
		/// <summary>
		/// Initializes a new instance.
		/// </summary>
		/// <param name="actionName">The name of the action that will be effected.</param>
		/// <param name="excludingProperties">The properties of dto that will be excluded in API documentation comment.</param>
		/// <param name="parameterName">The parameter name of the variable that will be effected.</param>
		/// <exception cref="ArgumentNullException">Any of the arguments is <see langword="null"/>.</exception>
		public ApiDocRequestExcludingPropertiesAttribute(String actionName, String[] excludingProperties, String parameterName = null)
			: base(actionName, excludingProperties)
		{
			ParameterName = parameterName;
		}

		/// <summary>
		/// Initializes a new instance.
		/// </summary>
		/// <param name="actionName">The name of the action that will be effected.</param>
		/// <param name="excludingProperties">The properties of dto that will be excluded in API documentation comment, split by ','.</param>
		/// <param name="parameterName">The parameter name of the variable that will be effected.</param>
		/// <exception cref="ArgumentNullException">Any of the arguments is <see langword="null"/>.</exception>
		public ApiDocRequestExcludingPropertiesAttribute(String actionName, String excludingProperties, String parameterName = null)
			: base(actionName, excludingProperties)
		{
			ParameterName = parameterName;
		}

		/// <summary>
		/// The parameter name of the variable that will be effected.
		/// </summary>
		public String ParameterName { get; private set; }
	}
}
