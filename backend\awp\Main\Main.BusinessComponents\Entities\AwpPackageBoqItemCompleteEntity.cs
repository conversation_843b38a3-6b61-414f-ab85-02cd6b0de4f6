﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.Common.BusinessComponents;
using static RIB.Visual.Boq.Main.BusinessComponents.BoqItemAwpLogic;

namespace RIB.Visual.Awp.Main.BusinessComponents
{
    /// <summary>
    /// 
    /// </summary>
	public class AwpPackageBoqItemCompleteEntity : BaseCompleteEntity
    {
        /// <summary>
        /// Gets or sets the package BOQ item.
        /// </summary>
        public AwpPackageBoqItemEntity ServicePackages { get; set; }
    }
}
