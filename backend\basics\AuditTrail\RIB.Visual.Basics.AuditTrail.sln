﻿Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.32106.194
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "AuditTrail", "AuditTrail", "{BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Foundation", "Foundation", "{99DF2B87-942B-456C-8065-19D82BFD4B66}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Presentation Layer", "Presentation Layer", "{5A68D1F9-923B-420E-B05A-192EAEFCBEED}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Client", "Client", "{7910B591-73C9-4033-8431-70E5D3527B76}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Service Layer", "Service Layer", "{ECB189F5-611B-463C-87E5-384B127E9BB4}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Service Facades", "Service Facades", "{14439C06-8163-48A3-97E8-5541F28E465A}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Business Layer", "Business Layer", "{DC8CA2C8-CB16-4927-9D35-3207505075D9}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTests", "UnitTests", "{C35FCB33-C320-4750-8E23-C28CEF8E57F2}"
	ProjectSection(FolderStartupServices) = postProject
		{B4F97281-0DBD-4835-9ED8-7DFB966E87FF} = {B4F97281-0DBD-4835-9ED8-7DFB966E87FF}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{ED4B0F69-FAA0-4CF5-82FD-DA161441434A}"
	ProjectSection(SolutionItems) = preProject
		RIB.Visual.Basics.AuditTrail.vsmdi = RIB.Visual.Basics.AuditTrail.vsmdi
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.AuditTrail.Core", "AuditTrail.Core\RIB.Visual.Basics.AuditTrail.Core.csproj", "{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.AuditTrail.BusinessComponents", "AuditTrail.BusinessComponents\RIB.Visual.Basics.AuditTrail.BusinessComponents.csproj", "{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.AuditTrail.Common", "AuditTrail.Common\RIB.Visual.Basics.AuditTrail.Common.csproj", "{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.AuditTrail.Localization", "AuditTrail.Localization\RIB.Visual.Basics.AuditTrail.Localization.csproj", "{374FBE7A-25B1-42F2-8FFE-C276BF617A17}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi", "AuditTrail.ServiceFacade.WebApi\RIB.Visual.Basics.AuditTrail.ServiceFacade.WebApi.csproj", "{DF399B45-DDD6-412F-83FE-265DB568001F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}.Release|Any CPU.Build.0 = Release|Any CPU
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}.Release|Any CPU.Build.0 = Release|Any CPU
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}.Release|Any CPU.Build.0 = Release|Any CPU
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF399B45-DDD6-412F-83FE-265DB568001F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF399B45-DDD6-412F-83FE-265DB568001F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF399B45-DDD6-412F-83FE-265DB568001F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF399B45-DDD6-412F-83FE-265DB568001F}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{99DF2B87-942B-456C-8065-19D82BFD4B66} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{5A68D1F9-923B-420E-B05A-192EAEFCBEED} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{7910B591-73C9-4033-8431-70E5D3527B76} = {5A68D1F9-923B-420E-B05A-192EAEFCBEED}
		{ECB189F5-611B-463C-87E5-384B127E9BB4} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{14439C06-8163-48A3-97E8-5541F28E465A} = {ECB189F5-611B-463C-87E5-384B127E9BB4}
		{DC8CA2C8-CB16-4927-9D35-3207505075D9} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{C35FCB33-C320-4750-8E23-C28CEF8E57F2} = {BF4C49EC-5196-42A5-9EE0-BD5CBDF93DF8}
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB} = {99DF2B87-942B-456C-8065-19D82BFD4B66}
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4} = {DC8CA2C8-CB16-4927-9D35-3207505075D9}
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2} = {99DF2B87-942B-456C-8065-19D82BFD4B66}
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17} = {99DF2B87-942B-456C-8065-19D82BFD4B66}
		{DF399B45-DDD6-412F-83FE-265DB568001F} = {14439C06-8163-48A3-97E8-5541F28E465A}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0D27C579-F6A3-40DF-A5C1-C7B63B4A44CC}
	EndGlobalSection
	GlobalSection(SolutionConfigurationAuditTrails) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|ARM = Debug|ARM
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|ARM = Release|ARM
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationAuditTrails) = postSolution
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|Any CPU.Build.0 = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|ARM.ActiveCfg = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|x64.ActiveCfg = Release|Any CPU
		{F19CE8A9-B63A-4CFC-A957-E10D31019B41}.Release|x86.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|ARM.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|x64.ActiveCfg = Release|Any CPU
		{EE6ECF6D-83A9-42C4-86ED-C518D03471BB}.Release|x86.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|ARM.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|x64.ActiveCfg = Release|Any CPU
		{CFBA18D6-5BE3-40F9-A908-D6AAA3CA21A8}.Release|x86.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|x64.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Debug|x86.ActiveCfg = Debug|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|Any CPU.Build.0 = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|ARM.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|x64.ActiveCfg = Release|Any CPU
		{38213779-9E55-4457-A766-318427AA7089}.Release|x86.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|ARM.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|x64.ActiveCfg = Release|Any CPU
		{A43F9088-796E-4F04-A6E0-D55639AE699C}.Release|x86.ActiveCfg = Release|Any CPU
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}.Release|Any CPU.Build.0 = Release|Any CPU
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}.Release|ARM.ActiveCfg = Release|Any CPU
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}.Release|x64.ActiveCfg = Release|Any CPU
		{401B66C0-CD71-4BA6-B900-87D17EA8FEAB}.Release|x86.ActiveCfg = Release|Any CPU
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}.Release|Any CPU.Build.0 = Release|Any CPU
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}.Release|ARM.ActiveCfg = Release|Any CPU
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}.Release|x64.ActiveCfg = Release|Any CPU
		{8EDF8F88-5E5E-404D-A67D-2CF906DB6EC4}.Release|x86.ActiveCfg = Release|Any CPU
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}.Release|Any CPU.Build.0 = Release|Any CPU
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}.Release|ARM.ActiveCfg = Release|Any CPU
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}.Release|x64.ActiveCfg = Release|Any CPU
		{1420A2DE-2BEE-4B02-81D3-82AF4F1B35B2}.Release|x86.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|Any CPU.Build.0 = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|ARM.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|x64.ActiveCfg = Release|Any CPU
		{C5EB1A2C-23D2-47EC-8DAE-DA74483E30CB}.Release|x86.ActiveCfg = Release|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Release|Any CPU.Build.0 = Release|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Release|ARM.ActiveCfg = Release|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Release|x64.ActiveCfg = Release|Any CPU
		{DD41E39F-29B4-4E47-9BBE-3AAFB8EBA54E}.Release|x86.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|ARM.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|x64.ActiveCfg = Release|Any CPU
		{A3E891A4-B40C-4544-9CCD-7F726A55A132}.Release|x86.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|Any CPU.Build.0 = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|ARM.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|x64.ActiveCfg = Release|Any CPU
		{6447B2B5-1F1E-4146-9987-A5231CCCDEF2}.Release|x86.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|ARM.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|x64.ActiveCfg = Release|Any CPU
		{C0666410-35C9-467E-B666-760CD03300A8}.Release|x86.ActiveCfg = Release|Any CPU
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17}.Debug|x64.ActiveCfg = Debug|Any CPU
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17}.Debug|x86.ActiveCfg = Debug|Any CPU
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17}.Release|Any CPU.Build.0 = Release|Any CPU
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17}.Release|ARM.ActiveCfg = Release|Any CPU
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17}.Release|x64.ActiveCfg = Release|Any CPU
		{374FBE7A-25B1-42F2-8FFE-C276BF617A17}.Release|x86.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|Any CPU.Build.0 = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|ARM.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|x64.ActiveCfg = Release|Any CPU
		{00BFBCC1-344F-44DD-8FD5-6DB0D55E6C4D}.Release|x86.ActiveCfg = Release|Any CPU
		{DF399B45-DDD6-412F-83FE-265DB568001F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF399B45-DDD6-412F-83FE-265DB568001F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF399B45-DDD6-412F-83FE-265DB568001F}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{DF399B45-DDD6-412F-83FE-265DB568001F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DF399B45-DDD6-412F-83FE-265DB568001F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DF399B45-DDD6-412F-83FE-265DB568001F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF399B45-DDD6-412F-83FE-265DB568001F}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF399B45-DDD6-412F-83FE-265DB568001F}.Release|ARM.ActiveCfg = Release|Any CPU
		{DF399B45-DDD6-412F-83FE-265DB568001F}.Release|x64.ActiveCfg = Release|Any CPU
		{DF399B45-DDD6-412F-83FE-265DB568001F}.Release|x86.ActiveCfg = Release|Any CPU
	EndGlobalSection
	GlobalSection(TestCaseManagementSettings) = postSolution
		CategoryFile = RIB.Visual.Basics.AuditTrail.vsmdi
	EndGlobalSection
	GlobalSection(DPCodeReviewSolutionGUID) = preSolution
		DPCodeReviewSolutionGUID = {00000000-0000-0000-0000-000000000000}
	EndGlobalSection
	GlobalSection(TextTemplating) = postSolution
		TextTemplating = 1
	EndGlobalSection
EndGlobal
