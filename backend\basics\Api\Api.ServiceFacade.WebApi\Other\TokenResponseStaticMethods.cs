﻿using IdentityModel.Client;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace RIB.Visual.Basics.Api.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public static class TokenResponseStaticMethods
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="httpStatusCode"></param>
		/// <param name="message"></param>
		/// <returns></returns>
		public static TokenResponse CreateTokenResponse(HttpStatusCode httpStatusCode, string message)
		{
			var httpResponseMessage = new HttpResponseMessage(httpStatusCode)
			{
				Content = new StringContent(JsonConvert.SerializeObject(message))
			};
			return TokenResponse.FromHttpResponseAsync<TokenResponse>(httpResponseMessage).Result;
		}
	}
}
